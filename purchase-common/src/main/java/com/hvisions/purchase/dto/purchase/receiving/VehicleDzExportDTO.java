package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 丢糟车辆导出dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "丢糟车辆导出dto")
public class VehicleDzExportDTO {

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "收货单位")
    private String receiptPlace;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "入场时间")
    private Date admissionTime;

    @ApiModelProperty(value = "出场时间")
    private Date appearanceTime;

    @ApiModelProperty(value = "毛重(kg)")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "皮重(kg)")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "最终净重(kg)")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


}
