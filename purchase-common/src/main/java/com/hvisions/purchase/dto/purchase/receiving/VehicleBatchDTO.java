package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 车辆批次信息
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "车辆批次信息")
public class VehicleBatchDTO {

    @ApiModelProperty(value = "送货单id")
    private Integer id;

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "检验单ids")
    private String inspectionIds;

    @ApiModelProperty(value = "检验单号")
    private String inspectionOrders;

    @ApiModelProperty(value = "样品编码")
    private String samplingCodes;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;


    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "最终净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "出厂时间")
    private Date appearanceTime;



}
