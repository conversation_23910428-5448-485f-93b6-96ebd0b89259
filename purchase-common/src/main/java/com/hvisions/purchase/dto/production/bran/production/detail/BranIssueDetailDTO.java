package com.hvisions.purchase.dto.production.bran.production.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 定时任务获取熟糠发放信息dto
 * @author: hhz
 * @time: 2023/6/26 11:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "定时任务获取熟糠发放信息dto")
public class BranIssueDetailDTO {

    @ApiModelProperty(value = "中心编码")
    private String location;

    @ApiModelProperty(value = "进料重量")
    private float feedWeight;

    @ApiModelProperty(value = "发放仓Id")
    private Integer sendSiloId;

    @ApiModelProperty(value = "点位")
    private Integer point;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date postingTime;

}
