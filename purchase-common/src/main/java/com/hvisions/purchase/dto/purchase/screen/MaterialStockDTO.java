package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 物料库存数量统计
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "物料库存数量统计")
public class MaterialStockDTO {

    @ApiModelProperty(value = "物料类别编码")
    private String materialTypeCode;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQuantity;

}
