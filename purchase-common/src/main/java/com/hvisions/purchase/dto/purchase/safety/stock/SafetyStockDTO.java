package com.hvisions.purchase.dto.purchase.safety.stock;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 计划安全库存新增修改dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "计划安全库存新增修改dto")
public class SafetyStockDTO extends SysBaseDTO {

    @ApiModelProperty(value = "物料id")
    @NotNull(message = "物料id不能为空")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    @NotNull(message = "物料编码不能为空")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    @NotNull(message = "物料名称不能为空")
    private String materialName;

    @ApiModelProperty(value = "物料单位")
    private String materialUom;

    @ApiModelProperty(value = "安全库存值")
    private BigDecimal safetyValue;


}
