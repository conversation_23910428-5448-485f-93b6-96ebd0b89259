package com.hvisions.purchase.dto.production.husk.Inspection.item;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 蒸糠工艺巡检项目新增or修改Dto
 * @author: Jcao
 * @time: 2022/5/13 13:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠工艺巡检项目新增or修改Dto")
public class HuskInspectionItemDTO extends SysBaseDTO {

    @ApiModelProperty(value = "巡检项目编码")
    private String code;

    @ApiModelProperty(value = "巡检项目名称")
    private String name;

    @ApiModelProperty(value = "巡检标准要求")
    private String standard;

    @ApiModelProperty(value = "是否生效;0-失效、1-生效")
    private String effectState;

    @ApiModelProperty(value = "备注")
    private String remark;

}
