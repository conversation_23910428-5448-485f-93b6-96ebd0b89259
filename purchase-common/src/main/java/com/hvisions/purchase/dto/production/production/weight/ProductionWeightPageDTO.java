package com.hvisions.purchase.dto.production.production.weight;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 生产过磅管理分页查询
 * @author: Jcao
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "生产过磅管理分页查询")
public class ProductionWeightPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "过磅单号")
    private String weightOrder;

    @ApiModelProperty(value = "基地id")
    private Integer productionBaseId;

    @ApiModelProperty(value = "基地")
    private String productionBase;

    @ApiModelProperty(value = "过磅日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date weightDate;

    @ApiModelProperty(value = "源单号")
    private String sourceOrder;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "过磅类型;1、采购收货预处理杂质过磅，2、转运处理杂质过磅，3、不合格重蒸出库过磅，4、不合格退货出库过磅，5、重蒸预处理杂质过磅")
    private String type;

    @ApiModelProperty(value = "毛重")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "皮重")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "首次称重时间")
    private Date admissionTime;

    @ApiModelProperty(value = "二次称重时间")
    private Date appearanceTime;

    @ApiModelProperty(value = "发货单位")
    private String deliveryPart;

    @ApiModelProperty(value = "收货单位")
    private String receivePart;

    @ApiModelProperty(value = "状态;0-新增、1-已下发、2-一次已过磅、3-二次已过磅、4-关闭")
    private String state;

}
