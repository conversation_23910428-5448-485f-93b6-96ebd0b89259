package com.hvisions.purchase.dto.production.production;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 生产过磅称重数据
 * @author: Jcao
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "生产过磅称重数据")
public class ProductionWeightDataDTO {

    @ApiModelProperty(value = "过磅任务id")
    private Integer id;

    @ApiModelProperty(value = "源单号")
    private String sourceOrder;

    @ApiModelProperty(value = "毛重（入场重量）")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "出场重量（皮重）")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "类型：入厂:0，出厂:1")
    private String type;

}
