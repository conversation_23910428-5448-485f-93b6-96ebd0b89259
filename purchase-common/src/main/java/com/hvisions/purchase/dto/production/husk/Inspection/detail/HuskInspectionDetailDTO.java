package com.hvisions.purchase.dto.production.husk.Inspection.detail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 蒸糠工艺巡检项目详情Dto
 * @author: Jcao
 * @time: 2022/5/13 14:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "蒸糠工艺巡检项目详情Dto")
public class HuskInspectionDetailDTO {

    @ApiModelProperty(value = "详情id")
    private Integer id;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "质量巡检id")
    private Integer inspectionId;

    @ApiModelProperty(value = "检验项目id")
    private Integer itemId;

    @ApiModelProperty(value = "巡检项目编码")
    private String code;

    @ApiModelProperty(value = "巡检项目名称")
    private String name;

    @ApiModelProperty(value = "巡检标准要求")
    private String standard;

    @ApiModelProperty(value = "巡检顺序")
    private Integer sort;

    @ApiModelProperty(value = "检查结论")
    private String detailResult;

    @ApiModelProperty(value = "异常描述")
    private String abnormalDescription;

    @ApiModelProperty(value = "改进措施")
    private String improvementMeasures;

    @ApiModelProperty(value = "备注")
    private String remark;

}
