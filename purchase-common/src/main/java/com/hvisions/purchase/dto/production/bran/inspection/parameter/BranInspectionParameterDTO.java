package com.hvisions.purchase.dto.production.bran.inspection.parameter;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 蒸糠质量巡检参数Dto
 * @author: Jcao
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠质量巡检参数Dto")
public class BranInspectionParameterDTO extends SysBaseDTO {

    @ApiModelProperty(value = "巡检周期")
    private Integer inspectionCycle;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "文件id")
    private Integer fileId;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "基地id")
    private Integer productionBaseId;

    @ApiModelProperty(value = "基地")
    private String productionBase;

    @ApiModelProperty(value = "原辅料管理部id")
    private Integer centerId;

    @ApiModelProperty(value = "原辅料管理部")
    private String center;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "车间")
    private String location;

    @ApiModelProperty(value = "产线id")
    private Integer productionLineId;

    @ApiModelProperty(value = "产线")
    private String productionLine;

    @ApiModelProperty(value = "定时任务id")
    private Integer timerId;

    @ApiModelProperty(value = "自动生成巡检任务;0-false、1-true")
    private Boolean generatedTask;

}
