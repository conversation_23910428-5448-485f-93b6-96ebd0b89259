package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 红高粱采供报表导出dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "红高粱采供报表导出dto")
public class RedSorghumPurchaseReportExportDTO {

    @ApiModelProperty(value = "sap采购编码")
    private String sapOrder;

    @ApiModelProperty(value = "采购订单号")
    private String orderNo;

    @ApiModelProperty(value = "合同号")
    private String contractNumber;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "单价(元)")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "单价基数")
    private BigDecimal unitPriceBase;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal quantitySupplied;

    @ApiModelProperty(value = "收货数量")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "剩余数量")
    private BigDecimal remainQuantity;

    @ApiModelProperty(value = "剩余数量百分比")
    private BigDecimal remainQuantityPercentage;

    @ApiModelProperty(value = "已过账数量")
    private BigDecimal postingQuantity;

    // 送货车辆数量
    @ApiModelProperty(value = "订单供货批次数")
    private Integer totalBatch;

    // 送货车辆质检不合格数量
    @ApiModelProperty(value = "不合格批次数")
    private Integer unqualifiedBatch;

    // 存在扣重的送货车辆数
    @ApiModelProperty(value = "让步接收次数")
    private Integer buckleBatch;

    @ApiModelProperty(value = "采购订单状态;执行中-0、待审批-1，执行中-2、已关闭-3、已删除-4")
    private String state;

    @ApiModelProperty(value = "采购订单截至日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deadline;

    @ApiModelProperty(value = "采购计划编号")
    private String planCode;

}
