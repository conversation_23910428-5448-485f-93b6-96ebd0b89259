package com.hvisions.purchase.dto.production.change.shift;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 交接班交接操作
 * @author: Jcao
 * @time: 2022/4/27 18:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "交接班交接操作")
public class ChangeShiftHandoverDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "类型：0、交班，1、接班")
    private Integer type;

    @ApiModelProperty(value = "操作人id")
    private Integer operationId;

    @ApiModelProperty(value = "操作人名称")
    private String operationName;

}
