package com.hvisions.purchase.dto.production.machining.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 预处理加工工单分页返回dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "预处理加工工单分页返回dto")
public class MachiningOrderPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "工单号")
    private String orderNumber;

    @ApiModelProperty(value = "工单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "入库仓库id")
    private Integer warehouseId;

    @ApiModelProperty(value = "入库仓库")
    private String warehouse;

    @ApiModelProperty(value = "入库筒仓号id(库区)")
    private Integer siloId;

    @ApiModelProperty(value = "入库筒仓号(库区)")
    private String silo;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料单位")
    private String unit;

    @ApiModelProperty(value = "预处理重量")
    private BigDecimal machiningWeight;

    @ApiModelProperty(value = "预处理扣重")
    private BigDecimal machiningBuckleWeight;

    @ApiModelProperty(value = "实际入仓数量")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "卸货记录类型：1-高粱稻壳、2-小麦")
    private String type;

    @ApiModelProperty(value = "小麦认证批次")
    private String wheatCertificateBatch;
}
