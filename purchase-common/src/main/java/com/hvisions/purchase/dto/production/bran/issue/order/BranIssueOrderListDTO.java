package com.hvisions.purchase.dto.production.bran.issue.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 熟糠发放工单集合dto
 * @author: yyy
 * @time: 2022/4/26 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "熟糠发放工单集合dto")
public class BranIssueOrderListDTO {

    @ApiModelProperty(value = "工单id")
    private Integer id;

    @ApiModelProperty(value = "计划id")
    private Integer planId;

    @ApiModelProperty(value = "工单号")
    private String orderNo;

    @ApiModelProperty(value = "中心编码")
    private String center;

    @ApiModelProperty(value = "工单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "生产类型编码")
    private String productionTypeCode;

    @ApiModelProperty(value = "生产类型名称")
    private String productionTypeName;


    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料单位")
    private String unit;

    @ApiModelProperty(value = "物料需求数量")
    private BigDecimal requirementQuantity;

    @ApiModelProperty(value = "当前库存数量")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal planQuantity;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualQuantity;

}
