package com.hvisions.purchase.dto.purchase.purchasing.group;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 采购组分页查询
 * @author: Jcao
 * @time: 2022/4/2 9:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "采购组分页查询")
public class PurchasingGroupPageDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "采购组代码")
    private String code;

    @ApiModelProperty(value = "采购组名称")
    private String name;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createName;

}
