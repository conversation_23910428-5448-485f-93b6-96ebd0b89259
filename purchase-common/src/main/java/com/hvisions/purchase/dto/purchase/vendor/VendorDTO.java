package com.hvisions.purchase.dto.purchase.vendor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: 供应商新增修改dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "供应商新增修改dto")
public class VendorDTO {

    @ApiModelProperty(value = "供应商编码")
    @NotNull(message = "供应商编码不能为空")
    private String code;

    @ApiModelProperty(value = "供应商名称")
    @NotNull(message = "供应商名称不能为空")
    private String name;

    @ApiModelProperty(value = "供应商组名称")
    private String vendorGroup;

    @ApiModelProperty(value = "采购组织编码")
    private String organizationCode;

}
