package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 送货车辆收货冲销dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "送货车辆收货冲销dto")
public class WriteOffDTO {

    @ApiModelProperty(value = "送货计划详情id")
    @NotNull(message = "送货计划详情id不能为空")
    private Integer id;

    @ApiModelProperty(value = "冲销数量")
    @NotNull(message = "冲销数量不能为空")
    private BigDecimal postingQuantity;


}
