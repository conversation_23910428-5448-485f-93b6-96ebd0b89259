package com.hvisions.purchase.dto.production.husk.Inspection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 蒸糠工艺巡检项目查询条件Dto
 * @author: Jcao
 * @time: 2022/5/13 14:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠工艺巡检项目查询条件Dto")
public class HuskInspectionQueryDTO extends PageInfo {

    @ApiModelProperty(value = "单据号")
    private String orderNo;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "状态;0-待巡检、1-已完成")
    private String state;

}
