package com.hvisions.purchase.dto.production.change.shift;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description: 交接班分页查询
 * @author: Jcao
 * @time: 2022/4/28 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "交接班分页查询")
public class ChangeShiftPageDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "任务单号")
    private String taskOrder;

    @ApiModelProperty(value = "原辅料管理部id")
    private Integer centerId;

    @ApiModelProperty(value = "原辅料管理部")
    private String center;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "车间")
    private String location;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date changeDate;

    @ApiModelProperty(value = "班次id")
    private Integer shiftId;

    @ApiModelProperty(value = "班次")
    private String shift;

    @ApiModelProperty(value = "班组id")
    private Integer crewId;

    @ApiModelProperty(value = "班组")
    private String crew;

    @ApiModelProperty(value = "交出人id")
    private Integer giveId;

    @ApiModelProperty(value = "交出人")
    private String givePeople;

    @ApiModelProperty(value = "交出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date giveTime;

    @ApiModelProperty(value = "接收人id")
    private Integer contactId;

    @ApiModelProperty(value = "接收人")
    private String contactPeople;

    @ApiModelProperty(value = "接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contactTime;

    @ApiModelProperty(value = "状态;0-新增、1-已交出、2-已接收，3-已取消")
    private String state;

    @ApiModelProperty(value = "交接班明细")
    private List<ChangeShiftDetailListDTO> changeShiftDetails;

}
