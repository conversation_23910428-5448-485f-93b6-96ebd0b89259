package com.hvisions.purchase.dto.production.impurity.deal;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 杂质处理单新增or修改
 * @author: Jcao
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "杂质处理单新增or修改")
public class ImpurityDealDTO extends SysBaseDTO {

    @ApiModelProperty(value = "生产单位")
    private String productionPart;

    @ApiModelProperty(value = "班次id")
    private Integer shiftId;

    @ApiModelProperty(value = "班次")
    private String shift;

    @ApiModelProperty(value = "源单号")
    private String sourceOrder;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "重量")
    private BigDecimal quantity;
}
