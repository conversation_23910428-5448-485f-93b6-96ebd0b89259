package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 车辆数量
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "送货车辆数量")
public class VehicleQuantityDTO {

    @ApiModelProperty(value = "物料类别编码")
    private String materialTypeCode;

    @ApiModelProperty(value = "计划送货车数量")
    private Integer planQuantity;

    @ApiModelProperty(value = "等待入厂车数量")
    private Integer waitEnterQuantity;

    @ApiModelProperty(value = "合格车数量")
    private Integer qualifiedQuantity;

    @ApiModelProperty(value = "卸货完成车数量")
    private Integer alreadyUnloading;

}
