package com.hvisions.purchase.dto.production.production.weight;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 生产过磅新增or修改Dto
 * @author: Jcao
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "生产过磅新增or修改Dto")
public class ProductionWeightDTO extends SysBaseDTO {

    @ApiModelProperty(value = "过磅类型；1-采购收货预处理杂质过磅、2-转运处理杂质过磅、3-不合格重蒸出库过磅、4-不合格退货出库过磅、5-重蒸报废")
    private String type;

    @ApiModelProperty(value = "源单号")
    private String sourceOrder;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

}
