package com.hvisions.purchase.dto.purchase.purchasing.group;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: 采购组新增or修改
 * @author: Jcao
 * @time: 2022/4/2 9:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "采购组新增or修改")
public class PurchasingGroupDTO extends SysBaseDTO {

    @ApiModelProperty(value = "采购组代码")
    @NotNull(message = "采购组代码不能为空")
    private String code;

    @ApiModelProperty(value = "采购组名称")
    @NotNull(message = "采购组名称不能为空")
    private String name;

}
