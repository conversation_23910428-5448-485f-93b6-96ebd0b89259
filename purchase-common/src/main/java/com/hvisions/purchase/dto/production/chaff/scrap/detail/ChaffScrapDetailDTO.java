package com.hvisions.purchase.dto.production.chaff.scrap.detail;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 蒸糠报废处理详情
 * @author: Jcao
 * @time: 2022/4/28 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠报废处理详情")
public class ChaffScrapDetailDTO extends SysBaseDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "报废处理id")
    private Integer scrapId;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "称量方式;0-车间称重、1-地磅称重")
    private String weightType;

    @ApiModelProperty(value = "处理数量")
    private BigDecimal dealQuantity;

    @ApiModelProperty(value = "入场重量")
    private BigDecimal enterQuantity;

    @ApiModelProperty(value = "出厂重量")
    private BigDecimal outQuantity;

    @ApiModelProperty(value = "记录时间")
    private Date recordTime;

    @ApiModelProperty(value = "记录人员id")
    private Integer recordPeopleId;

    @ApiModelProperty(value = "记录人员")
    private String recordPeople;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "过磅任务id")
    private Integer productionWeightId;
}
