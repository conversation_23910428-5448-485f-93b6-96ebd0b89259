package com.hvisions.purchase.dto.production.sorghum.receive;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 高粱工单详情接收dto
 * @author: yyy
 * @time: 2022/8/5 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱工单详情接收dto")
public class SorghumReceiveDTO {

    @ApiModelProperty(value = "生产线号")
    private String lineIdent;

    @ApiModelProperty(value = "中心编号")
    private String centerCode;

    @ApiModelProperty(value = "任务号")
    private String jobIdent;

    @ApiModelProperty(value = "原料代码")
    private String productIdent;

    @ApiModelProperty(value = "原料名称")
    private String productName;

    @ApiModelProperty(value = "发送仓号（H01,H02）")
    private String senderStorageIdent;

    @ApiModelProperty(value = "接收仓号（G04,G05）")
    private String receiverStorageIdent;

    @ApiModelProperty(value = "各发送仓发送原料重量（示例：3500,3600），重量单位都是KG")
    private String sendStorageWeightActual;

    @ApiModelProperty(value = "各接收仓接收原料重量（示例：3500,3600），重量单位都是KG")
    private String receiverStorageWeightActual;

    @ApiModelProperty(value = "任务开始日期（示例：2023/2/10 19:42:01）")
    private Date startDateActual;

    @ApiModelProperty(value = "任务结束日期（示例：2023/2/10 23:10:05）")
    private Date endDateActual;
}
