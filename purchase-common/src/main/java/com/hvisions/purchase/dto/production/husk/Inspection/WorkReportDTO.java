package com.hvisions.purchase.dto.production.husk.Inspection;

import com.hvisions.purchase.dto.SysBaseDTO;
import com.hvisions.purchase.dto.production.husk.Inspection.detail.HuskInspectionDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 工艺巡检填报Dto
 * @author: Jcao
 * @time: 2022/5/13 10:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "工艺巡检填报Dto")
public class WorkReportDTO extends SysBaseDTO {

    @ApiModelProperty(value = "检查详情")
    private List<HuskInspectionDetailDTO> details;

    @ApiModelProperty(value = "巡检人员id")
    private Integer inspectionPeopleId;

    @ApiModelProperty(value = "巡检人员")
    private String inspectionPeople;

    @ApiModelProperty(value = "检查结论;0-异常、1-正常")
    private String inspectionResult;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "图片id")
    private List<String> imageIds;

}
