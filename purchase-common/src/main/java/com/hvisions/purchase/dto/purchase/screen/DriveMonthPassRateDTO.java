package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 当月采购合格率
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "当月采购合格率")
public class DriveMonthPassRateDTO {

    @ApiModelProperty(value = "日期集合")
    private List<String> dates;

    @ApiModelProperty(value = "高粱比例集合")
    private List<BigDecimal> glRate;

    @ApiModelProperty(value = "稻壳比例集合")
    private List<BigDecimal> dkRate;
}
