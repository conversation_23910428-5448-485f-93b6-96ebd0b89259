package com.hvisions.purchase.dto.purchase.screen;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 执行中的采购订单查询dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "执行中的采购订单查询dto")
public class PurchaseOrderExecuteQueryDTO extends PageInfo {

    @ApiModelProperty(value = "物料类型编码")
    private List<String> materialTypeCodeList;
}
