package com.hvisions.purchase.dto.production.loss;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 损耗同步dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "损耗同步dto")
public class SyncLossDTO {

    @ApiModelProperty(value = "损耗单id")
    private Integer id;

    @ApiModelProperty(value = "损耗数量")
    private BigDecimal lossQuantity;

    @ApiModelProperty(value = "移动类型：默认Z51，可选Z52")
    private String moveType;

    @ApiModelProperty(value = "提报日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lossDate;

}
