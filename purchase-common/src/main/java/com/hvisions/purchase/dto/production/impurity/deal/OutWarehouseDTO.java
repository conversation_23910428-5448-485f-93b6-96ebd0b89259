package com.hvisions.purchase.dto.production.impurity.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: 杂质处理单出库
 * @author: Jcao
 * @time: 2022/4/27 15:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "杂质处理单出库")
public class OutWarehouseDTO {

    @ApiModelProperty(value = "杂质处理单ids")
    @NotNull(message = "杂质处理单id不能为空！")
    private Integer[] ids;

}
