package com.hvisions.purchase.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "原辅料使用报表")
public class MaterialUseStatementDTO {
    @ApiModelProperty(value = "日期")
    private String orderDate;

    @ApiModelProperty(value = "车间")
    private String workShop;

    @ApiModelProperty(value = "物料类别")
    private String materialTypeName;

    @ApiModelProperty(value = "使用数量")
    private Float quantityUse;

    @ApiModelProperty(value = "领用数量")
    private Float quantityReceive;
}
