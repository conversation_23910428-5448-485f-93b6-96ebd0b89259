package com.hvisions.purchase.dto.production.change.shift;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 交接班新增or修改
 * @author: Jcao
 * @time: 2022/4/27 18:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "交接班新增or修改")
public class ChangeShiftDTO extends SysBaseDTO {

    @ApiModelProperty(value = "原辅料管理部id")
    private Integer centerId;

    @ApiModelProperty(value = "原辅料管理部")
    private String center;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "车间")
    private String location;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date changeDate;

    @ApiModelProperty(value = "班次id")
    private Integer shiftId;

    @ApiModelProperty(value = "班次")
    private String shift;

    @ApiModelProperty(value = "班组id")
    private Integer crewId;

    @ApiModelProperty(value = "班组")
    private String crew;

}
