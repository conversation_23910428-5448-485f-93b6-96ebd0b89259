package com.hvisions.purchase.dto.purchase.inventory.location;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: sap库存地点维护
 * @author: Jcao
 * @time: 2022/4/2 9:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "sap库存地点维护分页查询")
public class InventoryLocationPageDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "库存地点编码")
    private String code;

    @ApiModelProperty(value = "库存地点名称")
    private String name;

    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    @ApiModelProperty(value = "中心名称")
    private String centerName;

    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;

    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "修改人")
    private String updaterName;

}
