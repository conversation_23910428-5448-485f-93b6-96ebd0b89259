package com.hvisions.purchase.dto.purchase.report;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 红高粱采供报表查询dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "红高粱采供报表查询dto")
public class RedSorghumPurchaseReportQueryDTO extends PageInfo {

    @ApiModelProperty(value = "物料编码集合，逗号隔开")
    private String materialCodes;

    @ApiModelProperty(value = "供应商编码集合，逗号隔开")
    private String vendorCodes;

    @ApiModelProperty(value = "sap采购编码")
    private String sapOrder;

    @ApiModelProperty(value = "合同号")
    private String contractNumber;

}
