package com.hvisions.purchase.dto.purchase.vendor;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 供应商分页条件dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商分页条件dto")
public class VendorPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "供应商名称")
    private String name;

    @ApiModelProperty(value = "供应商编码")
    private String code;

}
