package com.hvisions.purchase.dto.production.order.type;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 工单类型详情
 * @author: Jcao
 * @time: 2022/4/24 9:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "工单类型详情")
public class OrderTypeDetailDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "类型编码")
    private String code;

    @ApiModelProperty(value = "类型名称")
    private String name;

    @ApiModelProperty(value = "物料id")
    private String itemClassification;

    @ApiModelProperty(value = "物料名称")
    private String itemClassificationName;

    @ApiModelProperty(value = "移动类型id")
    private Integer movementTypeId;

    @ApiModelProperty(value = "移动类型名称")
    private String movementTypeName;
}
