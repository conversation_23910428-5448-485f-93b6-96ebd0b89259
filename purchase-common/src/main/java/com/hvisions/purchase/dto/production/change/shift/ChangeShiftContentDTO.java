package com.hvisions.purchase.dto.production.change.shift;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 交接班内容
 * @author: Jcao
 * @time: 2022/6/13 15:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "交接班内容")
public class ChangeShiftContentDTO {

    @ApiModelProperty(value = "明细id")
    private Integer id;

    @ApiModelProperty(value = "交接班内容")
    private String content;

}
