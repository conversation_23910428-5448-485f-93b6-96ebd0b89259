package com.hvisions.purchase.dto.production.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 原辅料领用报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "原辅料领用报表dto")
public class MaterialCollectionReportDTO {

    @ApiModelProperty(value = "酿酒中心")
    private String center;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "统计开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "统计结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "发放数量")
    private BigDecimal issueQuantity;

    @ApiModelProperty(value = "损耗")
    private BigDecimal lossQuantity;

    @ApiModelProperty(value = "单位")
    private String unit;


}
