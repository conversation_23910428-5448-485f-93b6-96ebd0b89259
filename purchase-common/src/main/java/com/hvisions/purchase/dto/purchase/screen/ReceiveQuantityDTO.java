package com.hvisions.purchase.dto.purchase.screen;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 收货数量统计
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "收货数量统计")
public class ReceiveQuantityDTO {

    @JsonFormat(pattern = "MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "日期", example = "2022-08-09")
    private Date date;

    @ApiModelProperty(value = "稻壳收货数量")
    private BigDecimal dkReceiveQuantity;

    @ApiModelProperty(value = "稻壳计划数量")
    private BigDecimal dkPlanQuantity;

    @ApiModelProperty(value = "稻壳比例")
    private BigDecimal dkRate;

    @ApiModelProperty(value = "高粱收货数量")
    private BigDecimal glReceiveQuantity;

    @ApiModelProperty(value = "高粱计划数量")
    private BigDecimal glPlanQuantity;

    @ApiModelProperty(value = "高粱比例")
    private BigDecimal glRate;

    @ApiModelProperty(value = "小麦收货数量")
    private BigDecimal xmReceiveQuantity;

    @ApiModelProperty(value = "小麦计划数量")
    private BigDecimal xmPlanQuantity;

    @ApiModelProperty(value = "小麦比例")
    private BigDecimal xmRate;

}
