package com.hvisions.purchase.dto.purchase.receiving;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 车辆异常DTO
 * @author: Jcao
 * @time: 2022/6/10 13:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "车辆异常DTO")
public class VehicleExceptionDTO extends SysBaseDTO {

    @ApiModelProperty(value = "类型：0、退货，1、复检")
    private String type;

    @ApiModelProperty(value = "检验任务id（如果为复检需要传）")
    private Integer taskId;

    @ApiModelProperty(value = "异常描述")
    private String remark;

}
