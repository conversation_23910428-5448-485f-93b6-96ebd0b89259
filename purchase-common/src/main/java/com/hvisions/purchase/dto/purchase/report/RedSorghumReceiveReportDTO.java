package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 红高粱收货报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "红高粱收货报表dto")
public class RedSorghumReceiveReportDTO {

    @ApiModelProperty(value = "日送货计划id")
    private Integer id;

    @ApiModelProperty(value = "日送货计划编号")
    private String planNumber;

    @ApiModelProperty(value = "送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "计划送货数量(t)")
    private BigDecimal deliverQuantity;

    @ApiModelProperty(value = "实际送货数量(t)")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "过账数量(t)")
    private BigDecimal postingWeight;

    @ApiModelProperty(value = "计划达成率(%)")
    private BigDecimal completeRate;

    // 送货车辆数量
    @ApiModelProperty(value = "订单供货批次数")
    private Integer totalBatch;

    // 送货车辆质检不合格数量
    @ApiModelProperty(value = "不合格批次数")
    private Integer unqualifiedBatch;

    // 存在扣重的送货车辆数
    @ApiModelProperty(value = "让步接收次数")
    private Integer buckleBatch;

    @ApiModelProperty(value = "首车入厂时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstInTime;

    @ApiModelProperty(value = "末车入厂时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastInTime;

    @ApiModelProperty(value = "MES采购订单号")
    private String orderNo;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "单价基数")
    private BigDecimal unitPriceBase;

    @ApiModelProperty(value = "物料单位")
    private String uom;

    @ApiModelProperty(value = "合同号")
    private String contractNumber;


}
