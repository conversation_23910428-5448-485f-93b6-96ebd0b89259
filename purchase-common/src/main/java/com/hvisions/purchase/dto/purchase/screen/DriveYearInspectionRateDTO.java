package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 驾驶舱看板-酿酒过程年检验合格率接口
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "驾驶舱看板-酿酒过程年检验合格率接口")
public class DriveYearInspectionRateDTO {

    @ApiModelProperty(value = "月份集合")
    private List<String> months;

    @ApiModelProperty(value = "检验总数")
    private List<Integer> totalList;

    @ApiModelProperty(value = "合格率")
    private List<BigDecimal> rateList;

    @ApiModelProperty(value = "年检验总数")
    private Integer yearTotal;

    @ApiModelProperty(value = "年检验合格数")
    private Integer yearQualifiedNum;
}
