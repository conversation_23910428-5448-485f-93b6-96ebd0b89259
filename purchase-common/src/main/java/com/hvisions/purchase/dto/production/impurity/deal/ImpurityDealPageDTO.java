package com.hvisions.purchase.dto.production.impurity.deal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import com.hvisions.purchase.dto.production.impurity.deal.detail.ImpurityDealDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 杂质处理单分页查询
 * @author: Jcao
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "杂质处理单分页查询")
public class ImpurityDealPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "任务单号")
    private String taskOrder;

    @ApiModelProperty(value = "生产单位")
    private String productionPart;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dealDate;

    @ApiModelProperty(value = "班次id")
    private Integer shiftId;

    @ApiModelProperty(value = "班次")
    private String shift;

    @ApiModelProperty(value = "源单号")
    private String sourceOrder;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "状态：0-新增、1-已称重、2-已完成，3-已关闭")
    private String state;

    @ApiModelProperty(value = "详情数据")
    private List<ImpurityDealDetailDTO> details;
}
