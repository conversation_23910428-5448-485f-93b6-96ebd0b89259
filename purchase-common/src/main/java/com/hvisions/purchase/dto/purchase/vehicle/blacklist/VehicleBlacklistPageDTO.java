package com.hvisions.purchase.dto.purchase.vehicle.blacklist;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 车辆黑名单分页返回dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "车辆黑名单分页返回dto")
public class VehicleBlacklistPageDTO {

    @ApiModelProperty(value = "车辆黑名单id")
    private Integer id;

    @ApiModelProperty(value = "送货单号")
    private String receiptNumber;

    @ApiModelProperty(value = "检验单号")
    private String checkOrder;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "黑名单次数")
    private Integer blacklistCount;

    @ApiModelProperty(value = "黑名单状态：0正常，1已解除")
    private String state;

    @ApiModelProperty(value = "黑名单解除时间")
    private Date relieveTime;

    @ApiModelProperty(value = "是否是品创：0-否，1-是")
    private String isPc;

    @ApiModelProperty(value = "原辅料感官检验结果")
    private String inspectResult;

    @ApiModelProperty(value = "srm状态 0-已下发；1-已处理")
    private String srmState;

    @ApiModelProperty(value = "处理详情")
    private String processDetail;

    @ApiModelProperty(value = "处理类型 1-文件；2文字")
    private String processType;

    @ApiModelProperty(value = "感官检验id")
    private Integer deliveryInspectId;

    @ApiModelProperty(value = "检验任务ids")
    private String inspectionIds;

    @ApiModelProperty(value = "检验任务orders")
    private String inspectionOrders;
}
