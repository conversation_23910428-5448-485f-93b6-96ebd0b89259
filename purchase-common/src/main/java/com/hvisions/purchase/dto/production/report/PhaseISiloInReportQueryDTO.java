package com.hvisions.purchase.dto.production.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 一期筒仓入库报表查询dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "一期筒仓入库报表查询dto")
public class PhaseISiloInReportQueryDTO extends PageInfo {

    @ApiModelProperty(value = "筒仓号")
    private String siloCode;

    @ApiModelProperty(value = "进料开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "进料结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "物料名称")
    private String materialName;


}
