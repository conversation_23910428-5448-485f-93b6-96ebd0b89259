package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 红高粱收货报表查询dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "红高粱收货报表查询dto")
public class RedSorghumReceiveReportQueryDTO extends PageInfo {

    @ApiModelProperty(value = "物料编码集合，逗号隔开")
    private String materialCodes;

    @ApiModelProperty(value = "供应商编码集合，逗号隔开")
    private String vendorCodes;

    @ApiModelProperty(value = "日送货计划号")
    private String planNumber;

    @ApiModelProperty(value = "送货日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "送货日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;


}
