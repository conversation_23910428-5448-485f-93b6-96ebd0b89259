package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: 丢糟车收货单位修改
 * @author: yyy
 * @time: 2022/5/9 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "丢糟车收货单位修改")
public class DzReceiptPlaceUpdateDTO {

    @ApiModelProperty(value = "送货车id")
    @NotNull(message = "送货车id不能为空")
    private Integer id;

    @ApiModelProperty(value = "收货单位")
    private String receiptPlace;

}
