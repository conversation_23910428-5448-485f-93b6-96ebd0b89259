package com.hvisions.purchase.dto.production.sorghum.shipment.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.production.sorghum.shipment.detail.SorghumShipmentDetailListDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 高粱转粮工单分页返回dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱转粮工单分页返回dto")
public class SorghumShipmentOrderPageDTO {

    @ApiModelProperty(value = "高粱转粮工单id")
    private Integer id;

    @ApiModelProperty(value = "高粱转粮计划id")
    private Integer planId;

    @ApiModelProperty(value = "计划编号")
    private String planNo;

    @ApiModelProperty(value = "工单编号")
    private String orderNo;

    @ApiModelProperty(value = "中心编码")
    private String centerCode;

    @ApiModelProperty(value = "工单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "生产类型编码")
    private String productionTypeCode;

    @ApiModelProperty(value = "生产类型名称")
    private String productionTypeName;


    @ApiModelProperty(value = "分发仓库id")
    private Integer sendWarehouseId;

    @ApiModelProperty(value = "接收仓库id")
    private Integer acceptWarehouseId;

    @ApiModelProperty(value = "分发仓库")
    private String sendWarehouse;

    @ApiModelProperty(value = "接收仓库")
    private String acceptWarehouse;


    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料单位")
    private String unit;

    @ApiModelProperty(value = "物料需求数量")
    private BigDecimal requirementQuantity;

    @ApiModelProperty(value = "当前库存数量")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal planQuantity;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "详情集合")
    List<SorghumShipmentDetailListDTO> detailList;

}
