package com.hvisions.purchase.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "原辅料使用报表查询条件")
public class MaterialUseStatementQueryDTO extends PageInfo {
    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "执行时间,开始日期 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date executionTimeStart;

    @ApiModelProperty(value = "执行时间,结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date executionTimeEnd;
}
