package com.hvisions.purchase.dto.purchase.receiving;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 送货车辆分页返回dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "送货车辆分页返回dto")
public class DeliveryVehiclePageDTO {

    @ApiModelProperty(value = "送货计划详情id")
    private Integer id;

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "日送货计划单号")
    private String planNumber;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "车辆状态： 0- 未入场、1-已入场、2-可卸货、3-卸货完成、4-出门、5-待处理、6-拒收、7-复检,8-特殊退货")
    private String state;

    @ApiModelProperty(value = "车辆状态： 0- 待检验（没有取样）、1-质检中（已取样之间）、2-合格（质检合格）、3-不合格（质检结果不合格）")
    private String inspectState;

    @ApiModelProperty(value = "卸货状态： 0- 卸货中、1-卸货完成")
    private String unloadState;

    @ApiModelProperty(value = "预送数量")
    private BigDecimal estimatedNumber;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "要货单号")
    private String orderNo;

    @ApiModelProperty(value = "要货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;

    @ApiModelProperty(value = "入场时间")
    private Date admissionTime;

    @ApiModelProperty(value = "质检结论")
    private String qualityResult;

    @ApiModelProperty(value = "质检单号")
    private String inspectionOrder;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "样品编码集合")
    private String samplingCodes;

    @ApiModelProperty(value = "检验单ids")
    private String inspectionIds;

    @ApiModelProperty(value = "检验单id")
    private Integer inspectionId;

    @ApiModelProperty(value = "检验备注")
    private String inspectionRemark;

    @ApiModelProperty(value = "取样任务id")
    private Integer samplingId;

    @ApiModelProperty(value = "异常数据id")
    private Integer exceptionId;

    @ApiModelProperty(value = "异常描述")
    private String exceptionDescription;

    @ApiModelProperty(value = "异常数据")
    private String exceptionData;

    @ApiModelProperty(value = "出场时间")
    private Date appearanceTime;

    @ApiModelProperty(value = "毛重（入场重量）")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "出场重量（皮重）")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "离厂净重")
    private BigDecimal leaveNetWeight;

    @ApiModelProperty(value = "扣重重量")
    private BigDecimal buckleWeight;

    @ApiModelProperty(value = "杂质重量")
    private BigDecimal impurityWeight;

    @ApiModelProperty(value = "最终净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "是否打印；0-否，1-是")
    private String print;

    @ApiModelProperty(value = "退货备注")
    private String returnRemark;

    @ApiModelProperty(value = "入库筒仓id")
    private Integer warehouseId;

    @ApiModelProperty(value = "物料类型编码")
    private String materialTypeCode;

    @ApiModelProperty(value = "收货单位")
    private String receiptPlace;

    @ApiModelProperty(value = "卸货人")
    private String unloadName;

    @ApiModelProperty(value = "卸货时间")
    private Date unloadTime;

    @ApiModelProperty(value = "部门id")
    private Integer departmentId;

    @ApiModelProperty(value = "入库仓库名称")
    private String locationName;

    @ApiModelProperty(value = "检测完成时间")
    private Date actualInspectionTime;

    @ApiModelProperty(value = "过账状态 0-未过账、1-已过账")
    private String postingState;

    @ApiModelProperty(value = "检验结果：合格/不合格")
    private String inspectResult;

    @ApiModelProperty(value = "不合格原因")
    private String inspectFailReason;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "图片地址")
    private String picUrls;

    @ApiModelProperty(value = "检验人--提交人")
    private String submitUser;
}
