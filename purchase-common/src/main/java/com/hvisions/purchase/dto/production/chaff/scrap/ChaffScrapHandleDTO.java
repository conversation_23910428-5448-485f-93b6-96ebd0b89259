package com.hvisions.purchase.dto.production.chaff.scrap;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 蒸糠报废处理
 * @author: Jcao
 * @time: 2022/5/19 10:44
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠报废处理")
public class ChaffScrapHandleDTO extends SysBaseDTO {

    @ApiModelProperty(value = "计量方式;0-地磅称重、1-车间称重")
    private String weightType;

    @ApiModelProperty(value = "车牌号")
    private List<String> licensePlateNumber;

    @ApiModelProperty(value = "处理数量")
    private BigDecimal dealQuantity;

    @ApiModelProperty(value = "记录人员id")
    private Integer recordPeopleId;

    @ApiModelProperty(value = "记录人员")
    private String recordPeople;
}
