package com.hvisions.purchase.dto.production.production.type;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 生产类型分页返回dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "生产类型分页返回dto")
public class ProductionTypePageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型说明")
    private String declaration;

    @ApiModelProperty(value = "创建人")
    private String creatorName;

}
