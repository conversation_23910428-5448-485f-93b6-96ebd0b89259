package com.hvisions.purchase.dto.production.change.shift;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 班次、班组信息Dto
 * @author: Jcao
 * @time: 2022/5/9 17:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "班次、班组信息Dto")
public class ShiftInfoDTO {

    @ApiModelProperty(value = "班次id")
    private Integer shiftId;

    @ApiModelProperty(value = "班次名称")
    private String shiftName;

    @ApiModelProperty(value = "班组id")
    private Integer crewId;

    @ApiModelProperty(value = "班组名称")
    private String crewName;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
