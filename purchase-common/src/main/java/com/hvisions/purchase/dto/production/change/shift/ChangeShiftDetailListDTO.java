package com.hvisions.purchase.dto.production.change.shift;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 交接班明细列表DTO
 * @author: Jcao
 * @time: 2022/4/28 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "交接班明细列表DTO")
public class ChangeShiftDetailListDTO extends SysBaseDTO {

    @ApiModelProperty(value = "交接班id")
    private Integer changeShiftId;

    @ApiModelProperty(value = "交接班内容")
    private String content;

    @ApiModelProperty(value = "类型：0-交班，1-接班")
    private String type;

    @ApiModelProperty(value = "填写人id")
    private Integer fileId;

    @ApiModelProperty(value = "填写人")
    private String filePeople;

    @ApiModelProperty(value = "填写时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fileTime;

}
