package com.hvisions.purchase.dto.production.bran.inspection.item;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 蒸糠质量巡检分页条件dto
 * @author: yyy
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠质量巡检分页条件dto")
public class BranInspectionItemPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "是否生效: 0-失效、1-生效")
    private String effectState;
}
