package com.hvisions.purchase.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "原辅料使用数据汇总")
public class MaterialUseDTO {
    @ApiModelProperty(value = "车间")
    private String workShop;

    @ApiModelProperty(value = "高粱使用量")
    private Float sorghum;

    @ApiModelProperty(value = "稻壳使用量")
    private Float riceHusk;

    @ApiModelProperty(value = "大曲使用量")
    private Float daQu;

    @ApiModelProperty(value = "回酒使用量")
    private Float backWine;
}
