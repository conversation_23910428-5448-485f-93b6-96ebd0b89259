package com.hvisions.purchase.dto.purchase.screen;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 收货数量统计
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "收货数量统计")
public class ReceiveQuantityDetailDTO {

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "日期", example = "2022-08-09")
    private Date date;

    @ApiModelProperty(value = "物料类别编码")
    private String materialTypeCode;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal planQuantity;


    @ApiModelProperty(value = "收货数量")
    private BigDecimal receiveQuantity;

    @ApiModelProperty(value = "收货比例")
    private BigDecimal rate;


}
