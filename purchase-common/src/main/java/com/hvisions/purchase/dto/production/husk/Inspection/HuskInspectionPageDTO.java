package com.hvisions.purchase.dto.production.husk.Inspection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import com.hvisions.purchase.dto.production.husk.Inspection.detail.HuskInspectionDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description: 蒸糠工艺巡检项目分页查询Dto
 * @author: Jcao
 * @time: 2022/5/13 14:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠工艺巡检项目分页查询Dto")
public class HuskInspectionPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "单据号")
    private String orderNo;

    @ApiModelProperty(value = "基地id")
    private Integer productionBaseId;

    @ApiModelProperty(value = "基地")
    private String productionBase;

    @ApiModelProperty(value = "原辅料管理部id")
    private Integer centerId;

    @ApiModelProperty(value = "原辅料管理部")
    private String center;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "车间")
    private String location;

    @ApiModelProperty(value = "产线id")
    private Integer productionLineId;

    @ApiModelProperty(value = "产线")
    private String productionLine;

    @ApiModelProperty(value = "检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inspectionTime;

    @ApiModelProperty(value = "检查结论;0-异常、1-正常")
    private String inspectionResult;

    @ApiModelProperty(value = "巡检完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inspectionFinishTime;

    @ApiModelProperty(value = "巡检人员id")
    private Integer inspectionPeopleId;

    @ApiModelProperty(value = "巡检人员")
    private String inspectionPeople;

    @ApiModelProperty(value = "状态;0-待巡检、1-已完成")
    private String state;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "图片id")
    private String imageId;

    @ApiModelProperty(value = "子项数据")
    List<HuskInspectionDetailDTO> details;

}
