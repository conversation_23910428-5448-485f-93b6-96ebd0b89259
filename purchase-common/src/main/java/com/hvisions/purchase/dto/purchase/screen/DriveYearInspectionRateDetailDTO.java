package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 驾驶舱看板-酿酒过程年检验合格率接口
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "驾驶舱看板-酿酒过程年检验合格率接口")
public class DriveYearInspectionRateDetailDTO {

    @ApiModelProperty(value = "年月")
    private String month;

    @ApiModelProperty(value = "检验总数")
    private Integer total;

    @ApiModelProperty(value = "合格数")
    private Integer qualifiedNum;

    @ApiModelProperty(value = "合格率")
    private BigDecimal rate;
}
