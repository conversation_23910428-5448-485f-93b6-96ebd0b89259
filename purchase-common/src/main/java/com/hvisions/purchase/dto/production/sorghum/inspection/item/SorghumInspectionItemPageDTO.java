package com.hvisions.purchase.dto.production.sorghum.inspection.item;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 蒸糠巡检项目分页查询dto
 * @author: Jcao
 * @time: 2022/5/6 9:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "蒸糠巡检项目分页查询dto")
public class SorghumInspectionItemPageDTO {

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "说明")
    private String declaration;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "顺序")
    private Integer sort;

    @ApiModelProperty(value = "父节点id")
    private Integer parentId;

}
