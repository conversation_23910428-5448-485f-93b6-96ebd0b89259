package com.hvisions.purchase.dto.production.material.requirement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 基地信息dto
 * @author: yyy
 * @time: 2022/4/28 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "基地信息dto")
public class BaseInfoDTO {

    @ApiModelProperty(value = "位置id")
    private Integer locationId;

    @ApiModelProperty(value = "位置")
    private String location;

}
