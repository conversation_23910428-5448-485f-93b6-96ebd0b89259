package com.hvisions.purchase.dto.purchase.purchasing.organization;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 采购组织分页查询条件
 * @author: Jcao
 * @time: 2022/4/2 9:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "采购组织分页查询条件")
public class PurchasingOrganizationPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "采购组织代码")
    private String code;

    @ApiModelProperty(value = "采购组织名称")
    private String name;

}
