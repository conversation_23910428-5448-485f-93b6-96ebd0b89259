package com.hvisions.purchase.dto.production.chaff.scrap;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.production.chaff.scrap.detail.ChaffScrapDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 蒸糠报废处理分页查询
 * @author: Jcao
 * @time: 2022/4/28 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "蒸糠报废处理分页查询")
public class ChaffScrapPageDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "处理单号")
    private String taskOrder;

    @ApiModelProperty(value = "源单号(检验单)")
    private String sourceOrder;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskDate;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "数量")
    private BigDecimal branQuantity;

    @ApiModelProperty(value = "不合格处理单id")
    private Integer disqualifiedId;

    @ApiModelProperty(value = "蒸糠工单id")
    private Integer orderId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态;0-待处理、1-已处理、2-完成")
    private String state;

    @ApiModelProperty(value = "原辅料部处理措施;2-退货、3-复检、4-报废、5-重蒸")
    private String treatmentMeasures;

    @ApiModelProperty(value = "原辅料部审意见")
    private String rawMaterialsCommit;

    @ApiModelProperty(value = "意见给出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handleTime;

    @ApiModelProperty(value = "原辅料处理人")
    private String handlePeople;

    @ApiModelProperty(value = "质检结果")
    private String inspectionResult;

    @ApiModelProperty(value = "详情列表")
    private List<ChaffScrapDetailDTO> details;

}
