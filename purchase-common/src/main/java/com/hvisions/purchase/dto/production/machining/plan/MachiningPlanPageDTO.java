package com.hvisions.purchase.dto.production.machining.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 预处理加工计划分页返回dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "预处理加工计划分页返回dto")
public class MachiningPlanPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "计划号")
    private String planNo;

    @ApiModelProperty(value = "计划日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDate;

    @ApiModelProperty(value = "供应商id")
    private Integer vendorId;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料单位")
    private String unit;

    @ApiModelProperty(value = "计划处理数量")
    private BigDecimal planDealNumber;

    @ApiModelProperty(value = "实际处理数量")
    private BigDecimal actualNumber;


}
