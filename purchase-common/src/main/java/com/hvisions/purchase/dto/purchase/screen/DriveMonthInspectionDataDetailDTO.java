package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 驾驶舱看板-高粱理化趋势图表接口
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "驾驶舱看板-高粱理化趋势图表接口")
public class DriveMonthInspectionDataDetailDTO {

    @ApiModelProperty(value = "年月")
    private String month;

    @ApiModelProperty(value = "指标名称")
    private String indicatorName;

    @ApiModelProperty(value = "指标平均值")
    private BigDecimal avgValue;
}
