package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 驾驶舱看板收货批次
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "驾驶舱看板收货批次")
public class DriveReceiveBatchDTO {

    @ApiModelProperty(value = "需求日期")
    private String demandDate;

    @ApiModelProperty(value = "物料类别编码")
    private String materialTypeCode;

    @ApiModelProperty(value = "总批次")
    private Integer total;

    @ApiModelProperty(value = "合格率")
    private BigDecimal ratio;
}
