package com.hvisions.purchase.dto.production.chaff.scrap;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 蒸糠报废处理新增or修改
 * @author: Jcao
 * @time: 2022/4/28 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠报废处理新增or修改")
public class ChaffScrapDTO extends SysBaseDTO {

    @ApiModelProperty(value = "源单号(检验单)")
    private String sourceOrder;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskDate;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "数量")
    private BigDecimal branQuantity;

    @ApiModelProperty(value = "不合格处理单id")
    private Integer disqualifiedId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态;0-待处理、1-已处理、2-完成")
    private String state;

}
