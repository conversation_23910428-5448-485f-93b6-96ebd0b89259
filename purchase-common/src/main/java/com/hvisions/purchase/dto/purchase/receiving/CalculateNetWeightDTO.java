package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: 计算送货车辆送货净重dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "计算送货车辆送货净重dto")
public class CalculateNetWeightDTO {

    @ApiModelProperty(value = "送货单id")
    @NotNull(message = "送货单id不能为空")
    private Integer id;

    @ApiModelProperty(value = "送货单号")
    @NotNull(message = "送货单号不能为空")
    private String deliveryNumber;

}
