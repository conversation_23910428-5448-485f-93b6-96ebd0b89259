package com.hvisions.purchase.dto.production.bran.balance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 蒸糠质量巡检详情Dto
 * @author: Jcao
 * @time: 2022/5/13 9:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "蒸糠质量巡检详情Dto")
public class BranBalanceValueDTO {

    @ApiModelProperty(value = "中心编码")
    private String center;

    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "平衡值")
    private BigDecimal balanceValue;


}
