package com.hvisions.purchase.dto.purchase.safety.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 物料需求信息
 * @author: Jcao
 * @time: 2022/4/12 11:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "物料需求信息")
public class MaterialStockInfoDTO {

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "预计需求数量（t）")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "库存数量（t）")
    private BigDecimal stockQuantity;

    @ApiModelProperty(value = "安全库存数量（t）")
    private BigDecimal safetyValue;

    @ApiModelProperty(value = "采购数量（t）")
    private BigDecimal procurementNumber;

    @ApiModelProperty(value = "在途数量（t）")
    private BigDecimal transitQuantity;

}
