package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 送货车辆报表dto
 * @author: Jcao
 * @time: 2022/6/8 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "送货车辆报表dto")
public class DeliveryVehicleInfoDTO {

    @ApiModelProperty(value = "送货计划详情id")
    private Integer id;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "毛重（入场重量）")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "出场重量（皮重）")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "离厂净重")
    private BigDecimal leaveNetWeight;

    @ApiModelProperty(value = "扣重重量")
    private BigDecimal buckleWeight;

    @ApiModelProperty(value = "杂质重量")
    private BigDecimal impurityWeight;

    @ApiModelProperty(value = "最终净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "质检备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "取样日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualDate;

    @ApiModelProperty(value = "收样日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiveData;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "检验单号")
    private String inspectionOrder;

    @ApiModelProperty(value = "检验次数")
    private String inspectioNumber;

    @ApiModelProperty(value = "样品编码集合")
    private String samplingCodes;

    @ApiModelProperty(value = "检验人")
    private String inspectionName;

    @ApiModelProperty(value = "质检结论")
    private String qualityResult;

    @ApiModelProperty(value = "质检备注")
    private String qualityRemark;

    @ApiModelProperty(value = "检验单id")
    private Integer inspectionId;

    @ApiModelProperty(value = "最终结果")
    private String finalResult;

    @ApiModelProperty(value = "取样人")
    private String samplingPeople;

}
