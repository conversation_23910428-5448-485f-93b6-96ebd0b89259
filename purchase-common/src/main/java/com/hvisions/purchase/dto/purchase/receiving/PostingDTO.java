package com.hvisions.purchase.dto.purchase.receiving;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Description: 送货车辆过账dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "送货车辆过账dto")
public class PostingDTO {

    @ApiModelProperty(value = "送货计划详情ids")
    @NotNull(message = "送货计划详情ids不能为空")
    private List<Integer> ids;

    @ApiModelProperty(value = "过账人")
    @NotNull(message = "过账人不能为空")
    private String postingPeople;

    @ApiModelProperty(value = "过账日期")
    @NotNull(message = "过账日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date postDate;

}
