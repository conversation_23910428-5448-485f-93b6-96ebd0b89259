package com.hvisions.purchase.dto.purchase.receiving;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 送货车辆导出dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "送货车辆导出dto")
public class VehicleExportDTO {

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "日送货计划单号")
    private String planNumber;

    @ApiModelProperty(value = "预计到货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "车辆状态： 0- 未入场、1-已入场、2-可卸货、3-卸货完成、4-出门、5-待处理、6-拒收、7-复检,8-特殊退货")
    private String state;

    @ApiModelProperty(value = "检验状态： 0-待检验、1-质检中、2-合格、3-不合格")
    private String inspectState;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "要货单号")
    private String orderNo;

    @ApiModelProperty(value = "入场时间")
    private Date admissionTime;

    @ApiModelProperty(value = "质检结论")
    private String qualityResult;

    @ApiModelProperty(value = "质检单号")
    private String inspectionOrder;

    @ApiModelProperty(value = "出场时间")
    private Date appearanceTime;

    @ApiModelProperty(value = "毛重(kg)")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "皮重(kg)")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "离厂净重(kg)")
    private BigDecimal leaveNetWeight;

    @ApiModelProperty(value = "扣重重量(kg)")
    private BigDecimal buckleWeight;

    @ApiModelProperty(value = "杂质重量(kg)")
    private BigDecimal impurityWeight;

    @ApiModelProperty(value = "最终净重(kg)")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "是否打印；0-否，1-是")
    private String print;

}
