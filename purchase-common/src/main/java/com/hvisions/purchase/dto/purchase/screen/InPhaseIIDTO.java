package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 今日二期库位入库统计dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "今日二期库位入库统计dto")
public class InPhaseIIDTO {

    @ApiModelProperty(value = "库位编码")
    private String code;

    @ApiModelProperty(value = "库位名称")
    private String name;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal netWeight;

}
