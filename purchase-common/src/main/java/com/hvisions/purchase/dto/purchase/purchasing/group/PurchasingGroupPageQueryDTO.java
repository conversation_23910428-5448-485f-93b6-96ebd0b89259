package com.hvisions.purchase.dto.purchase.purchasing.group;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 采购组分页查询条件
 * @author: Jcao
 * @time: 2022/4/2 9:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "采购组分页查询条件")
public class PurchasingGroupPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "采购组代码")
    private String code;

    @ApiModelProperty(value = "采购组名称")
    private String name;

}
