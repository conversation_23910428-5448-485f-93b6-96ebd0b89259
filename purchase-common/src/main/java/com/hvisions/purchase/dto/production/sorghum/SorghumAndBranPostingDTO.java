package com.hvisions.purchase.dto.production.sorghum;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 高粱稻壳发放过账dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱稻壳发放过账dto")
public class SorghumAndBranPostingDTO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "过账数量")
    private BigDecimal postingQuantity;

    @ApiModelProperty("过账日期")
    @NotNull(message = "过账日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date postingTime;

}
