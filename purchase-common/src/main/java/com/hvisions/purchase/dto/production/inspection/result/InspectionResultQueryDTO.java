package com.hvisions.purchase.dto.production.inspection.result;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 巡检结果查询条件
 * @author: Jcao
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "巡检结果查询条件")
public class InspectionResultQueryDTO extends PageInfo {

    @ApiModelProperty(value = "任务单号")
    private String remark;

    @ApiModelProperty(value = "生效状态;0-生效、1-失效")
    private String effectiveState;

}
