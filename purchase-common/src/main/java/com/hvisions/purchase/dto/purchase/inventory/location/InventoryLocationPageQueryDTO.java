package com.hvisions.purchase.dto.purchase.inventory.location;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: sap库存地点维护
 * @author: Jcao
 * @time: 2022/4/2 9:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "sap库存地点维护分页查询条件")
public class InventoryLocationPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "库存地点编码")
    private String code;

    @ApiModelProperty(value = "库存地点名称")
    private String name;

}
