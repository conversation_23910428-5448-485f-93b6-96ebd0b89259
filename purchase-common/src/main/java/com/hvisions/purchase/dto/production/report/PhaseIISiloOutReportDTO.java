package com.hvisions.purchase.dto.production.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 二期筒仓出库报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "二期筒仓出库报表dto")
public class PhaseIISiloOutReportDTO {


    @ApiModelProperty(value = "筒仓号")
    private String siloCode;

    @ApiModelProperty(value = "出仓日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "发料净重")
    private BigDecimal actualQuantity;

}
