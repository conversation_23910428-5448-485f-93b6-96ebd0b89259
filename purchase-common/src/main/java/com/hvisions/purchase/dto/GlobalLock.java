//package com.hvisions.purchase.dto;
//
//import java.lang.annotation.*;
//import java.util.concurrent.TimeUnit;
//
//import static java.lang.annotation.RetentionPolicy.RUNTIME;
//
///**
// * <p>Title: ApiResultIgnore</p>
// * <p>Description: 如果不想控制器自动包装结果可以添加此注解</p>
// * <p>Company: www.h-visions.com</p>
// * <p>create date: 2018/11/13</p>
// *
// * <AUTHOR>
// * @version :1.0.0
// */
//@Documented
//@Target(value = ElementType.METHOD)
//@Retention(RUNTIME)
//@Inherited
//public @interface GlobalLock {
//
//    /**
//     * 锁key的后缀
//     *
//     * @return redis 锁key的后缀
//     */
//    String suffix() default "";
//
//    /**
//     * 过期秒数,默认为5秒，这个值应该设置成你的程序最大运行时间还要长的值。
//     *
//     * @return 轮询锁的时间
//     */
//    @Deprecated
//    int expire() default 5;
//
//    /**
//     * 超时时间单位
//     *
//     * @return 秒
//     */
//    @Deprecated
//    TimeUnit timeUnit() default TimeUnit.SECONDS;
//
//}
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
