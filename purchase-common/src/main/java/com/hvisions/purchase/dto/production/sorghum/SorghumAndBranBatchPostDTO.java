package com.hvisions.purchase.dto.production.sorghum;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 高粱粉熟糠发放批量过账dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱粉熟糠发放批量过账dto")
public class SorghumAndBranBatchPostDTO {

    @ApiModelProperty(value = "工单详情ids")
    private List<SorghumAndBranPostDetailDTO> sorghumAndBranPostDetailDTOList;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "总过账数量")
    private BigDecimal postingQuantity;

    @ApiModelProperty("过账日期")
    @NotNull(message = "过账日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date postingDate;

}
