package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 出门车修改dto
 * @author: yyy
 * @time: 2022/5/9 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "出门车修改dto")
public class OutVehicleUpdateDTO {

    @ApiModelProperty(value = "送货车id")
    @NotNull(message = "送货车id不能为空")
    private Integer id;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "毛重")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "皮重")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "离厂净重")
    private BigDecimal leaveNetWeight;

    @ApiModelProperty(value = "部门id")
    Integer departmentId;

    @ApiModelProperty(value = "用户id")
    Integer userId;

}
