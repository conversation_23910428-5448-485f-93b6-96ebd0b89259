package com.hvisions.purchase.dto.production.plan.type;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 计划类型分页查询条件
 * @author: Jcao
 * @time: 2022/4/22 10:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "计划类型分页查询条件")
public class PlanTypePageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;
}
