package com.hvisions.purchase.dto.production.material.requirement;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 蒸糠机器运行时长dto
 * @author: yyy
 * @time: 2022/7/8 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠机器运行时长dto")
public class MachineTimeDTO extends SysBaseDTO {

    @ApiModelProperty(value = "蒸糠机器编码")
    private String code;

    @ApiModelProperty(value = "运行时长")
    private BigDecimal time;


}
