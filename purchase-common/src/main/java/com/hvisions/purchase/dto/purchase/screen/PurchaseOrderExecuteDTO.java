package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 执行中的采购订单
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "执行中的采购订单")
public class PurchaseOrderExecuteDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "采购订单号")
    private String orderNo;

    @ApiModelProperty(value = "sap订单号")
    private String sapOrder;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal quantitySupplied;

    @ApiModelProperty(value = "已到货数量")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "不合格车次")
    private Integer unqualifiedVehicle;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;


}
