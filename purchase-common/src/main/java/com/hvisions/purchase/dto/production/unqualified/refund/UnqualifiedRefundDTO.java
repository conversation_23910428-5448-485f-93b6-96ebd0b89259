package com.hvisions.purchase.dto.production.unqualified.refund;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 蒸糠退货处理新增or修改
 * @author: Jcao
 * @time: 2022/4/28 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠退货处理新增or修改")
public class UnqualifiedRefundDTO extends SysBaseDTO {

    @ApiModelProperty(value = "源单号(检验单)")
    private String sourceOrder;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskDate;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "不合格处理单id")
    private Integer disqualifiedId;

    @ApiModelProperty(value = "备注")
    private String remark;

}
