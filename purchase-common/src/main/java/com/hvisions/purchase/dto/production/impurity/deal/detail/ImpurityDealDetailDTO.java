package com.hvisions.purchase.dto.production.impurity.deal.detail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 杂质处理单详情
 * @author: Jcao
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "杂质处理单详情")
public class ImpurityDealDetailDTO {

    @ApiModelProperty(value = "详情id")
    private Integer id;

    @ApiModelProperty(value = "处理单id")
    private Integer dealId;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

}
