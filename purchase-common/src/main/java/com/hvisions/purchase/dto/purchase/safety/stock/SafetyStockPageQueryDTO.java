package com.hvisions.purchase.dto.purchase.safety.stock;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 计划安全库存分页条件dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "计划安全库存分页条件dto")
public class SafetyStockPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "物料名称")
    private String materialCode;

    @ApiModelProperty(value = "物料编码")
    private String materialName;

}
