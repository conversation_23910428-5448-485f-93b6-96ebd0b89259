package com.hvisions.purchase.dto.production.sorghum.dispense.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 高粱粉，熟糠发放详情查询dto
 * @author: yyy
 * @time: 2022/4/26 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱粉，熟糠发放详情查询dto")
public class MaterialDispenseDetailQueryDTO {

    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    @ApiModelProperty(value = "类型：0全部，1高粱，2稻壳")
    private String type;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
