package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: 扣重处理dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "扣重处理dto")
public class BuckleHandleDTO {

    @ApiModelProperty(value = "送货计划详情id")
    @NotNull(message = "送货计划详情id不能为空")
    private Integer id;

    @ApiModelProperty(value = "扣重是否处理；0-否，1-是")
    @NotNull(message = "扣重是否处理不能为空")
    private String buckleIsHandle;


}
