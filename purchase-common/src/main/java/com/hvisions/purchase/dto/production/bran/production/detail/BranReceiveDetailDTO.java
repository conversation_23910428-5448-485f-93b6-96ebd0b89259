package com.hvisions.purchase.dto.production.bran.production.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 接受自控传输任务信息dto
 * @author: hhz
 * @time: 2023/6/2 10:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "接受自控传输任务信息集合dto")
public class BranReceiveDetailDTO {

    @ApiModelProperty(value = "进料重量")
    private String feedWeight;

    @ApiModelProperty(value = "车间缓存仓")
    private String acceptSilo;

    @ApiModelProperty(value = "任务号")
    private String sendNo;

    @ApiModelProperty(value = "线路")
    private String route;

    @ApiModelProperty(value = "车间")
    private String plant;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "停止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
