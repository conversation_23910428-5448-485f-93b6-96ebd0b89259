package com.hvisions.purchase.dto.production.machining.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description: 预处理加工工单分页条件dto
 * @author: yyy
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "预处理加工工单分页条件dto")
public class MachiningOrderPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料类型编码")
    private List<String> materialTypeCodeList;

    @ApiModelProperty(value = "供应商id")
    private Integer vendorId;

    @ApiModelProperty(value = "工单开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "工单结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "卸货记录类型：1-高粱稻壳、2-小麦")
    private String type;
}
