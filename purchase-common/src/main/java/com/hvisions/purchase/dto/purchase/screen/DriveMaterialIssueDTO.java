package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 驾驶舱看板-原料发放接口
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "驾驶舱看板-原料发放接口")
public class DriveMaterialIssueDTO {

    @ApiModelProperty(value = "物料类别编码")
    private String materialType;

    @ApiModelProperty(value = "发放数量")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal planQuantity;
}
