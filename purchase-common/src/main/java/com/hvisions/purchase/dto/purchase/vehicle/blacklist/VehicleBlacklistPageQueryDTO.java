package com.hvisions.purchase.dto.purchase.vehicle.blacklist;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description: 车辆黑名单分页查询条件dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "车辆黑名单分页查询条件dto")
public class VehicleBlacklistPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "送货单号")
    private String receiptNumber;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "黑名单状态：0正常，1已解除")
    private String state;

    @ApiModelProperty(value = "是否是品创：0-否，1-是")
    private String isPc;

    @ApiModelProperty(value = "物料类型编码")
    private List<String> materialTypeCodeList;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;
}
