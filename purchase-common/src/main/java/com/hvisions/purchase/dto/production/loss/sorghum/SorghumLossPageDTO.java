package com.hvisions.purchase.dto.production.loss.sorghum;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 高粱损耗分页返回dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱损耗分页返回dto")
public class SorghumLossPageDTO {

    @ApiModelProperty(value = "损耗单id")
    private Integer id;

    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    @ApiModelProperty(value = "中心编码")
    private String center;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料单位")
    private String unit;

    @ApiModelProperty(value = "统计开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "统计结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "提报日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date postingTime;

    @ApiModelProperty(value = "发放数量")
    private BigDecimal issueQuantity;

    @ApiModelProperty(value = "损耗数量")
    private BigDecimal lossQuantity;

    @ApiModelProperty(value = "是否提报 0-未提报，1-已提报")
    private String state;

    @ApiModelProperty(value = "过账凭证号")
    private String matDoc;

    @ApiModelProperty(value = "详情集合")
    List<SorghumLossDetailListDTO> detailList;

}
