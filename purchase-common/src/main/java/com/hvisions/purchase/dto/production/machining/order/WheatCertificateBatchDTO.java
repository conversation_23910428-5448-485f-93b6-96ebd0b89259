package com.hvisions.purchase.dto.production.machining.order;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "小麦有机认证批次dto")
public class WheatCertificateBatchDTO {

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "小麦认证批次")
    private String wheatCertificateBatch;
}
