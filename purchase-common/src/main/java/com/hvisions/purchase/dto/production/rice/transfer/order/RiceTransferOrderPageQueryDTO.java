package com.hvisions.purchase.dto.production.rice.transfer.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 稻壳转运工单分页条件dto
 * @author: yyy
 * @time: 2022/2/26 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "稻壳转运工单分页条件dto")
public class RiceTransferOrderPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "工单开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "工单结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

}
