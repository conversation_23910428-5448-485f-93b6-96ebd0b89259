package com.hvisions.purchase.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

/**
 * <p>Title: SysDepartmentDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "示例对象")
public class UserDTO extends SysBaseDTO {

    /**
     * 名称(可选内容)
     */
    @Length(max = 15,message = "这里可以填一些自己的错误信息，这里的信息将直接通过统一的异常信息处理返回异常码为10000")
    @ApiModelProperty(value = "名称",required = true)
    private String userName;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 日期
     */
    private LocalDate date;

}
