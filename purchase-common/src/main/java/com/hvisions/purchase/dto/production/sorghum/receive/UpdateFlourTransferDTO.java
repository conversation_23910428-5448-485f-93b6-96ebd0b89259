package com.hvisions.purchase.dto.production.sorghum.receive;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱工单详情修改dto")
public class UpdateFlourTransferDTO {

    @ApiModelProperty(value = "任务号")
    private String jobIdent;

    @ApiModelProperty(value = "任务结束日期（示例：2023/2/10 23:10:05）")
    private Date endDateActual;
}
