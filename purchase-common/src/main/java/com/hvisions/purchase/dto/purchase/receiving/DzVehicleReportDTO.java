package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 丢糟汇总dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "丢糟汇总dto")
public class DzVehicleReportDTO {

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "收货单位")
    private String receiptPlace;

    @ApiModelProperty(value = "车次")
    private Integer count;

    @ApiModelProperty(value = "扣重重量")
    private BigDecimal sum;

}
