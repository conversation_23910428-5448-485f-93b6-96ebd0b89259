package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 稻壳报表导出dto
 * @author: Jcao
 * @time: 2022/6/8 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "稻壳报表导出dto")
public class RiceHuskReportExportDTO  {

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "收样日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiveData;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "水分")
    private String water;

    @ApiModelProperty(value = "粗细度（%）")
    private String coarseness;

    @ApiModelProperty(value = "夹杂物（%）")
    private String inclusion;

    @ApiModelProperty(value = "异色粒（%）")
    private String particle;

    @ApiModelProperty(value = "感观")
    private String sensory;

    @ApiModelProperty(value = "最终净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "质检结论")
    private String qualityResult;

    @ApiModelProperty(value = "质检备注")
    private String qualityRemark;

    @ApiModelProperty(value = "检验人")
    private String inspectionName;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "取样人")
    private String samplingPeople;

    @ApiModelProperty(value = "质检备注")
    private String remark;

    @ApiModelProperty(value = "检验次数")
    private String inspectioNumber;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "毛重")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "皮重")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "离厂净重")
    private BigDecimal leaveNetWeight;

    @ApiModelProperty(value = "扣重重量")
    private BigDecimal buckleWeight;

    @ApiModelProperty(value = "杂质重量")
    private BigDecimal impurityWeight;

}
