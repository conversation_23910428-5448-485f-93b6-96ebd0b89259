package com.hvisions.purchase.dto.purchase.vendor;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 要货需求单分页返回dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "要货需求单分页返回dto")
public class VendorPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "供应商编码")
    private String code;

    @ApiModelProperty(value = "供应商名称")
    private String name;

    @ApiModelProperty(value = "供应商组")
    private String vendorGroup;

    @ApiModelProperty(value = "采购组织名称")
    private String organizationName;

}
