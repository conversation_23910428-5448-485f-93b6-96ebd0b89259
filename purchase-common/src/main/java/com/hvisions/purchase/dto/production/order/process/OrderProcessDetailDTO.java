package com.hvisions.purchase.dto.production.order.process;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 工单工艺路线详情
 * @author: yyy
 * @time: 2022/5/5 10:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "工单工艺路线详情")
public class OrderProcessDetailDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "工单名称")
    private String name;

    @ApiModelProperty(value = "工单类型id")
    private Integer orderTypeId;

    @ApiModelProperty(value = "发出仓区")
    private String issueWarehouseName;

    @ApiModelProperty(value = "接收仓区")
    private String acceptWarehouseName;

    @ApiModelProperty(value = "发出仓区id")
    private Integer issueWarehouseId;

    @ApiModelProperty(value = "接收仓区id")
    private Integer acceptWarehouseId;

    @ApiModelProperty(value = "物料分类名称id")
    private String itemClassification;

    @ApiModelProperty(value = "物料分类名称")
    private String itemClassificationName;

    @ApiModelProperty(value = "移动类型id")
    private Integer movementTypeId;

    @ApiModelProperty(value = "移动类型名称")
    private String movementTypeName;

    @ApiModelProperty(value = "类型说明")
    private String declaration;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
