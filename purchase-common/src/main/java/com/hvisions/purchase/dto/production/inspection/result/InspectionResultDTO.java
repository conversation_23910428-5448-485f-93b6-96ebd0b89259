package com.hvisions.purchase.dto.production.inspection.result;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 巡检结果新增or修改
 * @author: Jcao
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "巡检结果新增or修改")
public class InspectionResultDTO extends SysBaseDTO {

    @ApiModelProperty(value = "异常项目编码")
    private String code;

    @ApiModelProperty(value = "异常项目描述")
    private String remark;

    @ApiModelProperty(value = "处理措施")
    private String treatmentMeasures;

    @ApiModelProperty(value = "应用模块;-1-全部、0-蒸糠质量巡检、1-蒸糠工艺巡检")
    private String module;

    @ApiModelProperty(value = "生效状态;0-生效、1-失效")
    private String effectiveState;

}
