package com.hvisions.purchase.dto.production.husk.Inspection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 蒸糠工艺巡检项目新增or修改Dto
 * @author: Jcao
 * @time: 2022/5/13 14:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠工艺巡检项目新增or修改Dto")
public class HuskInspectionDTO extends SysBaseDTO {

    @ApiModelProperty(value = "基地id")
    private Integer productionBaseId;

    @ApiModelProperty(value = "基地")
    private String productionBase;

    @ApiModelProperty(value = "原辅料管理部id")
    private Integer centerId;

    @ApiModelProperty(value = "原辅料管理部")
    private String center;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "车间")
    private String location;

    @ApiModelProperty(value = "产线id")
    private Integer productionLineId;

    @ApiModelProperty(value = "产线")
    private String productionLine;

    @ApiModelProperty(value = "检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inspectionTime;

}
