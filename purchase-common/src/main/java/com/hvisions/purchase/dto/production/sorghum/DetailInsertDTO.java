package com.hvisions.purchase.dto.production.sorghum;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 工单详情新增dto
 * @author: yyy
 * @time: 2022/8/5 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "工单详情新增dto")
public class DetailInsertDTO {

    @ApiModelProperty(value = "工单id")
    private Integer orderId;

    @ApiModelProperty(value = "中心编码")
    private String center;

    @ApiModelProperty(value = "任务号")
    private String jobIdent;

    @ApiModelProperty(value = "分发筒仓id(库区)")
    private Integer sendSiloId;

    @ApiModelProperty(value = "下料点位，1-一中心筒仓")
    private Integer point;//暂不需要

    @ApiModelProperty(value = "接收筒仓id(库区)")
    private Integer acceptSiloId;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料单位")
    private String unit;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "开始时间")
    private Date actualBeginTime;

    @ApiModelProperty(value = "停止时间")
    private Date actualEndTime;

    @ApiModelProperty("过账日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date postingTime;


}
