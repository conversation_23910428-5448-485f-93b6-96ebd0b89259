package com.hvisions.purchase.dto.production.sorghum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "工单详情修改dto")
public class DetailUpdateDTO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualQuantity;

}
