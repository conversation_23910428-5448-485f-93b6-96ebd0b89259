package com.hvisions.purchase.dto.purchase.purchasing.organization;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: 采购组织新增or修改
 * @author: Jcao
 * @time: 2022/4/2 9:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "采购组织新增or修改")
public class PurchasingOrganizationDTO extends SysBaseDTO {

    @ApiModelProperty(value = "采购组织代码")
    @NotNull(message = "采购组织代码不能为空")
    private String code;

    @ApiModelProperty(value = "采购组织名称")
    @NotNull(message = "采购组织名称不能为空")
    private String name;

}
