package com.hvisions.purchase.dto.purchase.inventory.location;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: sap库存地点维护
 * @author: Jcao
 * @time: 2022/4/2 9:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "sap库存地点维护新增or修改")
public class InventoryLocationDTO extends SysBaseDTO {

    @ApiModelProperty(value = "库存地点编码")
    @NotNull(message = "库存地点编码不能为空")
    private String code;

    @ApiModelProperty(value = "库存地点名称")
    @NotNull(message = "库存地点名称不能为空")
    private String name;

    @ApiModelProperty(value = "公司工厂代码维护id")
    @NotNull(message = "工厂id不能为空")
    private Integer maintenanceId;

    @ApiModelProperty(value = "关联MES中心ID")
    @NotNull(message = "关联MES中心ID")
    private Integer centerId;
}
