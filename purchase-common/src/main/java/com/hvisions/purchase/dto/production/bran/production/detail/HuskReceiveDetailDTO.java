package com.hvisions.purchase.dto.production.bran.production.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 接受自控发放任务信息dto
 * @author: hhz
 * @time: 2023/6/2 10:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "接受自控发放任务信息集合dto")
public class HuskReceiveDetailDTO {

    @ApiModelProperty(value = "进料重量")
    private String feedWeight;

    @ApiModelProperty(value = "任务号")
    private String sendNo;

    @ApiModelProperty(value = "发放筒仓")
    private String sendSilo;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "停止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
