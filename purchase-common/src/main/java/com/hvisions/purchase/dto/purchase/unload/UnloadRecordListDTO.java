package com.hvisions.purchase.dto.purchase.unload;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: 卸货记录dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "卸货记录dto")
public class UnloadRecordListDTO {

    @ApiModelProperty(value = "送货车id")
    private Integer id;

    @ApiModelProperty(value = "卸货位置")
    @NotNull(message = "卸货位置不能为空")
    private String unloadPosition;

    @ApiModelProperty(value = "卸货状态 空-可卸货：0-卸货中、1-卸货完成")
    private String unloadState;

    @ApiModelProperty(value = "质检状态： 0- 待检验、1-质检中、2-合格、3-不合格")
    private String inspectState;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;


}
