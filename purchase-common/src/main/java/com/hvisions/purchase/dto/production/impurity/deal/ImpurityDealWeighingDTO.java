package com.hvisions.purchase.dto.production.impurity.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 杂质处理单称重
 * @author: Jcao
 * @time: 2022/4/27 16:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "杂质处理单称重")
public class ImpurityDealWeighingDTO {

    @ApiModelProperty(value = "杂质处理单id")
    private Integer id;

    @ApiModelProperty(value = "称重数量")
    private BigDecimal weigh;

}
