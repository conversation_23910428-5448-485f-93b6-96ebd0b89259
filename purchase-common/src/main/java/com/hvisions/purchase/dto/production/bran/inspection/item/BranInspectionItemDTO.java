package com.hvisions.purchase.dto.production.bran.inspection.item;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 蒸糠质量巡检新增修改dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠质量巡检新增修改dto")
public class BranInspectionItemDTO extends SysBaseDTO {

    @ApiModelProperty(value = "检查项目编码")
    private String code;

    @ApiModelProperty(value = "检查项目名称")
    private String name;

    @ApiModelProperty(value = "检查设备")
    private String equipment;

    @ApiModelProperty(value = "检查位置")
    private String location;

    @ApiModelProperty(value = "检查手段")
    private String methods;

    @ApiModelProperty(value = "检查标准")
    private String standard;

    @ApiModelProperty(value = "巡检顺序")
    private Integer sort;

    @ApiModelProperty(value = "是否生效;0-失效、1-生效")
    private String effectState;

    @ApiModelProperty(value = "备注")
    private String remark;

}
