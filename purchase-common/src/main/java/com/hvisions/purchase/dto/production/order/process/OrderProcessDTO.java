package com.hvisions.purchase.dto.production.order.process;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Description: 工单工艺路线新增or修改
 * @author: Jcao
 * @time: 2022/4/22 10:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "工单工艺路线新增or修改")
public class OrderProcessDTO extends SysBaseDTO {

    @ApiModelProperty(value = "工单类型id")
    @NotNull(message = "工单类型id不能为空")
    private Integer orderTypeId;

    @ApiModelProperty(value = "发出仓库")
    @NotNull(message = "发出仓库不能为空")
    private Integer issueWarehouseId;

    @ApiModelProperty(value = "接收仓库")
    @NotNull(message = "接收仓库不能为空")
    private Integer acceptWarehouseId;

    @ApiModelProperty(value = "类型说明")
    @Size(min = 0, max = 50, message = "类型说明字符长度不能大于50")
    private String declaration;

}
