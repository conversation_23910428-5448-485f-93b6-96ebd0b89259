package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 物料数量统计
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "物料数量统计")
public class MaterialQuantityDTO {

    @ApiModelProperty(value = "物料类别编码")
    private String materialTypeCode;

    @ApiModelProperty(value = "预计送货数量")
    private BigDecimal deliverQuantity;

    @ApiModelProperty(value = "收货总净重")
    private BigDecimal inboundWeight;

    @ApiModelProperty(value = "所需物料吨数")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "到货物料吨数")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "完成率")
    private BigDecimal completeRatio;


}
