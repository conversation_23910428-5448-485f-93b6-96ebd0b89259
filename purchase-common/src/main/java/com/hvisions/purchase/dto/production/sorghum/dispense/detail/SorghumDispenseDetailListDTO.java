package com.hvisions.purchase.dto.production.sorghum.dispense.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 高粱分发工单详情集合dto
 * @author: yyy
 * @time: 2022/4/26 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱分发工单详情集合dto")
public class SorghumDispenseDetailListDTO {

    @ApiModelProperty(value = "详情id")
    private Integer id;

    @ApiModelProperty(value = "工单id")
    private Integer orderId;

    @ApiModelProperty(value = "分发筒仓id(库区)")
    private Integer sendSiloId;

    @ApiModelProperty(value = "分发筒仓(库区)")
    private String sendSilo;

    @ApiModelProperty(value = "接收筒仓id(库区)")
    private Integer acceptSiloId;

    @ApiModelProperty(value = "接收筒仓(库区)")
    private String acceptSilo;
    

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料单位")
    private String unit;

    @ApiModelProperty(value = "物料需求数量")
    private BigDecimal requirementQuantity;

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal planQuantity;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;

    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;

    @ApiModelProperty(value = "是否是手动：0-自动，1-手动")
    private String isManual;

    @ApiModelProperty(value = "sap状态：0未同步，1已同步")
    private String sapState;

    @ApiModelProperty(value = "过账凭证号")
    private String matDoc;

    @ApiModelProperty(value = "过账日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date postingTime;

    @ApiModelProperty(value = "创建人")
    private String createName;

}
