package com.hvisions.purchase.dto.production.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 熟糠发放报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "熟糠发放报表dto")
public class BranIssueReportDTO {

    @ApiModelProperty(value = "酿酒中心")
    private String centerName;

    @ApiModelProperty(value = "发放日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "下料点1次数")
    private Integer oneCount;

    @ApiModelProperty(value = "下料点2次数")
    private Integer twoCount;

    @ApiModelProperty(value = "下料点3次数")
    private Integer threeCount;

    @ApiModelProperty(value = "下料点4次数")
    private Integer fourCount;

    @ApiModelProperty(value = "下料点5次数")
    private Integer fiveCount;

    @ApiModelProperty(value = "下料点1重量")
    private Integer oneTotal;

    @ApiModelProperty(value = "下料点2重量")
    private Integer twoTotal;

    @ApiModelProperty(value = "下料点3重量")
    private Integer threeTotal;

    @ApiModelProperty(value = "下料点4重量")
    private Integer fourTotal;

    @ApiModelProperty(value = "下料点5重量")
    private Integer fiveTotal;

    @ApiModelProperty(value = "总重量")
    private Integer total;

    @ApiModelProperty(value = "总次数")
    private Integer count;

    @ApiModelProperty(value = "过账数量")
    private Integer postQuantity;

    @ApiModelProperty(value = "平衡量")
    private Integer balanceQuantity;


}
