package com.hvisions.purchase.dto.purchase.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 不合格退货报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "不合格退货报表dto")
public class DisqualifiedReturnReportDTO {

    @ApiModelProperty(value = "退货日期")
    private Date handleDate;

    @ApiModelProperty(value = "预计到货日期")
    private Date demandDate;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "不合格描述")
    private String rawMaterialsCommit;

    @ApiModelProperty(value = "处理结果")
    private String treatmentMeasures;

    @ApiModelProperty(value = "记录人")
    private String handlePeople;

    @ApiModelProperty(value = "计划送货数量")
    private BigDecimal estimatedNumber;

}
