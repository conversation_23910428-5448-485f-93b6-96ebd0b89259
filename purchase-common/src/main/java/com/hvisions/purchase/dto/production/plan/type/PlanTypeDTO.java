package com.hvisions.purchase.dto.production.plan.type;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @Description: 计划类型新增or修改
 * @author: Jcao
 * @time: 2022/4/22 10:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "计划类型新增or修改")
public class PlanTypeDTO extends SysBaseDTO {

    @ApiModelProperty(value = "类型编码")
    @NotNull(message = "类型编码不能为空")
    private String code;

    @ApiModelProperty(value = "类型名称")
    @NotNull(message = "类型名称不能为空")
    private String name;

    @ApiModelProperty(value = "物料分类")
    @NotNull(message = "物料分类不能为空")
    private List<Integer> itemClassification;

    @ApiModelProperty(value = "类型说明")
    @Size(min = 0, max = 50, message = "类型说明字符长度不能大于50")
    private String declaration;

}
