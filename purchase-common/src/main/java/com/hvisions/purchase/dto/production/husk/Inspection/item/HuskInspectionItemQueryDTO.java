package com.hvisions.purchase.dto.production.husk.Inspection.item;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 蒸糠工艺巡检项目查询条件Dto
 * @author: Jcao
 * @time: 2022/5/13 13:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠工艺巡检项目查询条件Dto")
public class HuskInspectionItemQueryDTO extends PageInfo {

    @ApiModelProperty(value = "巡检项目编码")
    private String code;

    @ApiModelProperty(value = "巡检项目名称")
    private String name;

    @ApiModelProperty(value = "是否生效;0-失效、1-生效")
    private String effectState;

}
