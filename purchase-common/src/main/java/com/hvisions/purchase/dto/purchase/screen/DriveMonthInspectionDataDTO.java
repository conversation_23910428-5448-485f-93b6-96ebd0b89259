package com.hvisions.purchase.dto.purchase.screen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description: 驾驶舱看板-高粱理化趋势图表接口
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "驾驶舱看板-高粱理化趋势图表接口")
public class DriveMonthInspectionDataDTO {

    @ApiModelProperty(value = "月份集合")
    private List<String> months;

    @ApiModelProperty(value = "指标和值")
    private Map<String, List<BigDecimal>> indicatorAndValue;

}
