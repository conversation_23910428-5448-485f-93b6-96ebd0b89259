package com.hvisions.purchase.dto.production.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 一期高粱转粮报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "一期高粱转粮报表dto")
public class PhaseISorghumShipmentReportDTO {

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "任务号")
    private String orderNo;

    @ApiModelProperty(value = "开始时间")
    private Date actualBeginTime;

    @ApiModelProperty(value = "结束时间")
    private Date actualEndTime;

    @ApiModelProperty(value = "产品编号")
    private String materialCode;

    @ApiModelProperty(value = "发出仓")
    private String sendSiloCode;

    @ApiModelProperty(value = "接收仓")
    private String acceptSiloCode;

    @ApiModelProperty(value = "实际重量")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "运行时间(分钟)")
    private BigDecimal runningTime;

}
