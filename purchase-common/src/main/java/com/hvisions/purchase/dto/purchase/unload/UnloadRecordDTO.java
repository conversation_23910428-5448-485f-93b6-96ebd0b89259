package com.hvisions.purchase.dto.purchase.unload;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: 卸货记录dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "卸货记录dto")
public class UnloadRecordDTO {

    @ApiModelProperty(value = "送货车id")
    @NotNull(message = "送货车id不能为空")
    private Integer vehicleId;

    @ApiModelProperty(value = "卸货位置")
    private String unloadPosition;

    /**
     * 入库库位id
     */
    @ApiModelProperty(value = "库位位置")
    @NotNull(message = "库位id不能为空")
    private Integer warehouseId;

}
