package com.hvisions.purchase.dto.production.bran.inspection.detail;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 蒸糠质量巡检详情列表Dto
 * @author: Jcao
 * @time: 2022/5/13 9:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠质量巡检详情列表Dto")
public class BranInspectionDetailListDTO extends SysBaseDTO {

    @ApiModelProperty(value = "质量巡检id")
    private Integer inspectionId;

    @ApiModelProperty(value = "是否运行;0-否、1-是")
    private String whetherRun;

    @ApiModelProperty(value = "检查结论;0-异常、1-正常")
    private String detailResult;

    @ApiModelProperty(value = "异常描述")
    private String abnormalDescription;

    @ApiModelProperty(value = "改进措施")
    private String improvementMeasures;

    @ApiModelProperty(value = "摊晒机")
    private String machine;

}
