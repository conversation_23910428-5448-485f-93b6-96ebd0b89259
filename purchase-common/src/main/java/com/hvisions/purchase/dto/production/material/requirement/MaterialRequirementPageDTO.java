package com.hvisions.purchase.dto.production.material.requirement;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 酿酒生产物料需求分页返回dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "酿酒生产物料需求分页返回dto")
public class MaterialRequirementPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "需求日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date requirementDate;

    @ApiModelProperty(value = "位置id")
    private Integer locationId;

    @ApiModelProperty(value = "位置")
    private String location;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料单位")
    private String unit;

    @ApiModelProperty(value = "配方量")
    private BigDecimal formulationAmount;

    @ApiModelProperty(value = "需求甑数")
    private BigDecimal vinasseData;

    @ApiModelProperty(value = "需求数量")
    private BigDecimal requirementNumber;

    @ApiModelProperty(value = "糟源类型")
    private String sourceCode;

    @ApiModelProperty(value = "父id")
    private Integer parentId;

    @ApiModelProperty(value = "状态 0-待接收、1-已接收")
    private String state;

    @ApiModelProperty(value = "子集物料需求")
    List<MaterialRequirementPageDTO> materials;

}
