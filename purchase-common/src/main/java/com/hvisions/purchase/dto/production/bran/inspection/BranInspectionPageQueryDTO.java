package com.hvisions.purchase.dto.production.bran.inspection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 蒸糠质量巡检分页条件dto
 * @author: yyy
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠质量巡检分页条件dto")
public class BranInspectionPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "单据号")
    private String orderNo;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "检查结论：0-异常、1-正常")
    private String inspectionResult;

    @ApiModelProperty(value = "状态：0-待处理、1-已完成")
    private String state;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
