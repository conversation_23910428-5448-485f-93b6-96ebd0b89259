package com.hvisions.purchase.dto.production.loss.sorghum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 高粱损耗详情dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱损耗详情dto")
public class SorghumLossDetailDTO {

    @ApiModelProperty(value = "损耗详情id")
    private Integer id;

    @ApiModelProperty(value = "工单id")
    private Integer orderId;

    @ApiModelProperty(value = "分发筒仓id")
    private Integer sendSiloId;

    @ApiModelProperty(value = "接收筒仓id")
    private Integer acceptSiloId;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料单位")
    private String unit;

    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;

    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;

    @ApiModelProperty(value = "发放数量")
    private BigDecimal issueQuantity;

    @ApiModelProperty(value = "相关库存表过账单号")
    private String certificateNumber;

}
