package com.hvisions.purchase.dto.production.production.type;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Description: 生产类型新增修改dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "生产类型新增修改dto")
public class ProductionTypeDTO extends SysBaseDTO {

    @ApiModelProperty(value = "编码")
    @NotNull(message = "编码不能为空")
    private String code;

    @ApiModelProperty(value = "名称")
    @NotNull(message = "生产类型名称不能为空")
    private String name;

    @ApiModelProperty(value = "类型说明")
    @Size(min = 0, max = 50, message = "类型说明字符长度不能大于50")
    private String declaration;


}
