package com.hvisions.purchase.dto.purchase.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "原辅料报表-不合格饼图")
public class PurchaseMaterialReportDefectiveDTO {

    @ApiModelProperty(value = "不合格原因")
    private String defectiveReason;

    @ApiModelProperty(value = "不合格数量")
    private String defectiveNum;
}
