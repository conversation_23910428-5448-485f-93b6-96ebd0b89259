package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 高粱报表导出dto
 * @author: Jcao
 * @time: 2022/6/8 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱报表导出dto")
public class SorghumReportExportDTO {

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "收样日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiveData;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "容重（g/L）")
    private String density;

    @ApiModelProperty(value = "水分（%）")
    private String water;

    @ApiModelProperty(value = "杂质（%）")
    private String impurity;

    @ApiModelProperty(value = "不完善粒（%）")
    private String Imperfect;

    @ApiModelProperty(value = "生霉病率（%）")
    private String moldRate;

    @ApiModelProperty(value = "生芽粒（%）")
    private String sproutedKernel;

    @ApiModelProperty(value = "感观")
    private String perception;

    @ApiModelProperty(value = "最终净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "质检结论")
    private String qualityResult;

    @ApiModelProperty(value = "质检备注")
    private String qualityRemark;

    @ApiModelProperty(value = "检验人")
    private String inspectionName;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "检验次数")
    private String inspectioNumber;

    @ApiModelProperty(value = "毛重")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "皮重")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "离厂净重")
    private BigDecimal leaveNetWeight;

    @ApiModelProperty(value = "扣重重量")
    private BigDecimal buckleWeight;

    @ApiModelProperty(value = "杂质重量")
    private BigDecimal impurityWeight;

    @ApiModelProperty(value = "取样人")
    private String samplingPeople;

    @ApiModelProperty(value = "质检备注")
    private String remark;


}
