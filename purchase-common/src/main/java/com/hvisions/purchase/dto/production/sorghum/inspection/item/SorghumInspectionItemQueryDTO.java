package com.hvisions.purchase.dto.production.sorghum.inspection.item;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 蒸糠巡检项目查询条件dto
 * @author: Jcao
 * @time: 2022/5/6 9:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "蒸糠巡检项目查询条件dto")
public class SorghumInspectionItemQueryDTO extends PageInfo {

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "说明")
    private String declaration;

}
