package com.hvisions.purchase.dto.production.plan.type;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 计划类型分页查询
 * @author: Jcao
 * @time: 2022/4/22 10:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "计划类型分页查询")
public class PlanTypePageDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "物料分类id")
    private String itemClassification;

    @ApiModelProperty(value = "物料分类名称")
    private String itemClassificationName;

    @ApiModelProperty(value = "类型说明")
    private String declaration;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
