package com.hvisions.purchase.dto.production.sorghum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 高粱稻壳发放过账dto
 * @author: yyy
 * @time: 2022/4/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱稻壳发放过账dto")
public class SorghumAndBranPostDetailDTO {

    @ApiModelProperty(value = "工单详情id")
    private Integer id;

    @ApiModelProperty(value = "过账数量")
    private BigDecimal postingQuantity;

}
