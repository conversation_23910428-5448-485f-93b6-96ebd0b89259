package com.hvisions.purchase.dto.production.sorghum.dispense.detail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 高粱粉，熟糠发放详情dto
 * @author: yyy
 * @time: 2022/4/26 10:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱粉，熟糠发放详情dto")
public class MaterialDispenseDetailDTO {

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "中心编码")
    private String center;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;

    @ApiModelProperty(value = "报工状态：1已同步,其他:未同步")
    private String sapState;

}
