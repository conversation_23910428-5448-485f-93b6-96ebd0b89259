package com.hvisions.purchase.dto.production.order.process;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 工单工艺路线分页查询条件
 * @author: Jcao
 * @time: 2022/4/22 10:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "工单工艺路线分页查询条件")
public class OrderProcessPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

}
