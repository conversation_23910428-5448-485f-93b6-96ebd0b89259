package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 异常数据关联dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "异常数据关联dto")
public class ReceivingExceptionDTO {

    @ApiModelProperty(value = "送货计划详情id")
    private Integer id;

    @ApiModelProperty(value = "异常id")
    private Integer exceptionId;

    @ApiModelProperty(value = "异常描述")
    private String exceptionDescription;
}
