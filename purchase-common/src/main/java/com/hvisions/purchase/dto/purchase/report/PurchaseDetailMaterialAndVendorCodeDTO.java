package com.hvisions.purchase.dto.purchase.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "原辅料报表明细dto")
public class PurchaseDetailMaterialAndVendorCodeDTO {
    @ApiModelProperty(value = "物料编码")
    private String materialCodes;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCodes;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    List<PurchaseDetailMaterialReportDTO> purchaseDetailMaterialReportDTOList;
}
