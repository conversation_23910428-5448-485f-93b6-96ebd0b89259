package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 原辅料报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "原辅料报表dto")
public class PurchaseMaterialReportDTO {

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "收货数量")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "过账数量")
    private BigDecimal postingQuantity;

    @ApiModelProperty(value = "杂质数量")
    private BigDecimal impurityQuantity;

    @ApiModelProperty(value = "送货车次")
    private Integer deliveryNum;

    @ApiModelProperty(value = "退货车次")
    private Integer refuseNum;

    @ApiModelProperty(value = "合格率")
    private String passRate;


    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

}
