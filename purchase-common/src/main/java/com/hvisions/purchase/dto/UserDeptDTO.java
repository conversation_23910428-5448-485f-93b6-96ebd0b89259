package com.hvisions.purchase.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "用户部门信息")
public class UserDeptDTO {
    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "部门id")
    private Integer departmentId;

    @ApiModelProperty(value = "部门编号")
    private String departmentCode;
}
