package com.hvisions.purchase.dto.production.production.weight;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 生产过磅管理分页查询条件
 * @author: Jcao
 * @time: 2022/2/22 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "生产过磅管理分页查询条件")
public class ProductionWeightQueryDTO extends PageInfo {

    @ApiModelProperty(value = "过磅单号")
    private String weightOrder;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "过磅类型;1、采购收货预处理杂质过磅，2、转运处理杂质过磅，3、不合格重蒸出库过磅，4、不合格退货出库过磅，5、重蒸预处理杂质过磅")
    private String type;

    @ApiModelProperty(value = "状态;0-新增、1-已下发、2-一次已过磅、3-二次已过磅、4-关闭")
    private String state;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束日期")
    private Date endDate;

}
