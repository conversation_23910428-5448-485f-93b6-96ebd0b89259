package com.hvisions.purchase.dto.production.change.shift;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 填写交接班明细DTO
 * @author: Jcao
 * @time: 2022/4/28 10:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "填写交接班明细DTO")
public class ChangeShiftDetailDTO extends SysBaseDTO {

    @ApiModelProperty(value = "类型：0、保存，1、提交")
    private Integer flag;

    @ApiModelProperty(value = "类型：0-交班，1-接班")
    private String type;

    @ApiModelProperty(value = "接班人id")
    private Integer contactId;

    @ApiModelProperty(value = "接班人名称")
    private String contactPeople;

    @ApiModelProperty(value = "填写人id")
    private Integer fileId;

    @ApiModelProperty(value = "填写人")
    private String filePeople;

    @ApiModelProperty(value = "交接班id")
    private Integer changeShiftId;

    @ApiModelProperty(value = "交接班内容集合")
    private List<ChangeShiftContentDTO> contents;

}
