package com.hvisions.purchase.dto.purchase.receiving;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description: 送货车辆分页查询条件dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "送货车辆分页查询条件dto")
public class DeliveryVehiclePageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "收货单位")
    private String receiptPlace;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "送货计划单号")
    private String planNumber;

    @ApiModelProperty(value = "不包含杂质：false-包含，true-不包含")
    private Boolean notContainZZ;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "供应商id")
    private Integer vendorId;

    @ApiModelProperty(value = "车辆状态：0- 未入场、1-已入场、2-可卸货、3-卸货完成、4-出门、5-待处理、6-拒收、7-复检,8-特殊退货")
    private String state;

    @ApiModelProperty(value = "质检状态： 0- 待检验、1-质检中、2-合格、3-不合格")
    private String inspectState;

    @ApiModelProperty(value = "车辆状态： 0-已入场、1-质检中、2-可卸货、3-已卸货、4-异常待处理、5-拒收、6-出门、7-已收货、8-特殊退货")
    private String[] states;

    @ApiModelProperty(value = "送货开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "送货结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "要货单号")
    private String orderNo;

    @ApiModelProperty(value = "质检结论")
    private String qualityResult;

    @ApiModelProperty(value = "物料类型编码")
    private List<String> materialTypeCodeList;

    @ApiModelProperty(value = "类型：1-高粱、2-稻壳、3-小麦")
    private List<String> typeList;

    @ApiModelProperty(value = "根据质检时间排序")
    private String orderByInspect;

    @ApiModelProperty(value = "出场开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date appearanceStartTime;

    @ApiModelProperty(value = "出场结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date appearanceEndTime;

}
