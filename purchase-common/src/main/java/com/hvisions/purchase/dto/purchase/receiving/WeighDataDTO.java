package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 过磅数据接收dtp
 * @author: yyy
 * @time: 2022/5/9 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "过磅数据接收dtp")
public class WeighDataDTO {

    @ApiModelProperty(value = "送货单号")
    @NotNull(message = "送货单号不能为空")
    private String deliveryNumber;

    @ApiModelProperty(value = "收货单位")
    private String receiptPlace;

    @ApiModelProperty(value = "车牌号")
    @NotNull(message = "车牌号不能为空")
    private String licensePlateNumber;

    @ApiModelProperty(value = "毛重（入场重量）")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "出场重量（皮重）")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "类型：入厂:1，出厂:2")
    @NotNull(message = "类型不能为空")
    private String type;


}
