package com.hvisions.purchase.dto.production.bran.inspection;

import com.hvisions.purchase.dto.production.bran.inspection.detail.BranInspectionDetailListDTO;
import com.hvisions.purchase.dto.production.bran.inspection.item.BranInspectionItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 蒸糠质量巡检详情Dto
 * @author: Jcao
 * @time: 2022/5/13 9:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "蒸糠质量巡检详情Dto")
public class BranInspectionDetailDTO {

    @ApiModelProperty(value = "检查详情")
    private List<BranInspectionDetailListDTO> details;

    @ApiModelProperty(value = "检查项目说明")
    private List<BranInspectionItemDTO> items;

    @ApiModelProperty(value = "巡检结果")
    private String inspectionResult;

    @ApiModelProperty(value = "备注")
    private String remark;

}
