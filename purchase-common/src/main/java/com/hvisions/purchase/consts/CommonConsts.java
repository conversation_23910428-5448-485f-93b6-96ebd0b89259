package com.hvisions.purchase.consts;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/3/22 20:58
 */
public interface CommonConsts {
    String PROP_SYS_DEPARTMENT_ID = "sysDepartmentId";
    String COL_DEFAULT_VALUE = "-1";
    String DEFAULT_SAAS_CODE = "super";
    long DEFAULT_TENANT_ID = 1L;
    long TENANT_ID_99L = 99L;
    long TENANT_ID_999L = 999L;
    String STRING_ZERO = "0";
    String STRING_NULL = "NULL";
    String STRING_TRUE = "true";
    String STRING_FALSE = "false";
    String STRING_TRUE_1 = "1";
    String STRING_FALSE_0 = "0";
    String DB_N = "(n)";
    String DB_P = "(p";
    String DB_S = ",s)";
    String DB_P_S = "(p,s)";
    String DB_P_S_V = "(ps)";
    String STRING_YSC = "（已删除）";
    byte TRUE = 1;
    byte FALSE = 0;
    int YES = 1;
    int NO = 0;
    String YES_STR = "yes";
    String NO_STR = "no";
    String YES_CH = "是";
    String NO_CH = "否";
    String USER_ENABLED_TRUE = "1";
    String USER_ENABLED_FALSE = "0";
    String PARAM_SESSION_USER_ID = "sessionUserId";
    String PARAM_SESSION_DEPT_ID = "sessionDeptId";
    String PARAM_DEPT_IDS = "deptIds";
    BigDecimal BD_ZERO = new BigDecimal(0);
    BigDecimal BD_ONE = new BigDecimal(1);
    BigDecimal BD_HUNDRED = new BigDecimal(100);
    long LONG_NULL = 0L;
    int INTEGER_NULL = 0;
    long LONG_TOP_NODE_PID = -100L;
    int SEQUENCE_START = 1;
    String CONTENT_TYPE = "application/json; charset=utf-8";
    String ROUTE_KEY = "_ROUTE_KEY";
    String SIGN_KEY = "DUST_KEY";
    String LOGBACK_FLAG = "DUST_LOG";
    String UTF8 = "utf-8";
    String GBK = "gbk";
    String ADMIN_ID = "1";
    String REQUEST_ID_KEY = "requestId";
    String FIRST_AUDIT_ROLE = "JHSP01";
    String SECOND_AUDIT_ROLE = "JHSP02";
    Integer CENTER = 20;
    Integer LOCATION = 30;
    String LOGIN_HINT = "当前用户未登陆，请重新登陆后重试";

    public interface Tenant {
        String ID = "id";
        String NAME = "name";
    }

    public interface HttpMethodType {
        String POST = "POST";
        String GET = "GET";
        String DELETE = "DELETE";
        String PUT = "PUT";
        String PATCH = "PATCH";
    }

    public interface ParamType {
        String HEADER = "header";
        String QUERY = "query";
        String PATH = "path";
        String BODY = "body";
        String FORM = "form";
    }

    public interface RoleId {
        long SUPER_ADMIN = 1L;
        long REGISTER_USER = 2L;
    }

    public interface Url {
        String OAUTH_TOKEN = "/oauth/token";
    }

    public interface GrantType {
        String PASSWORD = "password";
        String REFRESH_TOKEN = "refresh_token";
        String CUSTOM_MOBILE_PWD = "custom_mobile_pwd";
        String CUSTOM_MOBILE_SMS = "custom_mobile_sms";
    }

    public interface CodePrefix {
        String NOTICE = "GG";
        String FORM = "BD";
        String SUPERVISION = "JG";
        String CHART = "TB";
    }

    public interface CachePrefix {
        String DICTIONARY = "dictionary:";
        String API = "api:";
        String PERM_API = "perm_api:";
        String SESSION_USER = "session_user:";
        String HOLIDAY = "holiday:";
        String SAAS = "saas:";
        String DATA_ACCESS = "data_access:";
        String SMS_CODE = "sms_code:";
        String SMS_CODE_SENDCOUNT = "sms_code_sendcount:";
        String TENANT = "tenant:";
    }
    //消息通知是否为手机
    public interface Message {
        Integer phone = 1;
    }
}
