package com.hvisions.purchase.consts;

public interface ApiMessage {
    String ADD_SUCCESS = "ADD_SUCCESS";
    String UPDATE_SUCCESS = "UPDATE_SUCCESS";
    String DELETE_SUCCESS = "DELETE_SUCCESS";
    String SAVE_SUCCESS = "SAVE_SUCCESS";
    String REFRESH_SUCCESS = "REFRESH_SUCCESS";
    String IMPORT_SUCCESS = "IMPORT_SUCCESS";
    String SUBMIT_SUCCESS = "SUBMIT_SUCCESS";
    String CANCEL_SUCCESS = "CANCEL_SUCCESS";
    String BACK_SUCCESS = "BACK_SUCCESS";
    String RESET_SUCCESS = "RESET_SUCCESS";
    String ENABLE_SUCCESS = "ENABLE_SUCCESS";
    String DISABLE_SUCCESS = "DISABLE_SUCCESS";
    String AUTHORIZE_SUCCESS = "AUTHORIZE_SUCCESS";
    String SEND_SUCCESS = "SEND_SUCCESS";
    String WITHDRAW_SUCCESS = "WITHDRAW_SUCCESS";
    String OPERATE_SUCCESS = "OPERATE_SUCCESS";
    String PUBLISH_SUCCESS = "PUBLISH_SUCCESS";
    String LOGOUT_SUCCESS = "LOGOUT_SUCCESS";
    String COPY_SUCCESS = "COPY_SUCCESS";
    String CLONE_SUCCESS = "CLONE_SUCCESS";
}
