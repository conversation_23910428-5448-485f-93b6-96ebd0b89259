package com.hvisions.purchase.enums;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:19
 */
public enum StateEnum {
    // 状态枚举
    NEW("0", "新建"),
    TAKE_EFFECT("1", "生效"),
    FILE("2", "归档"),
    ;

    private String code;
    private String name;

    StateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
