package com.hvisions.purchase.enums;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:19
 */
public enum DemandStateEnum {
    // 状态枚举
    NOT_RECEIVED("0", "待接收"),
    RECEIVED("1", "已接收"),
    FINISH("2", "完成")
    ;

    private String code;
    private String name;

    DemandStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
