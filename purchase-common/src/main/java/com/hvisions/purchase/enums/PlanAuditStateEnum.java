package com.hvisions.purchase.enums;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:19
 */
public enum PlanAuditStateEnum {
    // 状态枚举
    NEW("0", "待审批"),
    IN_APPROVAL("1", "审批中"),
    APPROVAL_AGREE("2", "审批同意"),
    APPROVAL_REJECT("3", "审批拒绝"),
    ;

    private String code;
    private String name;

    PlanAuditStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
