package com.hvisions.purchase.enums;

/**
 * 曲斗状态枚举
 */
public enum DouStateEnum {
    // 状态枚举
    NORMAL("0", "正常"),
    INNORMAL("1", "检修");

    private String code;
    private String name;

    DouStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
