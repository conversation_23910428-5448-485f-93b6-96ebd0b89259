package com.hvisions.purchase.enums;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:19
 */
public enum TaskStateEnum {
    // 状态枚举
    NEW("0", "未处理"),
    ISSUE("1", "发放中"),
    FINISH("2", "已完成"),
    ;

    private String code;
    private String name;

    TaskStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
