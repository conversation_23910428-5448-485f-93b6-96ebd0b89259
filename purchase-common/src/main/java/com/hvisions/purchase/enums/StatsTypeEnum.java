package com.hvisions.purchase.enums;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:19
 */
public enum StatsTypeEnum {
    // 状态枚举
    INVESTMENT("0", "投入"),
    CONSUME("1", "消耗"),
    PRODUCE("2", "产出"),
    ;

    private String code;
    private String name;

    StatsTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
