package com.hvisions.purchase.enums;

public enum SapPostStateEnum {
    // 状态枚举
    NEW("4", "待发送"),
    SAP_PROCESSING("0", "SAP处理中"),
    FINISH("1", "完成"),
    FAILED("2", "失败"),
    REVOKE("3", "撤销"),
    DELAY_SEND("5", "延迟发送");

    private String code;
    private String name;

    SapPostStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
