package com.hvisions.purchase.enums;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:19
 */
public enum AuditResultEnum {
    // 状态枚举
    REJECT("0", "拒绝"),
    AGREE("1", "同意");

    private String code;
    private String name;

    AuditResultEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
