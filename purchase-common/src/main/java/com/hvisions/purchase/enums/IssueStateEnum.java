package com.hvisions.purchase.enums;

/**
 * 发货单状态
 */
public enum IssueStateEnum {
    NEW("0", "待发放"),
    PROCESSING("1", "进行中"),
    FINISH("2", "已完成");

    private String code;
    private String name;

    IssueStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
