package com.hvisions.purchase.enums;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:19
 */
public enum TaskDetailStateEnum {
    // 状态枚举
    NEW("0", "未发放"),
    FINISHED("1", "完成"),
    ;

    private String code;
    private String name;

    TaskDetailStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
