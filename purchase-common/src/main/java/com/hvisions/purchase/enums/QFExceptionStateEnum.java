package com.hvisions.purchase.enums;

public enum QFExceptionStateEnum {
    // 状态枚举
    NEW("0", "待处理"),
    SOLVED("1", "已处理"),
    CLOSED("2", "已关闭"),
    WITHDRAW("3","已撤销");


    private String code;
    private String name;

    QFExceptionStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
