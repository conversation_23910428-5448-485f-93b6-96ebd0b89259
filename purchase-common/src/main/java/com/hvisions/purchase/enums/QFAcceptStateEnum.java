package com.hvisions.purchase.enums;

public enum QFAcceptStateEnum {
    // 状态枚举
    NEW("0", "新建"),
    ACCEPT("1", "已接收"),
    RETURN_EXCHANGE("2", "退换"),
    RAISE_EXCEPTION("3","已发起异常");


    private String code;
    private String name;

    QFAcceptStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
