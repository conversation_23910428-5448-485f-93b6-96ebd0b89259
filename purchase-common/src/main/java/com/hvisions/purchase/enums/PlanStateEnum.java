package com.hvisions.purchase.enums;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:19
 */
public enum PlanStateEnum {
    // 状态枚举
    NEW("0", "待下发"),
    IMPLEMENT("1", "生效"),
    FINISH("2", "已完成"),
    FILE("3", "存档");

    private String code;
    private String name;

    PlanStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
