#默认公共配置
#开启feign的hystrix熔断功能
feign:
  hystrix:
    enabled: true
spring:
  jmx:
    enabled: false
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.10.4:8848
        namespace: dev
      config:
        #nacos中心地址
        server-addr: 192.168.10.4:8848
        # 配置文件格式
        file-extension: yaml
        namespace: dev
        override-none: true
        extension-configs:
          # 需共享的DataId，yaml后缀不能少，只支持yaml/properties
          # 越靠后，优先级越高
          - data-id: common.yaml
            refresh: true
  http:
    encoding:
      force: true
    charset: UTF-8
    enabled: true
  tomcat:
    uri-encoding: UTF-8
  #使用redis作为缓存
  cache:
    type: redis
  #序列化时间格式
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
    #数据库生成策略，如果打开会根据entity对象生成数据库。尽量不要使用
    hibernate:
      ddl-auto: update
  #服务注册名(此名称非常重要，是作为微服务框架中唯一标识一个服务的名称，相同名称的服务会被认为是提供一致接口的服务）
  application:
    name: printer
  #国际化配置
  messages:
    basename: i18n/messages
    cache-seconds: -1
    encoding: utf-8
ribbon:
  #请求处理的超时时间
  ReadTimeout: 120000
  #请求连接的超时时间
  ConnectTimeout: 30000
mybatis:
  configuration:
    map-underscore-to-camel-case: true
  typeAliasesPackage: com.hvisions.archetype.dto
  mapperLocations: classpath:mapper/*.xml
pagehelper:
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
  page-size-zero: true
server:
  port: 9999
#开启所有的健康监控信息
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
#info标签：可以在springboot admin的Insights界面Detail中进行展示,也可以再eureka界面点击实例名称查看
info:
  build:
    artifact: '@project.artifactId@'
    version: '@project.version@'
    server-name: ${h-visions.service-name}


