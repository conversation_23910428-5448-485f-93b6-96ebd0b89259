# suppress inspection "UnusedProperty" for whole file
#éç¨å¼å¸¸
SUCCESS=æå
EXISTS_TEMPLATE=å·²ç»å­å¨æå°æ¨¡æ¿ï¼ä¸åè®¸å é¤
SERVER_ERROR=æå¡å¨å¼å¸¸
JSON_PARSE_ERROR=Jsonè§£æéè¯¯
ILLEGAL_STRING=Jsonè§£æåºéï¼è¯·æ£æ¥jsonç»æ
NULL_RESULT=æ¥è¯¢ä¸ºç©º
VIOLATE_INTEGRITY=è¿åéå®ï¼è¯·æ£æ¥æ¯å¦æéå¤æ°æ®
IMPORT_FILE_NO_SUPPORT=æä»¶ç±»åä¸æ¯æ
IMPORT_SHEET_IS_NULL=æä»¶sheetè¡¨ä¸å­å¨
ENTITY_PROPERTY_NOT_SUPPORT=å®ä½å±æ§ä¸æ¯æï¼è¯·æ£æ¥å¯¼å¥æ°æ®
SAVE_SHOULD_NO_IDENTITY=ä¿å­ä¸åºè¯¥æä¸»é®
UPDATE_SHOULD_HAVE_IDENTITY=æ´æ°åºè¯¥æä¸»é®
CONST_VIOLATE=è¿åéå¶ï¼è¯·æ£æ¥æ°æ®åº
NO_SUCH_ELEMENT=æ°æ®æ¥è¯¢ä¸å­å¨
ENTITY_NOT_EXISTS=å¾æ©å±å¯¹è±¡ä¸å­å¨ï¼è¯·æ¥è¯¢åå¯¹è±¡æ¯å¦å­å¨
DATA_INTEGRITY_VIOLATION=æ°æ®å®æ´æ§éªè¯åºé
COLUMN_PATTERN_ILLEGAL=æ©å±åæ ¼å¼éæ³ï¼åªåè®¸æ°å­ï¼ä¸åçº¿ï¼è±æå­ç¬¦
#èªå®ä¹å¼å¸¸
DEMO_EXCEPTION_ENUM=ç¤ºä¾å¼å¸¸ç±»å