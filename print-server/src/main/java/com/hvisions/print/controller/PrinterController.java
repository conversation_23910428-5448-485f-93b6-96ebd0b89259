package com.hvisions.print.controller;

import com.hvisions.print.dto.PrinterDTO;
import com.hvisions.print.dto.PrinterInfoDTO;
import com.hvisions.print.dto.query.PrinterQuery;
import com.hvisions.print.service.PrinterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: PrinterController</p>
 * <p>Description: 打印机控制类</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/printer")
@Api(description = "打印机控制器")
public class PrinterController {

    private final PrinterService printerService;

    @Autowired
    public PrinterController(PrinterService printerService) {
        this.printerService = printerService;
    }


    /**
     * 创建打印机
     *
     * @param printerDTO 打印机信息
     * @return 打印机主键
     */
    @ApiOperation(value = "创建打印机")
    @PostMapping("/create")
    public Integer create(@Valid @RequestBody PrinterDTO printerDTO) {
        return printerService.create(printerDTO);
    }


    /**
     * 更新打印机
     *
     * @param printerDTO 打印机信息
     */
    @ApiOperation(value = "更新打印机")
    @PutMapping("/update")
    public void update(@Valid @RequestBody PrinterDTO printerDTO) {
        printerService.update(printerDTO);
    }

    /**
     * 删除打印机
     *
     * @param id 打印机id
     */
    @ApiOperation(value = "删除打印机")
    @DeleteMapping("/delete/{id}")
    public void delete(@PathVariable Integer id) {
        printerService.delete(id);
    }

    /**
     * 查询打印机分页数据
     *
     * @param printerQuery 查询条件
     * @return 分页数据
     */
    @ApiOperation(value = "查询打印机")
    @PostMapping("/getPage")
    public Page<PrinterDTO> getPage(@RequestBody PrinterQuery printerQuery) {
        return printerService.getPage(printerQuery);
    }

    /**
     * 根据id查询打印机信息
     *
     * @param id 主键
     * @return 打印机信息
     */
    @ApiOperation(value = "查询打印机")
    @PostMapping("/getById/{id}")
    public PrinterDTO getById(@PathVariable Integer id) {
        return printerService.getById(id);
    }


    /**
     * 查询本地USB连接的打印机信息
     *
     * @return 打印机信息
     */
    @GetMapping("/getPrinterLocalUSB")
    @ApiOperation("查找本地use连接的打印机信息")
    public List<PrinterInfoDTO> getPrinterLocalUSB() {
        return printerService.getPrinterLocalUSE();
    }
}









