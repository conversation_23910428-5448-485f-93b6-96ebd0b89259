package com.hvisions.print.controller;

import com.hvisions.print.dto.PrintTemplateDTO;
import com.hvisions.print.service.PrintTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>Title: PrintTemplateController</p>
 * <p>Description: 打印模板控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/printTemplate")
@Api(description = "打印模板控制器")
public class PrintTemplateController {

    private final PrintTemplateService printTemplateService;

    @Autowired
    public PrintTemplateController(PrintTemplateService printTemplateService) {
        this.printTemplateService = printTemplateService;
    }

    /**
     * 创建打印模板
     *
     * @param templateCode        打印模板编码
     * @param templateDescription 打印模板描述
     * @param printerId           打印机id
     * @param file                模板文件
     * @return 模板id
     */
    @ApiOperation(value = "创建打印模板")
    @PostMapping("/create")
    public Integer create(@RequestParam String templateCode,
                          @RequestParam(required = false) String templateDescription,
                          @RequestParam Integer printerId,
                          @RequestParam("file") MultipartFile file) {

        return printTemplateService.create(new PrintTemplateDTO(templateCode, templateDescription, printerId), file);
    }

    /**
     * 创建打印模板,网关问题，导致param传参异常
     *
     * @param templateCode        打印模板编码
     * @param templateDescription 打印模板描述
     * @param printerId           打印机id
     * @param file                模板文件
     * @return 模板id
     */
    @ApiOperation(value = "创建打印模板")
    @PostMapping("/createFix/{printerId}/{templateCode}/{templateDescription}")
    public Integer createFix(@PathVariable String templateCode,
                             @PathVariable Integer printerId,
                             @PathVariable(required = false) String templateDescription,
                             @RequestParam("file") MultipartFile file) {

        return printTemplateService.create(new PrintTemplateDTO(templateCode, templateDescription, printerId), file);
    }

    /**
     * 更新打印模板,网关传param参异常，绕过问题
     *
     * @param id                  模板id
     * @param templateDescription 打印模板描述
     * @param file                模板文件
     */
    @PostMapping("/updateFix/{id}/{templateDescription}")
    @ApiOperation(value = "更新打印模板 fix")
    public void updateFix( @PathVariable Integer id,
                          @PathVariable(required = false) String templateDescription,
                          @RequestParam("file") MultipartFile file) {
        printTemplateService.update(new PrintTemplateDTO(id, null, templateDescription, null), file);
    }

    /**
     * 更新打印模板
     *
     * @param id                  模板id
     * @param templateCode        打印模板编码
     * @param templateDescription 打印模板描述
     * @param printerId           打印机id
     * @param file                模板文件
     */
    @PutMapping("/update")
    @ApiOperation(value = "更新打印模板")
    public void update(@RequestParam String templateCode,
                       @RequestParam Integer id,
                       @RequestParam(required = false) String templateDescription,
                       @RequestParam Integer printerId,
                       @RequestParam("file") MultipartFile file) {
        printTemplateService.update(new PrintTemplateDTO(id, templateCode, templateDescription, printerId), file);
    }

    /**
     * 删除模板
     *
     * @param id 模板id
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除模板")
    public void delete(@PathVariable Integer id) {
        printTemplateService.delete(id);
    }


    /**
     * 根据打印机id查询模板数据
     *
     * @param printerId 打印机id
     * @return 模板数据
     */
    @GetMapping("/getByPrinterId/{printerId}")
    @ApiOperation(value = "根据打印机id查询模板数据")
    public List<PrintTemplateDTO> getByPrinterId(@PathVariable Integer printerId) {
        return printTemplateService.getByPrinterId(printerId);
    }


    /**
     * 根据id查询模板数据
     *
     * @param id 模板id
     * @return 模板数据
     */
    @GetMapping("/getById/{id}")
    @ApiOperation(value = "根据id查询模板数据")
    public PrintTemplateDTO getById(@PathVariable Integer id) {
        return printTemplateService.getById(id);
    }
}









