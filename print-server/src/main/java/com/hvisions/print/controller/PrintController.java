package com.hvisions.print.controller;

import com.hvisions.print.dto.PrintDTO;
import com.hvisions.print.service.PrintService;
import com.zebra.sdk.comm.ConnectionException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>Title: PrintController</p>
 * <p>Description: 打印控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping("/print")
@Api(description = "打印 控制器")
public class PrintController {

    private final PrintService printService;

    @Autowired
    public PrintController(PrintService printService) {
        this.printService = printService;
    }

    /**
     * 打印
     *
     * @param printDTO 打印对象
     * @throws ConnectionException 打印机连接异常
     */
    @ApiOperation("打印")
    @PostMapping("/print")
    public void print(@RequestBody PrintDTO printDTO) throws ConnectionException {
        printService.print(printDTO);
    }

}









