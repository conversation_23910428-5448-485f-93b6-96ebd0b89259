package com.hvisions.print.service.impl;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.print.dto.PrinterDTO;
import com.hvisions.print.dto.PrinterInfoDTO;
import com.hvisions.print.dto.query.PrinterQuery;
import com.hvisions.print.entity.HvPrinter;
import com.hvisions.print.enums.HExceptionEnum;
import com.hvisions.print.repository.PrintTemplateRepository;
import com.hvisions.print.repository.PrinterRepository;
import com.hvisions.print.service.PrinterService;
import com.zebra.sdk.comm.ConnectionException;
import com.zebra.sdk.printer.discovery.DiscoveredPrinterDriver;
import com.zebra.sdk.printer.discovery.UsbDiscoverer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: PrinterServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class PrinterServiceImpl implements PrinterService {
    private final PrinterRepository printerRepository;
    private final PrintTemplateRepository printTemplateRepository;

    @Autowired
    public PrinterServiceImpl(PrinterRepository printerRepository, PrintTemplateRepository printTemplateRepository) {
        this.printerRepository = printerRepository;
        this.printTemplateRepository = printTemplateRepository;
    }

    /**
     * 创建打印机
     *
     * @param printerDTO 打印机信息
     * @return 打印机主键
     */
    @Override
    public Integer create(PrinterDTO printerDTO) {
        Assert.isNull(printerDTO.getId(), "新增不能传递主键");
        return printerRepository.save(DtoMapper.convert(printerDTO, HvPrinter.class)).getId();
    }

    /**
     * 更新打印机
     *
     * @param printerDTO 打印机信息
     */
    @Override
    public void update(PrinterDTO printerDTO) {
        Assert.notNull(printerDTO.getId(), "更新需要传递主键");
        printerRepository.save(DtoMapper.convert(printerDTO, HvPrinter.class));
    }

    /**
     * 删除打印机
     *
     * @param id 打印机id
     */
    @Override
    public void delete(Integer id) {
        Optional<HvPrinter> printer = printerRepository.findById(id);
        printer.ifPresent(t -> {
            Boolean exists = printTemplateRepository.existsByPrinterId(t.getId());
            if (exists) {
                throw new BaseKnownException(HExceptionEnum.EXISTS_TEMPLATE);
            }else{
                printerRepository.deleteById(id);
            }
        });
    }

    /**
     * 查询打印机分页数据
     *
     * @param printerQuery 查询条件
     * @return 分页数据
     */
    @Override
    public Page<PrinterDTO> getPage(PrinterQuery printerQuery) {
        Page<HvPrinter> page = printerRepository.findAllByCodeContains(printerQuery.getCode(), printerQuery.getRequest());
        return DtoMapper.convertPage(page, PrinterDTO.class);
    }

    /**
     * 根据id查询打印机信息
     *
     * @param id 主键
     * @return 打印机信息
     */
    @Override
    public PrinterDTO getById(Integer id) {
        Optional<HvPrinter> printer = printerRepository.findById(id);
        return printer.map(hvPrinter -> DtoMapper.convert(hvPrinter, PrinterDTO.class)).orElse(null);
    }


    /**
     * 查询本地USB连接的打印机信息
     *
     * @return 打印机信息
     */
    @Override
    public List<PrinterInfoDTO> getPrinterLocalUSE() {
        List<PrinterInfoDTO> result = new ArrayList<>();
        List<DiscoveredPrinterDriver> zebraDriverPrinters;
        try {
            zebraDriverPrinters = Arrays.asList(UsbDiscoverer.getZebraDriverPrinters());
            for (DiscoveredPrinterDriver zebraDriverPrinter : zebraDriverPrinters) {
                PrinterInfoDTO printerInfo = new PrinterInfoDTO(
                        zebraDriverPrinter.driverName,
                        String.join("|", zebraDriverPrinter.portNames),
                        zebraDriverPrinter.printerName,
                        zebraDriverPrinter.address
                );
                result.add(printerInfo);
            }
        } catch (ConnectionException e) {
            log.error("查找本地打印机数据异常", e);
        }
        return result;
    }
}









