package com.hvisions.print.service;

import com.hvisions.print.dto.PrintTemplateDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>Title: PrintTemplateService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

public interface PrintTemplateService {
    /**
     * 创建打印模板
     *
     * @param printTemplateDTO 打印模板
     * @param file             模板文件
     * @return 模板id
     */
    Integer create(PrintTemplateDTO printTemplateDTO, MultipartFile file);

    /**
     * 更新打印模板
     *
     * @param printTemplateDTO 打印模板
     * @param file             模板文件
     */
    void update(PrintTemplateDTO printTemplateDTO, MultipartFile file);

    /**
     * 删除数据
     *
     * @param id 模板id
     */
    void delete(Integer id);


    /**
     * 根据id查询模板数据
     *
     * @param id 模板id
     * @return 模板数据
     */
    PrintTemplateDTO getById(Integer id);

    /**
     * 根据打印机id查询模板数据
     *
     * @param printerId 打印机id
     * @return 模板数据
     */
    List<PrintTemplateDTO> getByPrinterId(Integer printerId);
}

    
    
    
    