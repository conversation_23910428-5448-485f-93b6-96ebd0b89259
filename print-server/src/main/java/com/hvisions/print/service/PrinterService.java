package com.hvisions.print.service;

import com.hvisions.print.dto.PrinterDTO;
import com.hvisions.print.dto.PrinterInfoDTO;
import com.hvisions.print.dto.query.PrinterQuery;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: PrinterService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface PrinterService {
    /**
     * 创建打印机
     *
     * @param printerDTO 打印机信息
     * @return 打印机主键
     */
    Integer create(PrinterDTO printerDTO);

    /**
     * 更新打印机
     *
     * @param printerDTO 打印机信息
     */
    void update(PrinterDTO printerDTO);

    /**
     * 删除打印机
     *
     * @param id 打印机id
     */
    void delete(Integer id);

    /**
     * 查询打印机分页数据
     *
     * @param printerQuery 查询条件
     * @return 分页数据
     */
    Page<PrinterDTO> getPage(PrinterQuery printerQuery);

    /**
     * 根据id查询打印机信息
     *
     * @param id 主键
     * @return 打印机信息
     */
    PrinterDTO getById(Integer id);
    /**
     * 查询本地USB连接的打印机信息
     * @return 打印机信息
     */
    List<PrinterInfoDTO> getPrinterLocalUSE();
}









