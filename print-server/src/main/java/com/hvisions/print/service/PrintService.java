package com.hvisions.print.service;

import com.hvisions.print.dto.PrintDTO;
import com.zebra.sdk.comm.ConnectionException;

/**
 * <p>Title: PrintService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface PrintService {
    /**
     * 打印
     *
     * @param printDTO 打印对象
     * @throws ConnectionException 打印机连接异常
     */
    void print(PrintDTO printDTO) throws ConnectionException;
}









