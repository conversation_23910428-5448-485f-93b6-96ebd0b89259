package com.hvisions.print.service.impl;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.print.consts.PrintConsts;
import com.hvisions.print.dto.PrintDTO;
import com.hvisions.print.entity.HvPrintTemplate;
import com.hvisions.print.entity.HvPrinter;
import com.hvisions.print.repository.PrintTemplateRepository;
import com.hvisions.print.repository.PrinterRepository;
import com.hvisions.print.service.PrintService;
import com.zebra.sdk.comm.Connection;
import com.zebra.sdk.comm.ConnectionException;
import com.zebra.sdk.comm.DriverPrinterConnection;
import com.zebra.sdk.comm.TcpConnection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <p>Title: PrintServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class PrintServiceImpl implements PrintService {
    private final PrinterRepository printerRepository;
    private final PrintTemplateRepository printTemplateRepository;

    @Autowired
    public PrintServiceImpl(PrinterRepository printerRepository, PrintTemplateRepository printTemplateRepository) {
        this.printerRepository = printerRepository;
        this.printTemplateRepository = printTemplateRepository;
    }

    /**
     * 打印
     *
     * @param printDTO 打印对象
     */
    @Override
    public void print(PrintDTO printDTO) throws ConnectionException {
        //获取打印机信息
        HvPrinter printer = printerRepository.findByCode(printDTO.getPrinterCode());
        if (printer == null) {
            throw new BaseKnownException(10000, "找不到对应的打印机");
        }
        //获取打印模板信息
        HvPrintTemplate template = printTemplateRepository.findByPrinterIdAndCode(printer.getId(), printDTO.getPrintTemplateCode());
        if (template == null) {
            throw new BaseKnownException(10000, "找不到对应的模板设置");
        }
        //如果不是zebra打印机直接报错
        if (!PrintConsts.ZEBRA.equals(printer.getPrinterType())) {
            throw new BaseKnownException(10000, "目前支持支zebra打印机");
        }
        //获取打印机连接
        Connection connection = null;
        //如果打印机是tcp连接
        if (PrintConsts.TCP.equals(printer.getConnectType())) {
            connection = new TcpConnection(printer.getIpAddress(), printer.getPort());
        }
        //如果打印机是usb连接
        else if (PrintConsts.USB.equals(printer.getConnectType())) {
            try {
                connection = new DriverPrinterConnection(printer.getPrinterName());
            } catch (ConnectionException e) {
                throw new BaseKnownException(10000, e.getMessage());
            }
        } else {
            throw new BaseKnownException(10000, "不支持的打印机连接类型");
        }
        //获取模板数据
        //替换模板内容
        String zpl = new String(template.getTemplateInfo(), StandardCharsets.UTF_8);
        //为了解决zpl默认字体无法打印中文的问题，需要增加两行配置信息，并且将^A0N改为^A1N
        //代表使用SIMSUM.TTF (宋体),zpl编码格式为UTF8,打印机需要安装SIMSUM 字体，一般斑马打印机都已经安装
        zpl = zpl.replace("^XA\r\n", "^XA\r\n" +
                "^CW1,E:SIMSUN.TTF\r\n" +
                "^CI28\r\n");
        //打印字符的标签需要进行替换。
        zpl = zpl.replace("^A0N", "^A1N");
        if (printDTO.getData() == null || printDTO.getData().size() == 0) {
            try {
                connection.open();
                connection.write(template.getTemplateInfo());
            } catch (ConnectionException e) {
                log.error("打印连接异常", e);
                throw new BaseKnownException(10000, "打印异常:" + e.getMessage());
            } finally {
                connection.close();
            }
        } else {
            for (Map<String, String> argsMap : printDTO.getData()) {
                String finalZpl = zpl;
                for (String key : argsMap.keySet()) {
                    //替换字符串，需要把特殊的^ , \ 改为ASCII码。
                    finalZpl = finalZpl.replace("{{" + key + "}}", argsMap.get(key));
                }
                //调用API进行打印
                try {
                    connection.open();
                    connection.write(finalZpl.getBytes(StandardCharsets.UTF_8));
                } catch (ConnectionException e) {
                    log.error("打印连接异常", e);
                    throw new BaseKnownException(10000, "打印异常:" + e.getMessage());
                } finally {
                    connection.close();
                }
            }
        }

    }
}









