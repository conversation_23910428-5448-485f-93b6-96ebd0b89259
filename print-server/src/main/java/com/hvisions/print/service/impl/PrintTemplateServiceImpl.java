package com.hvisions.print.service.impl;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.print.dto.PrintTemplateDTO;
import com.hvisions.print.entity.HvPrintTemplate;
import com.hvisions.print.repository.PrintTemplateRepository;
import com.hvisions.print.service.PrintTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: PrintTemplateServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class PrintTemplateServiceImpl implements PrintTemplateService {
    private final PrintTemplateRepository printTemplateRepository;

    @Autowired
    public PrintTemplateServiceImpl(PrintTemplateRepository printTemplateRepository) {
        this.printTemplateRepository = printTemplateRepository;
    }

    /**
     * 创建打印模板
     *
     * @param printTemplateDTO 打印模板
     * @param file             模板文件
     * @return 模板id
     */
    @Override
    public Integer create(PrintTemplateDTO printTemplateDTO, MultipartFile file) {
        Assert.isNull(printTemplateDTO.getId(), "新增不能传递主键");
        HvPrintTemplate template = DtoMapper.convert(printTemplateDTO, HvPrintTemplate.class);
        try {
            template.setTemplateInfo(file.getBytes());
        } catch (IOException e) {
            throw new BaseKnownException(10000, "获取模板文件数据异常");
        }
        return printTemplateRepository.save(template).getId();
    }

    /**
     * 更新打印模板
     *
     * @param file             模板文件
     * @param printTemplateDTO 打印模板
     */
    @Override
    public void update(PrintTemplateDTO printTemplateDTO, MultipartFile file) {
        Assert.notNull(printTemplateDTO.getId(), "更新需要传递主键");
        Optional<HvPrintTemplate> template = printTemplateRepository.findById(printTemplateDTO.getId());
        template.ifPresent(t->{
            try {
                t.setTemplateInfo(file.getBytes());
            } catch (IOException e) {
                throw new BaseKnownException(10000, "获取模板文件数据异常");
            }
            t.setDescription(printTemplateDTO.getDescription());
            printTemplateRepository.save(t);
        });
    }

    /**
     * 删除数据
     *
     * @param id 模板id
     */
    @Override
    public void delete(Integer id) {
        printTemplateRepository.deleteById(id);
    }


    /**
     * 根据id查询模板数据
     *
     * @param id 模板id
     * @return 模板数据
     */
    @Override
    public PrintTemplateDTO getById(Integer id) {
        Optional<HvPrintTemplate> result = printTemplateRepository.findById(id);
        return result.map(t -> DtoMapper.convert(t, PrintTemplateDTO.class)).orElse(null);
    }

    /**
     * 根据打印机id查询模板数据
     *
     * @param printerId 打印机id
     * @return 模板数据
     */
    @Override
    public List<PrintTemplateDTO> getByPrinterId(Integer printerId) {
        return DtoMapper.convertList(printTemplateRepository.findByPrinterId(printerId), PrintTemplateDTO.class);
    }
}









