package com.hvisions.print.repository;

import com.hvisions.print.entity.HvPrinter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: PrinterRepository</p>
 * <p>Description: 打印机仓储</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface PrinterRepository extends JpaRepository<HvPrinter, Integer> {
    /**
     * 根据打印机编号进行查询
     *
     * @param code     编号
     * @param pageable 分页数据
     * @return 打印机分页数据
     */
    Page<HvPrinter> findAllByCodeContains(String code, Pageable pageable);


    /**
     * 根据打印机编码进行查询
     * @param code 编码
     * @return 打印机信息
     */
    HvPrinter findByCode(String code);
}

    
    
    
    