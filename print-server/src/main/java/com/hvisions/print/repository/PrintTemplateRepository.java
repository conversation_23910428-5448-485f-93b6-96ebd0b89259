package com.hvisions.print.repository;

import com.hvisions.print.entity.HvPrintTemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: PrintTemplateRepository</p>
 * <p>Description: 打印模板仓储</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface PrintTemplateRepository extends JpaRepository<HvPrintTemplate, Integer> {
    /**
     * 查找打印机是否存在模板
     *
     * @param printerId 打印机id
     * @return 是否存在模板
     */
    Boolean existsByPrinterId(Integer printerId);

    /**
     * 查询打印机下的模板信息
     * @param printerId 打印机模板
     * @return 模板信息
     */
    List<HvPrintTemplate> findByPrinterId(Integer printerId);


    /**
     * 根据打印机id和模板编码查找打印模板
     * @param printerId 打印机id
     * @param code 模板编码
     * @return 打印模板
     */
    HvPrintTemplate findByPrinterIdAndCode(Integer printerId,String code);
}

    
    
    
    