package com.hvisions.print.entity;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>Title: HvPrinter</p>
 * <p>Description: 打印机信息</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
@Setter
@ToString
@Entity
@Table(uniqueConstraints = @UniqueConstraint(name = "打印机编码不可重复",columnNames = "code"))
public class HvPrinter extends SysBase{
    /**
     * 打印机编码
     */
    @NotBlank(message = "打印机编码不能为空")
    private String code;

    /**
     * 打印机描述
     */
    private String description;

    /**
     * 打印机型号
     */
    @NotBlank(message = "打印机类型不能为空")
    private String printerType;

    /**
     * 打印机连接类型，1：tcp，2:USB
     */
    @NotNull(message = "打印机连接类型不能为空")
    private String connectType;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * 打印机信息
     */
    private String printerName;


}









