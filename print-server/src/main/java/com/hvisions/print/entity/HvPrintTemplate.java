package com.hvisions.print.entity;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>Title: HvPrintTemplate</p>
 * <p>Description: 打印模板</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
@Setter
@ToString
@Entity
@Table(uniqueConstraints = @UniqueConstraint(name = "打印模板编码不可重复", columnNames = {"code","printerId"}))
public class HvPrintTemplate extends SysBase {
    /**
     * 模板编码
     */
    @NotBlank(message = "模板编码不能为空")
    private String code;
    /**
     * 模板描述
     */
    private String description;

    /**
     * 模板数据
     */
    @Lob
    @NotNull(message = "模板数据不能为空")
    private byte[] templateInfo;

    /**
     * 打印机id
     */
    @NotNull(message = "模板的打印机信息不能为空")
    private Integer printerId;

}









