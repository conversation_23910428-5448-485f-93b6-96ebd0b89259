package com.hvisions.rawmaterial.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.entity.TMpdStOrderDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description:高粱转运工单详情
 * @date 2022/4/22 10:18
 */
@Mapper
public interface StOrderDetailMapper extends BaseMapper<TMpdStOrderDetail> {

    /**
     * 根据物料编码、实际数量、开始时间查询详情
     *
     * @param materialCode 物料编码
     * @param controlTaskNo 中控任务单号
     * @return 详情记录
     */
    TMpdStOrderDetail selectByMaterialCodeAndQuantityAndTime(
        @Param("materialCode") String materialCode,
        @Param("controlTaskNo") String controlTaskNo,
        @Param("dateStr") String dateStr);
}
