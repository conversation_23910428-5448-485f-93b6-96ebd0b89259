package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO;
import com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordQueryDTO;
import org.springframework.data.domain.Page;

import java.util.Date;
import java.util.List;

/**
 * 筒仓倒仓任务记录服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @Description: 筒仓倒仓任务记录服务接口
 * @Date: 2025-01-17
 */
public interface SiloTransferRecordService {

    /**
     * 创建倒仓任务记录
     * @param dto 倒仓任务记录DTO
     * @return 记录ID
     */
    String createSiloTransferRecord(SiloTransferRecordDTO dto);

    /**
     * 开始倒仓任务
     * @param taskNo 任务号
     * @param startTime 开始时间
     * @return 是否成功
     */
    Boolean startSiloTransferRecord(String taskNo, Date startTime);

    /**
     * 根据中控任务ID开始倒仓任务
     * @param centralControlTaskId 中控任务ID
     * @param startTime 开始时间
     * @return 是否成功
     */
    Boolean startSiloTransferRecordByTaskId(String centralControlTaskId, Date startTime);

    /**
     * 完成倒仓任务记录
     * @param taskNo 任务号
     * @param completionTime 完成时间
     * @return 是否成功
     */
    Boolean completeSiloTransferRecord(String taskNo, Date completionTime);

    /**
     * 根据中控任务ID完成倒仓任务记录
     * @param centralControlTaskId 中控任务ID
     * @param completionTime 完成时间
     * @return 是否成功
     */
    Boolean completeSiloTransferRecordByTaskId(String centralControlTaskId, Date completionTime);

    /**
     * 标记倒仓任务记录为失败
     * @param taskNo 任务号
     * @param failureReason 失败原因
     * @return 是否成功
     */
    Boolean failSiloTransferRecord(String taskNo, String failureReason);

    /**
     * 根据中控任务ID标记倒仓任务记录为失败
     * @param centralControlTaskId 中控任务ID
     * @param failureReason 失败原因
     * @return 是否成功
     */
    Boolean failSiloTransferRecordByTaskId(String centralControlTaskId, String failureReason);

    /**
     * 取消倒仓任务记录
     * @param taskNo 任务号
     * @param cancelReason 取消原因
     * @return 是否成功
     */
    Boolean cancelSiloTransferRecord(String taskNo, String cancelReason);

    /**
     * 更新倒仓进度
     * @param taskNo 任务号
     * @param progressPercentage 进度百分比
     * @param actualWeight 实际重量
     * @return 是否成功
     */
    Boolean updateSiloTransferProgress(String taskNo, Integer progressPercentage, Double actualWeight);

    /**
     * 分页查询倒仓任务记录
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<SiloTransferRecordDTO> querySiloTransferRecords(SiloTransferRecordQueryDTO queryDTO);

    /**
     * 根据ID查询倒仓任务记录
     * @param id 记录ID
     * @return 倒仓任务记录
     */
    SiloTransferRecordDTO getSiloTransferRecordById(Integer id);

    /**
     * 根据任务号查询倒仓任务记录
     * @param taskNo 任务号
     * @return 倒仓任务记录
     */
    SiloTransferRecordDTO getSiloTransferRecordByTaskNo(String taskNo);

    /**
     * 根据中控任务ID查询倒仓任务记录
     * @param centralControlTaskId 中控任务ID
     * @return 倒仓任务记录
     */
    SiloTransferRecordDTO getSiloTransferRecordByTaskId(String centralControlTaskId);

    /**
     * 根据唯一标识查询倒仓任务记录
     * @param uniqueId 唯一标识
     * @return 倒仓任务记录
     */
    SiloTransferRecordDTO getSiloTransferRecordByUniqueId(String uniqueId);

    /**
     * 查询执行中的倒仓任务记录
     * @return 执行中的倒仓任务记录列表
     */
    List<SiloTransferRecordDTO> getInProgressRecords();

    /**
     * 查询待执行的倒仓任务记录
     * @return 待执行的倒仓任务记录列表
     */
    List<SiloTransferRecordDTO> getPendingRecords();

    /**
     * 根据筒仓号查询最新的倒仓任务记录（作为源筒仓）
     * @param sourceSiloNo 源筒仓号
     * @return 最新的倒仓任务记录
     */
    SiloTransferRecordDTO getLatestSiloTransferRecordBySourceSilo(String sourceSiloNo);

    /**
     * 根据筒仓号查询最新的倒仓任务记录（作为目标筒仓）
     * @param targetSiloNo 目标筒仓号
     * @return 最新的倒仓任务记录
     */
    SiloTransferRecordDTO getLatestSiloTransferRecordByTargetSilo(String targetSiloNo);

    /**
     * 根据筒仓号查询最新的倒仓任务记录（源或目标）
     * @param siloNo 筒仓号
     * @return 最新的倒仓任务记录
     */
    SiloTransferRecordDTO getLatestSiloTransferRecordBySilo(String siloNo);

    /**
     * 根据源筒仓和状态查询倒仓任务记录
     * @param sourceSiloNo 源筒仓号
     * @param status 状态
     * @return 倒仓任务记录列表
     */
    List<SiloTransferRecordDTO> getSiloTransferRecordsBySourceSiloAndStatus(String sourceSiloNo, Integer status);

    /**
     * 根据目标筒仓和状态查询倒仓任务记录
     * @param targetSiloNo 目标筒仓号
     * @param status 状态
     * @return 倒仓任务记录列表
     */
    List<SiloTransferRecordDTO> getSiloTransferRecordsByTargetSiloAndStatus(String targetSiloNo, Integer status);

    /**
     * 根据筒仓和状态查询倒仓任务记录（源或目标）
     * @param siloNo 筒仓号
     * @param status 状态
     * @return 倒仓任务记录列表
     */
    List<SiloTransferRecordDTO> getSiloTransferRecordsBySiloAndStatus(String siloNo, Integer status);

    /**
     * 删除倒仓任务记录
     * @param id 记录ID
     * @return 是否成功
     */
    Boolean deleteSiloTransferRecord(Integer id);

    /**
     * 更新倒仓任务记录
     * @param dto 倒仓任务记录DTO
     * @return 是否成功
     */
    Boolean updateSiloTransferRecord(SiloTransferRecordDTO dto);

    /**
     * 处理倒仓任务业务逻辑
     * @param sourceSiloNo 源筒仓号
     * @param targetSiloNo 目标筒仓号
     * @param materialCode 物料编码
     * @param actualWeight 倒仓重量
     * @return 是否成功
     */
    Boolean processSiloTransfer(String sourceSiloNo, String targetSiloNo, String materialCode, Double actualWeight);

    /**
     * 处理库存更新
     * @param sourceSiloNo 源筒仓号
     * @param targetSiloNo 目标筒仓号
     * @param materialCode 物料编码
     * @param actualWeight 倒仓重量
     * @return 是否成功
     */
    Boolean processInventoryUpdate(String sourceSiloNo, String targetSiloNo, String materialCode, Double actualWeight);

    /**
     * 统计指定状态的记录数量
     * @param status 状态
     * @return 记录数量
     */
    Long countByStatus(Integer status);

    /**
     * 统计指定筒仓的记录数量（作为源筒仓）
     * @param sourceSiloNo 源筒仓号
     * @return 记录数量
     */
    Long countBySourceSiloNo(String sourceSiloNo);

    /**
     * 统计指定筒仓的记录数量（作为目标筒仓）
     * @param targetSiloNo 目标筒仓号
     * @return 记录数量
     */
    Long countByTargetSiloNo(String targetSiloNo);

    /**
     * 统计指定筒仓的记录数量（源或目标）
     * @param siloNo 筒仓号
     * @return 记录数量
     */
    Long countBySiloNo(String siloNo);

    /**
     * 检查任务号是否存在
     * @param taskNo 任务号
     * @return 是否存在
     */
    Boolean existsByTaskNo(String taskNo);

    /**
     * 检查唯一标识是否存在
     * @param uniqueId 唯一标识
     * @return 是否存在
     */
    Boolean existsByUniqueId(String uniqueId);

    /**
     * 检查筒仓是否有进行中的倒仓任务
     * @param siloNo 筒仓号
     * @return 是否存在进行中的任务
     */
    Boolean existsInProgressTaskBySiloNo(String siloNo);
}
