package com.hvisions.rawmaterial.service.sorghum;


import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailUpdateDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.detail.SorghumTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱转运工单
 * @date 2022/4/22 10:18
 */
public interface SorghumTransferOrderService {

    Page<SorghumTransferOrderPageDTO> getSorghumTransferOrderPageList(SorghumTransferOrderPageQueryDTO queryDTO);

    Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS);


    Integer detailUpdate(DetailUpdateDTO updateDTO);

    Page<SorghumTransferDetailListDTO> getSorghumTransferOrderDetailPageList(SorghumTransferOrderPageQueryDTO queryDTO);

    /**
     * 创建高粱转运工单
     * @param taskDTO
     * @return
     */
    String createSorghumTransfer(UnifiedTaskDTO taskDTO);
}
