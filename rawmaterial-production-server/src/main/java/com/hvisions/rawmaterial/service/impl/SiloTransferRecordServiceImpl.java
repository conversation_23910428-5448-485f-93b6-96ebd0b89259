package com.hvisions.rawmaterial.service.impl;

import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.consts.SiloTransferConstants;
import com.hvisions.rawmaterial.dao.SiloTransferRecordRepository;
import com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO;
import com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordQueryDTO;
import com.hvisions.rawmaterial.entity.silotransfer.TMpdSiloTransferRecord;
import com.hvisions.rawmaterial.mapper.SiloTransferRecordMapper;
import com.hvisions.rawmaterial.service.SiloTransferRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 筒仓倒仓任务记录服务实现类
 * 
 * <AUTHOR>
 * @version 1.0
 * @Description: 筒仓倒仓任务记录服务实现类
 * @Date: 2025-01-17
 */
@Slf4j
@Service
public class SiloTransferRecordServiceImpl implements SiloTransferRecordService {

    @Autowired
    private SiloTransferRecordRepository siloTransferRecordRepository;

    @Resource
    private SiloTransferRecordMapper siloTransferRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSiloTransferRecord(SiloTransferRecordDTO dto) {
        /**
         * 1.查询任务号或中控系统任务ID是否存在 - 存在则更新，不存在则添加
         * 2.处理倒仓任务逻辑
         */

        TMpdSiloTransferRecord record;
        boolean isUpdate = false;

        // 检查任务号是否存在
        if (dto.getTaskNo() != null && !dto.getTaskNo().trim().isEmpty()) {
            Optional<TMpdSiloTransferRecord> existingRecordOpt = siloTransferRecordRepository.findByTaskNo(dto.getTaskNo());
            if (existingRecordOpt.isPresent()) {
                // 存在则更新
                record = existingRecordOpt.get();
                isUpdate = true;
                log.info("找到已存在的倒仓任务记录，将进行更新，任务号：{}，原记录ID：{}", 
                        dto.getTaskNo(), record.getId());
                
                // 更新字段
                updateRecordFields(record, dto);
            } else {
                // 检查中控系统任务ID是否存在
                if (dto.getCentralControlTaskId() != null && !dto.getCentralControlTaskId().trim().isEmpty()) {
                    Optional<TMpdSiloTransferRecord> taskIdRecordOpt = siloTransferRecordRepository.findByCentralControlTaskId(dto.getCentralControlTaskId());
                    if (taskIdRecordOpt.isPresent()) {
                        record = taskIdRecordOpt.get();
                        isUpdate = true;
                        log.info("找到已存在的倒仓任务记录（通过中控任务ID），将进行更新，中控任务ID：{}，原记录ID：{}", 
                                dto.getCentralControlTaskId(), record.getId());
                        updateRecordFields(record, dto);
                    } else {
                        // 不存在则新建
                        record = new TMpdSiloTransferRecord();
                        BeanUtils.copyProperties(dto, record);
                    }
                } else {
                    // 不存在则新建
                    record = new TMpdSiloTransferRecord();
                    BeanUtils.copyProperties(dto, record);
                }
            }
        } else {
            // 没有任务号，直接新建
            record = new TMpdSiloTransferRecord();
            BeanUtils.copyProperties(dto, record);
        }

        // 设置开始时间（仅在新建时或开始时间为空时）
        if (!isUpdate && record.getStartTime() == null) {
            record.setStartTime(new Date());
        }

        // 设置初始状态为待执行（仅在新建时或状态为空时）
        if (!isUpdate && record.getStatus() == null) {
            record.setStatus(SiloTransferConstants.STATUS_PENDING);
        }

        // 设置初始进度为0%（仅在新建时或进度为空时）
        if (!isUpdate && record.getProgressPercentage() == null) {
            record.setProgressPercentage(BigDecimal.ZERO);
        }

        record = siloTransferRecordRepository.save(record);
        
        if (isUpdate) {
            log.info("更新倒仓任务记录成功，任务号：{}，源筒仓：{}，目标筒仓：{}", 
                    record.getTaskNo(), record.getSourceSiloNo(), record.getTargetSiloNo());
        } else {
            log.info("创建倒仓任务记录成功，任务号：{}，源筒仓：{}，目标筒仓：{}", 
                    record.getTaskNo(), record.getSourceSiloNo(), record.getTargetSiloNo());
        }

        // 处理倒仓任务业务逻辑
        if (record.getSourceSiloNo() != null && record.getTargetSiloNo() != null && record.getMaterialCode() != null) {
            Double actualWeight = record.getActualWeight() != null ? record.getActualWeight().doubleValue() : null;
            processSiloTransfer(record.getSourceSiloNo(), record.getTargetSiloNo(), record.getMaterialCode(), actualWeight);
        }

        return record.getId().toString();
    }

    /**
     * 更新记录字段
     */
    private void updateRecordFields(TMpdSiloTransferRecord record, SiloTransferRecordDTO dto) {
        if (dto.getBusinessSystem() != null) record.setBusinessSystem(dto.getBusinessSystem());
        if (dto.getMaterialType() != null) record.setMaterialType(dto.getMaterialType());
        if (dto.getTaskType() != null) record.setTaskType(dto.getTaskType());
        if (dto.getDataType() != null) record.setDataType(dto.getDataType());
        if (dto.getSourceSiloNo() != null) record.setSourceSiloNo(dto.getSourceSiloNo());
        if (dto.getSourceSiloName() != null) record.setSourceSiloName(dto.getSourceSiloName());
        if (dto.getTargetSiloNo() != null) record.setTargetSiloNo(dto.getTargetSiloNo());
        if (dto.getTargetSiloName() != null) record.setTargetSiloName(dto.getTargetSiloName());
        if (dto.getMaterialCode() != null) record.setMaterialCode(dto.getMaterialCode());
        if (dto.getMaterialName() != null) record.setMaterialName(dto.getMaterialName());
        if (dto.getUnit() != null) record.setUnit(dto.getUnit());
        if (dto.getActualWeight() != null) record.setActualWeight(dto.getActualWeight());
        if (dto.getPlannedWeight() != null) record.setPlannedWeight(dto.getPlannedWeight());
        if (dto.getStartTime() != null) record.setStartTime(dto.getStartTime());
        if (dto.getEndTime() != null) record.setEndTime(dto.getEndTime());
        if (dto.getStatus() != null) record.setStatus(dto.getStatus());
        if (dto.getExecutorId() != null) record.setExecutorId(dto.getExecutorId());
        if (dto.getExecutorName() != null) record.setExecutorName(dto.getExecutorName());
        if (dto.getCompletionTime() != null) record.setCompletionTime(dto.getCompletionTime());
        if (dto.getCentralControlTaskId() != null) record.setCentralControlTaskId(dto.getCentralControlTaskId());
        if (dto.getWorkshopCode() != null) record.setWorkshopCode(dto.getWorkshopCode());
        if (dto.getWorkshopName() != null) record.setWorkshopName(dto.getWorkshopName());
        if (dto.getCenterCode() != null) record.setCenterCode(dto.getCenterCode());
        if (dto.getCenterName() != null) record.setCenterName(dto.getCenterName());
        if (dto.getRemark() != null) record.setRemark(dto.getRemark());
        if (dto.getDataSource() != null) record.setDataSource(dto.getDataSource());
        if (dto.getUniqueId() != null) record.setUniqueId(dto.getUniqueId());
        if (dto.getProgressPercentage() != null) record.setProgressPercentage(dto.getProgressPercentage());
        if (dto.getFailureReason() != null) record.setFailureReason(dto.getFailureReason());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startSiloTransferRecord(String taskNo, Date startTime) {
        Optional<TMpdSiloTransferRecord> recordOpt = siloTransferRecordRepository.findByTaskNo(taskNo);
        if (!recordOpt.isPresent()) {
            log.warn("未找到任务号为{}的倒仓任务记录", taskNo);
            return false;
        }

        TMpdSiloTransferRecord record = recordOpt.get();
        
        // 检查状态是否可以开始
        if (!SiloTransferConstants.STATUS_PENDING.equals(record.getStatus())) {
            log.warn("倒仓任务记录状态不是待执行，无法开始，任务号：{}，当前状态：{}", taskNo, record.getStatus());
            return false;
        }

        record.setStatus(SiloTransferConstants.STATUS_IN_PROGRESS);
        record.setStartTime(startTime);

        siloTransferRecordRepository.save(record);
        log.info("倒仓任务记录开始执行，任务号：{}，源筒仓：{}，目标筒仓：{}", 
                record.getTaskNo(), record.getSourceSiloNo(), record.getTargetSiloNo());

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startSiloTransferRecordByTaskId(String centralControlTaskId, Date startTime) {
        Optional<TMpdSiloTransferRecord> recordOpt = siloTransferRecordRepository.findByCentralControlTaskId(centralControlTaskId);
        if (!recordOpt.isPresent()) {
            log.warn("未找到中控任务ID为{}的倒仓任务记录", centralControlTaskId);
            return false;
        }

        TMpdSiloTransferRecord record = recordOpt.get();
        
        // 检查状态是否可以开始
        if (!SiloTransferConstants.STATUS_PENDING.equals(record.getStatus())) {
            log.warn("倒仓任务记录状态不是待执行，无法开始，中控任务ID：{}，当前状态：{}", centralControlTaskId, record.getStatus());
            return false;
        }

        record.setStatus(SiloTransferConstants.STATUS_IN_PROGRESS);
        record.setStartTime(startTime);

        siloTransferRecordRepository.save(record);
        log.info("倒仓任务记录开始执行，中控任务ID：{}，源筒仓：{}，目标筒仓：{}", 
                centralControlTaskId, record.getSourceSiloNo(), record.getTargetSiloNo());

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeSiloTransferRecord(String taskNo, Date completionTime) {
        Optional<TMpdSiloTransferRecord> recordOpt = siloTransferRecordRepository.findByTaskNo(taskNo);
        if (!recordOpt.isPresent()) {
            log.warn("未找到任务号为{}的倒仓任务记录", taskNo);
            return false;
        }

        TMpdSiloTransferRecord record = recordOpt.get();
        record.setStatus(SiloTransferConstants.STATUS_COMPLETED);
        record.setCompletionTime(completionTime);
        record.setEndTime(completionTime);
        record.setProgressPercentage(new BigDecimal("100.00"));

        siloTransferRecordRepository.save(record);
        log.info("倒仓任务记录完成，任务号：{}，源筒仓：{}，目标筒仓：{}", 
                record.getTaskNo(), record.getSourceSiloNo(), record.getTargetSiloNo());

        // 处理库存更新
        Double actualWeight = record.getActualWeight() != null ? record.getActualWeight().doubleValue() : null;
        processInventoryUpdate(record.getSourceSiloNo(), record.getTargetSiloNo(), record.getMaterialCode(), actualWeight);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeSiloTransferRecordByTaskId(String centralControlTaskId, Date completionTime) {
        Optional<TMpdSiloTransferRecord> recordOpt = siloTransferRecordRepository.findByCentralControlTaskId(centralControlTaskId);
        if (!recordOpt.isPresent()) {
            log.warn("未找到中控任务ID为{}的倒仓任务记录", centralControlTaskId);
            return false;
        }

        TMpdSiloTransferRecord record = recordOpt.get();
        record.setStatus(SiloTransferConstants.STATUS_COMPLETED);
        record.setCompletionTime(completionTime);
        record.setEndTime(completionTime);
        record.setProgressPercentage(new BigDecimal("100.00"));

        siloTransferRecordRepository.save(record);
        log.info("倒仓任务记录完成，中控任务ID：{}，源筒仓：{}，目标筒仓：{}", 
                centralControlTaskId, record.getSourceSiloNo(), record.getTargetSiloNo());

        // 处理库存更新
        Double actualWeight = record.getActualWeight() != null ? record.getActualWeight().doubleValue() : null;
        processInventoryUpdate(record.getSourceSiloNo(), record.getTargetSiloNo(), record.getMaterialCode(), actualWeight);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean failSiloTransferRecord(String taskNo, String failureReason) {
        Optional<TMpdSiloTransferRecord> recordOpt = siloTransferRecordRepository.findByTaskNo(taskNo);
        if (!recordOpt.isPresent()) {
            log.warn("未找到任务号为{}的倒仓任务记录", taskNo);
            return false;
        }

        TMpdSiloTransferRecord record = recordOpt.get();
        record.setStatus(SiloTransferConstants.STATUS_FAILED);
        record.setFailureReason(failureReason);
        record.setEndTime(new Date());

        siloTransferRecordRepository.save(record);
        log.info("倒仓任务记录标记为失败，任务号：{}，失败原因：{}", taskNo, failureReason);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean failSiloTransferRecordByTaskId(String centralControlTaskId, String failureReason) {
        Optional<TMpdSiloTransferRecord> recordOpt = siloTransferRecordRepository.findByCentralControlTaskId(centralControlTaskId);
        if (!recordOpt.isPresent()) {
            log.warn("未找到中控任务ID为{}的倒仓任务记录", centralControlTaskId);
            return false;
        }

        TMpdSiloTransferRecord record = recordOpt.get();
        record.setStatus(SiloTransferConstants.STATUS_FAILED);
        record.setFailureReason(failureReason);
        record.setEndTime(new Date());

        siloTransferRecordRepository.save(record);
        log.info("倒仓任务记录标记为失败，中控任务ID：{}，失败原因：{}", centralControlTaskId, failureReason);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelSiloTransferRecord(String taskNo, String cancelReason) {
        Optional<TMpdSiloTransferRecord> recordOpt = siloTransferRecordRepository.findByTaskNo(taskNo);
        if (!recordOpt.isPresent()) {
            log.warn("未找到任务号为{}的倒仓任务记录", taskNo);
            return false;
        }

        TMpdSiloTransferRecord record = recordOpt.get();
        
        // 检查状态是否可以取消
        if (!SiloTransferConstants.canCancel(record.getStatus())) {
            log.warn("倒仓任务记录状态不允许取消，任务号：{}，当前状态：{}", taskNo, record.getStatus());
            return false;
        }

        record.setStatus(SiloTransferConstants.STATUS_CANCELLED);
        record.setRemark(record.getRemark() == null ? cancelReason : record.getRemark() + "; " + cancelReason);
        record.setEndTime(new Date());

        siloTransferRecordRepository.save(record);
        log.info("倒仓任务记录已取消，任务号：{}，取消原因：{}", taskNo, cancelReason);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSiloTransferProgress(String taskNo, Integer progressPercentage, Double actualWeight) {
        Optional<TMpdSiloTransferRecord> recordOpt = siloTransferRecordRepository.findByTaskNo(taskNo);
        if (!recordOpt.isPresent()) {
            log.warn("未找到任务号为{}的倒仓任务记录", taskNo);
            return false;
        }

        TMpdSiloTransferRecord record = recordOpt.get();
        
        if (progressPercentage != null) {
            record.setProgressPercentage(new BigDecimal(progressPercentage));
        }
        
        if (actualWeight != null) {
            record.setActualWeight(new BigDecimal(actualWeight));
        }

        siloTransferRecordRepository.save(record);
        log.debug("更新倒仓任务进度，任务号：{}，进度：{}%，实际重量：{}", taskNo, progressPercentage, actualWeight);

        return true;
    }

    @Override
    public Page<SiloTransferRecordDTO> querySiloTransferRecords(SiloTransferRecordQueryDTO queryDTO) {
        return PageHelperUtil.getPage(siloTransferRecordMapper::querySiloTransferRecords, queryDTO, SiloTransferRecordDTO.class);
    }

    @Override
    public SiloTransferRecordDTO getSiloTransferRecordById(Integer id) {
        Optional<TMpdSiloTransferRecord> recordOpt = siloTransferRecordRepository.findById(id);
        if (!recordOpt.isPresent()) {
            throw new IllegalArgumentException("倒仓任务记录不存在");
        }
        return convertToDTO(recordOpt.get());
    }

    @Override
    public SiloTransferRecordDTO getSiloTransferRecordByTaskNo(String taskNo) {
        SiloTransferRecordDTO dto = siloTransferRecordMapper.getSiloTransferRecordByTaskNo(taskNo);
        if (dto == null) {
            throw new IllegalArgumentException("倒仓任务记录不存在");
        }
        return dto;
    }

    @Override
    public SiloTransferRecordDTO getSiloTransferRecordByTaskId(String centralControlTaskId) {
        SiloTransferRecordDTO dto = siloTransferRecordMapper.getSiloTransferRecordByTaskId(centralControlTaskId);
        if (dto == null) {
            throw new IllegalArgumentException("倒仓任务记录不存在");
        }
        return dto;
    }

    @Override
    public SiloTransferRecordDTO getSiloTransferRecordByUniqueId(String uniqueId) {
        SiloTransferRecordDTO dto = siloTransferRecordMapper.getSiloTransferRecordByUniqueId(uniqueId);
        if (dto == null) {
            throw new IllegalArgumentException("倒仓任务记录不存在");
        }
        return dto;
    }

    @Override
    public List<SiloTransferRecordDTO> getInProgressRecords() {
        return siloTransferRecordMapper.getInProgressRecords();
    }

    @Override
    public List<SiloTransferRecordDTO> getPendingRecords() {
        return siloTransferRecordMapper.getPendingRecords();
    }

    @Override
    public SiloTransferRecordDTO getLatestSiloTransferRecordBySourceSilo(String sourceSiloNo) {
        return siloTransferRecordMapper.getLatestSiloTransferRecordBySourceSilo(sourceSiloNo);
    }

    @Override
    public SiloTransferRecordDTO getLatestSiloTransferRecordByTargetSilo(String targetSiloNo) {
        return siloTransferRecordMapper.getLatestSiloTransferRecordByTargetSilo(targetSiloNo);
    }

    @Override
    public SiloTransferRecordDTO getLatestSiloTransferRecordBySilo(String siloNo) {
        return siloTransferRecordMapper.getLatestSiloTransferRecordBySilo(siloNo);
    }

    @Override
    public List<SiloTransferRecordDTO> getSiloTransferRecordsBySourceSiloAndStatus(String sourceSiloNo, Integer status) {
        return siloTransferRecordMapper.getSiloTransferRecordsBySourceSiloAndStatus(sourceSiloNo, status);
    }

    @Override
    public List<SiloTransferRecordDTO> getSiloTransferRecordsByTargetSiloAndStatus(String targetSiloNo, Integer status) {
        return siloTransferRecordMapper.getSiloTransferRecordsByTargetSiloAndStatus(targetSiloNo, status);
    }

    @Override
    public List<SiloTransferRecordDTO> getSiloTransferRecordsBySiloAndStatus(String siloNo, Integer status) {
        return siloTransferRecordMapper.getSiloTransferRecordsBySiloAndStatus(siloNo, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteSiloTransferRecord(Integer id) {
        if (!siloTransferRecordRepository.existsById(id)) {
            throw new IllegalArgumentException("倒仓任务记录不存在");
        }
        siloTransferRecordRepository.deleteById(id);
        log.info("删除倒仓任务记录成功，ID：{}", id);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSiloTransferRecord(SiloTransferRecordDTO dto) {
        if (dto.getId() == null) {
            throw new IllegalArgumentException("记录ID不能为空");
        }
        
        Optional<TMpdSiloTransferRecord> recordOpt = siloTransferRecordRepository.findById(Integer.valueOf(dto.getId()));
        if (!recordOpt.isPresent()) {
            throw new IllegalArgumentException("倒仓任务记录不存在");
        }

        TMpdSiloTransferRecord record = recordOpt.get();
        updateRecordFields(record, dto);
        
        siloTransferRecordRepository.save(record);
        log.info("更新倒仓任务记录成功，ID：{}", dto.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processSiloTransfer(String sourceSiloNo, String targetSiloNo, String materialCode, Double actualWeight) {
        log.info("处理倒仓任务，源筒仓：{}，目标筒仓：{}，物料：{}，重量：{}", sourceSiloNo, targetSiloNo, materialCode, actualWeight);

        try {
            // 这里可以调用其他服务处理倒仓逻辑
            // 例如：检查筒仓状态、验证物料兼容性、启动倒仓设备等

            // 查询相关的倒仓任务记录
            List<SiloTransferRecordDTO> records = siloTransferRecordMapper.getSiloTransferRecordsBySiloAndStatus(sourceSiloNo, SiloTransferConstants.STATUS_IN_PROGRESS);

            log.info("倒仓任务处理完成，源筒仓：{}，目标筒仓：{}，影响记录数：{}", sourceSiloNo, targetSiloNo, records.size());
            return true;
        } catch (Exception e) {
            log.error("倒仓任务处理失败，源筒仓：{}，目标筒仓：{}，错误：{}", sourceSiloNo, targetSiloNo, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processInventoryUpdate(String sourceSiloNo, String targetSiloNo, String materialCode, Double actualWeight) {
        log.info("处理库存更新，源筒仓：{}，目标筒仓：{}，物料：{}，重量：{}", sourceSiloNo, targetSiloNo, materialCode, actualWeight);

        try {
            // 这里可以调用库存服务进行更新
            // 例如：减少源筒仓库存、增加目标筒仓库存、生成库存变更记录等

            // 获取最新的倒仓任务记录
            SiloTransferRecordDTO latestRecord = siloTransferRecordMapper.getLatestSiloTransferRecordBySilo(sourceSiloNo);

            if (latestRecord != null) {
                log.info("库存更新处理完成，源筒仓：{}，目标筒仓：{}，最新倒仓记录：{}", sourceSiloNo, targetSiloNo, latestRecord.getTaskNo());
            }

            return true;
        } catch (Exception e) {
            log.error("库存更新处理失败，源筒仓：{}，目标筒仓：{}，错误：{}", sourceSiloNo, targetSiloNo, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Long countByStatus(Integer status) {
        return siloTransferRecordMapper.countByStatus(status);
    }

    @Override
    public Long countBySourceSiloNo(String sourceSiloNo) {
        return siloTransferRecordMapper.countBySourceSiloNo(sourceSiloNo);
    }

    @Override
    public Long countByTargetSiloNo(String targetSiloNo) {
        return siloTransferRecordMapper.countByTargetSiloNo(targetSiloNo);
    }

    @Override
    public Long countBySiloNo(String siloNo) {
        return siloTransferRecordMapper.countBySiloNo(siloNo);
    }

    @Override
    public Boolean existsByTaskNo(String taskNo) {
        return siloTransferRecordMapper.existsByTaskNo(taskNo) > 0;
    }

    @Override
    public Boolean existsByUniqueId(String uniqueId) {
        return siloTransferRecordMapper.existsByUniqueId(uniqueId) > 0;
    }

    @Override
    public Boolean existsInProgressTaskBySiloNo(String siloNo) {
        return siloTransferRecordMapper.existsInProgressTaskBySiloNo(siloNo) > 0;
    }

    /**
     * 转换实体为DTO
     * @param record 倒仓任务记录实体
     * @return 倒仓任务记录DTO
     */
    private SiloTransferRecordDTO convertToDTO(TMpdSiloTransferRecord record) {
        SiloTransferRecordDTO dto = new SiloTransferRecordDTO();
        BeanUtils.copyProperties(record, dto);

        // 设置状态描述
        if (record.getStatus() != null) {
            dto.setStatusDesc(SiloTransferConstants.getStatusDesc(record.getStatus()));
        }

        // 计算持续时间
        if (record.getStartTime() != null && record.getEndTime() != null) {
            long durationMillis = record.getEndTime().getTime() - record.getStartTime().getTime();
            dto.setDurationMinutes(durationMillis / (1000 * 60));
        }

        // 计算倒仓效率
        if (record.getActualWeight() != null && dto.getDurationMinutes() != null && dto.getDurationMinutes() > 0) {
            BigDecimal efficiency = record.getActualWeight().divide(new BigDecimal(dto.getDurationMinutes()), 2, BigDecimal.ROUND_HALF_UP);
            dto.setTransferEfficiency(efficiency);
        }

        return dto;
    }
}
