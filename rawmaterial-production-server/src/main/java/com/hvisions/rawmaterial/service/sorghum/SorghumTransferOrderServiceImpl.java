package com.hvisions.rawmaterial.service.sorghum;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.SorghumTransferOrderMapper;
import com.hvisions.rawmaterial.dao.StOrderDetailMapper;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailUpdateDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.detail.SorghumTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdSorghumTransferOrder;
import com.hvisions.rawmaterial.entity.TMpdStOrderDetail;
import com.hvisions.rawmaterial.service.RlManagementService;
import com.hvisions.rawmaterial.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱转运工单
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class SorghumTransferOrderServiceImpl implements SorghumTransferOrderService {

    @Resource
    private SorghumTransferOrderMapper sorghumTransferOrderMapper;

    @Resource
    private StOrderDetailMapper stOrderDetailMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Override
    public Page<SorghumTransferOrderPageDTO> getSorghumTransferOrderPageList(SorghumTransferOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumTransferOrderMapper::getSorghumTransferOrderPageList, queryDTO, SorghumTransferOrderPageDTO.class);
    }

    @Override
    @Transactional
    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
        int res = 0;
        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
            TMpdSorghumTransferOrder order = sorghumTransferOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());
            BigDecimal count = new BigDecimal(0);
            List<TMpdStOrderDetail> stOrderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdStOrderDetail.class);
            for (TMpdStOrderDetail orderDetail : stOrderDetails) {
                orderDetail.setCreateTime(new Date());
                orderDetail.setState("1");
                orderDetail.setIsManual("1");
                // 执行出入仓操作
                String batch = null;//rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
                orderDetail.setBatch(batch);
                res += stOrderDetailMapper.insert(orderDetail);

                count = count.add(orderDetail.getActualQuantity());

            }
            // 更新工单实际数量
            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
            res += sorghumTransferOrderMapper.updateById(order);
        }
        return res;
    }

    /**
     * 修改高粱转运工单详情
     * @param updateDTO
     * @return
     */
    @Override
    @Transactional
    public Integer detailUpdate(DetailUpdateDTO updateDTO) {
        TMpdStOrderDetail tMpdStOrderDetail = stOrderDetailMapper.selectById(updateDTO.getId());
        if (tMpdStOrderDetail == null) {
            throw new BaseKnownException(10000, "获取数据有误，id：" + updateDTO.getId());
        }
        //修正的差值
        BigDecimal subtract = updateDTO.getActualQuantity().subtract(tMpdStOrderDetail.getActualQuantity());
        TMpdStOrderDetail update = new TMpdStOrderDetail();
        update.setId(updateDTO.getId());
        update.setActualQuantity(updateDTO.getActualQuantity());
        //更新订单的重量
        TMpdSorghumTransferOrder order = sorghumTransferOrderMapper.selectById(tMpdStOrderDetail.getOrderId());
        TMpdSorghumTransferOrder updateOrder = new TMpdSorghumTransferOrder();
        updateOrder.setId(order.getId());
        updateOrder.setActualQuantity(order.getActualQuantity().add(subtract));
        sorghumTransferOrderMapper.updateById(updateOrder);
        return stOrderDetailMapper.updateById(update);
    }

    @Override
    public Page<SorghumTransferDetailListDTO> getSorghumTransferOrderDetailPageList(SorghumTransferOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumTransferOrderMapper::getSorghumTransferOrderDetailPageList, queryDTO, SorghumTransferDetailListDTO.class);
    }

    /**
     * UnifiedTaskDTO 对象转换为TMpdSorghumTransferOrder对象
     * @param taskDTO
     * @return
     */
    private TMpdSorghumTransferOrder createTMpdSorghumTransferOrder(UnifiedTaskDTO taskDTO) {
        TMpdSorghumTransferOrder record = new TMpdSorghumTransferOrder();
        BeanUtils.copyProperties(taskDTO, record);
        return record;
    }


    @Override
    public String createSorghumTransfer(UnifiedTaskDTO dto) {
        /**
         * 1.查询任务号或中控系统任务ID是否存在 - 存在则更新，不存在则添加
         * 2.处理高粱转运工单逻辑
         */
        Date recordDate = dto.getStartTime(); // 使用记录的开始时间作为任务日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (recordDate == null){
            recordDate = new Date();
        }
        String dateStr = sdf.format(recordDate);

        boolean isUpdate = isRecordExists(dto.getMaterialCode(), dto.getTaskNo(),dateStr);
        // 检查日期+物料编码 当日任务有责需进行更新无则进行添加
        TMpdSorghumTransferOrder record;

        record = findOrderByDateAndMaterialCode(dateStr, dto.getMaterialCode());
        if (isUpdate){
            log.info("找到已存在的任务，将进行更新，任务号：{}", dto.getTaskNo());
            updateSorghumTransferOrder(dto);
        }else{
            log.info("未找到已存在的任务，将进行添加，任务号：{}", dto.getTaskNo());

        }

        //插入详情
        createOrderDetail(record.getId(), dto);
        return record.getId().toString();
    }

    /**
     * 更新记录字段
     */
    private void updateRecordFields(TMpdSorghumTransferOrder record, UnifiedTaskDTO dto) {
        if (dto.getMaterialCode() != null) record.setMaterialCode(dto.getMaterialCode());
        if (dto.getMaterialName() != null) record.setMaterialName(dto.getMaterialName());
        if (dto.getUnit() != null) record.setUnit(dto.getUnit());
        if (dto.getStatus() != null) record.setStatus(dto.getStatus());
    }

    /**
     * 检查记录是否已存在（根据物料编码、实际数量、开始时间判断）
     */
    private boolean isRecordExists(String materialCode, String controlTaskNo, String dateStr) {
        TMpdStOrderDetail existing = stOrderDetailMapper.selectByMaterialCodeAndQuantityAndTime(
            materialCode,
            controlTaskNo,
                dateStr
        );
        return existing != null;
    }

    /**
     * 根据日期和物料编码查找转运工单
     */
    private TMpdSorghumTransferOrder findOrderByDateAndMaterialCode(String dateStr, String materialCode) {
        return sorghumTransferOrderMapper.selectByDateAndMaterialCode(dateStr, materialCode);
    }

    /**
     * 创建工单详情
     */
    private void createOrderDetail(Integer orderId, UnifiedTaskDTO record) {
        TMpdStOrderDetail detail = new TMpdStOrderDetail();
        detail.setOrderId(orderId);
        detail.setSendSiloName(record.getSourceSiloName());
        detail.setAcceptSiloName(record.getDestinationSiloName());
        detail.setMaterialName(record.getMaterialName());
        detail.setMaterialCode(record.getMaterialCode());
        detail.setUnit(record.getUnit());
        detail.setActualQuantity(record.getActualQuantity());
        detail.setActualBeginTime(record.getActualStartTime());
        detail.setActualEndTime(record.getActualEndTime());
        detail.setState("1"); // 已完成
        detail.setIsManual("0"); // 自动（中控）
        detail.setCreatorName("中控系统");
        detail.setDeleted(false);
        detail.setCreateTime(new Date());
        detail.setUpdateTime(new Date());

        stOrderDetailMapper.insert(detail);
    }

    /**
     * 生成工单号
     * 规则：GD + 年月日 + 三位流水号
     */
    private String generateOrderNo(Date orderDate) {
        if (orderDate == null) {
            orderDate = new Date();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        String dateStr = sdf.format(orderDate);

        // 查询当天最大流水号
        String maxSerialNo = sorghumTransferOrderMapper.selectMaxSerialNoByDate(orderDate);
        int serialNo = 1;
        if (maxSerialNo != null && !maxSerialNo.isEmpty()) {
            serialNo = Integer.parseInt(maxSerialNo) + 1;
        }

        return String.format("GD%s%03d", dateStr, serialNo);
    }

    /**
     * 获取当前库存数量（汇总的前处理存储仓的总库存）
     */
    private BigDecimal getCurrentInventoryQuantity(String materialCode) {
        // 这里应该调用库存管理服务获取实际库存
        // 暂时返回模拟数据
        return new BigDecimal("1000000");
    }
}
