package com.hvisions.rawmaterial.service.sorghum;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.SorghumTransferOrderMapper;
import com.hvisions.rawmaterial.dao.StOrderDetailMapper;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailSummaryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailUpdateDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.detail.SorghumTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdSorghumTransferOrder;
import com.hvisions.rawmaterial.entity.TMpdStOrderDetail;
import com.hvisions.rawmaterial.service.RlManagementService;
import com.hvisions.rawmaterial.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱转运工单
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class SorghumTransferOrderServiceImpl implements SorghumTransferOrderService {

    @Resource
    private SorghumTransferOrderMapper sorghumTransferOrderMapper;

    @Resource
    private StOrderDetailMapper stOrderDetailMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Override
    public Page<SorghumTransferOrderPageDTO> getSorghumTransferOrderPageList(SorghumTransferOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumTransferOrderMapper::getSorghumTransferOrderPageList, queryDTO, SorghumTransferOrderPageDTO.class);
    }

    @Override
    @Transactional
    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
        int res = 0;
        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
            TMpdSorghumTransferOrder order = sorghumTransferOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());
            BigDecimal count = new BigDecimal(0);
            List<TMpdStOrderDetail> stOrderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdStOrderDetail.class);
            for (TMpdStOrderDetail orderDetail : stOrderDetails) {
                orderDetail.setCreateTime(new Date());
                orderDetail.setState("1");
                orderDetail.setIsManual("1");
                // 执行出入仓操作
                String batch = null;//rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
                orderDetail.setBatch(batch);
                res += stOrderDetailMapper.insert(orderDetail);

                count = count.add(orderDetail.getActualQuantity());

            }
            // 更新工单实际数量
            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
            res += sorghumTransferOrderMapper.updateById(order);
        }
        return res;
    }

    /**
     * 修改高粱转运工单详情
     * @param updateDTO
     * @return
     */
    @Override
    @Transactional
    public Integer detailUpdate(DetailUpdateDTO updateDTO) {
        TMpdStOrderDetail tMpdStOrderDetail = stOrderDetailMapper.selectById(updateDTO.getId());
        if (tMpdStOrderDetail == null) {
            throw new BaseKnownException(10000, "获取数据有误，id：" + updateDTO.getId());
        }
        //修正的差值
        BigDecimal subtract = updateDTO.getActualQuantity().subtract(tMpdStOrderDetail.getActualQuantity());
        TMpdStOrderDetail update = new TMpdStOrderDetail();
        update.setId(updateDTO.getId());
        update.setActualQuantity(updateDTO.getActualQuantity());
        //更新订单的重量
        TMpdSorghumTransferOrder order = sorghumTransferOrderMapper.selectById(tMpdStOrderDetail.getOrderId());
        TMpdSorghumTransferOrder updateOrder = new TMpdSorghumTransferOrder();
        updateOrder.setId(order.getId());
        updateOrder.setActualQuantity(order.getActualQuantity().add(subtract));
        sorghumTransferOrderMapper.updateById(updateOrder);
        return stOrderDetailMapper.updateById(update);
    }

    @Override
    public Page<SorghumTransferDetailListDTO> getSorghumTransferOrderDetailPageList(SorghumTransferOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumTransferOrderMapper::getSorghumTransferOrderDetailPageList, queryDTO, SorghumTransferDetailListDTO.class);
    }

    /**
     * UnifiedTaskDTO 对象转换为TMpdSorghumTransferOrder对象
     * @param taskDTO
     * @return
     */
    private TMpdSorghumTransferOrder createTMpdSorghumTransferOrder(UnifiedTaskDTO taskDTO) {
        TMpdSorghumTransferOrder record = new TMpdSorghumTransferOrder();
        BeanUtils.copyProperties(taskDTO, record);
        return record;
    }


    @Override
    public String createSorghumTransfer(UnifiedTaskDTO dto) {
        /**
         * 1.先查看详情是否有数据，如果有更新
         * 2.如果详情没有，看主表有数据没，没有创建并更新主表数据
         * 3.详情没有新增
         */
        Date recordDate = dto.getStartTime(); // 使用记录的开始时间作为任务日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (recordDate == null){
            recordDate = new Date();
        }
        String dateStr = sdf.format(recordDate);

        // 1. 先查看详情是否有数据
        boolean detailExists = isRecordExists(dto.getMaterialCode(), dto.getTaskNo(), dateStr);
        TMpdSorghumTransferOrder record;

        if (detailExists) {
            // 详情存在，更新详情数据
            log.info("找到已存在的详情记录，将进行更新，任务号：{}", dto.getTaskNo());
            TMpdStOrderDetail existingDetail = stOrderDetailMapper.selectByMaterialCodeAndQuantityAndTime(
                dto.getMaterialCode(), dto.getTaskNo(), dateStr);

            // 更新详情记录
            updateOrderDetail(existingDetail, dto);

            // 获取主表记录
            record = sorghumTransferOrderMapper.selectById(existingDetail.getOrderId());

            // 查询该主表下所有详情记录，对sendQuantity和inQuantity进行求和
            updateMainOrderWithDetailSum(record);

        } else {
            // 详情不存在，检查主表是否存在
            record = findOrderByDateAndMaterialCode(dateStr, dto.getMaterialCode());

            if (record == null) {
                // 主表也不存在，创建主表记录
                log.info("未找到主表记录，将创建新的主表记录，物料编码：{}", dto.getMaterialCode());
                record = createMainOrder(dto, recordDate, dateStr);
            } else {
                log.info("找到已存在的主表记录，将更新主表数据，工单ID：{}", record.getId());
            }

            // 新增详情记录
            createOrderDetail(record.getId(), dto);

            // 更新主表实际数量
            updateMainOrderQuantity(record, dto.getActualQuantity());
        }

        return record.getId().toString();
    }

    /**
     * 更新记录字段
     */
    private void updateRecordFields(TMpdSorghumTransferOrder record, UnifiedTaskDTO dto) {
        if (dto.getMaterialCode() != null) record.setMaterialCode(dto.getMaterialCode());
        if (dto.getMaterialName() != null) record.setMaterialName(dto.getMaterialName());
        if (dto.getUnit() != null) record.setUnit(dto.getUnit());
        if (dto.getStatus() != null) record.setStatus(dto.getStatus());
    }

    /**
     * 检查记录是否已存在（根据物料编码、实际数量、开始时间判断）
     */
    private boolean isRecordExists(String materialCode, String controlTaskNo, String dateStr) {
        TMpdStOrderDetail existing = stOrderDetailMapper.selectByMaterialCodeAndQuantityAndTime(
            materialCode,
            controlTaskNo,
                dateStr
        );
        return existing != null;
    }

    /**
     * 根据日期和物料编码查找转运工单
     */
    private TMpdSorghumTransferOrder findOrderByDateAndMaterialCode(String dateStr, String materialCode) {
        return sorghumTransferOrderMapper.selectByDateAndMaterialCode(dateStr, materialCode);
    }

    /**
     * 创建工单详情
     */
    private void createOrderDetail(Integer orderId, UnifiedTaskDTO record) {
        TMpdStOrderDetail detail = new TMpdStOrderDetail();
        detail.setOrderId(orderId);
        detail.setSendSiloName(record.getSourceSiloName());
        detail.setAcceptSiloName(record.getDestinationSiloName());
        detail.setMaterialName(record.getMaterialName());
        detail.setMaterialCode(record.getMaterialCode());
        detail.setUnit(record.getUnit());
        detail.setActualQuantity(record.getActualQuantity());
        detail.setActualBeginTime(record.getActualStartTime());
        detail.setActualEndTime(record.getActualEndTime());
        detail.setControlTaskNo(record.getTaskNo()); // 设置中控任务单号
        detail.setState("1"); // 已完成
        detail.setIsManual("0"); // 自动（中控）
        detail.setCreatorName("中控系统");
        detail.setDeleted(false);
        detail.setCreateTime(new Date());
        detail.setUpdateTime(new Date());

        stOrderDetailMapper.insert(detail);
        log.info("创建详情记录成功，工单ID：{}，任务号：{}", orderId, record.getTaskNo());
    }

    /**
     * 更新工单详情
     */
    private void updateOrderDetail(TMpdStOrderDetail existingDetail, UnifiedTaskDTO dto) {
        TMpdStOrderDetail updateDetail = new TMpdStOrderDetail();
        updateDetail.setId(existingDetail.getId());
        updateDetail.setActualQuantity(dto.getActualQuantity());
        updateDetail.setActualBeginTime(dto.getActualStartTime());
        updateDetail.setActualEndTime(dto.getActualEndTime());
        updateDetail.setSendSiloName(dto.getSourceSiloName());
        updateDetail.setAcceptSiloName(dto.getDestinationSiloName());
        // 设置发出数量和入仓数量（假设与实际数量相同，可根据业务需求调整）
        updateDetail.setSendQuantity(dto.getActualQuantity());
        updateDetail.setInQuantity(dto.getActualQuantity());
        updateDetail.setUpdateTime(new Date());

        stOrderDetailMapper.updateById(updateDetail);
        log.info("更新详情记录成功，详情ID：{}，实际数量：{}", existingDetail.getId(), dto.getActualQuantity());
    }

    /**
     * 创建主表工单
     */
    private TMpdSorghumTransferOrder createMainOrder(UnifiedTaskDTO dto, Date recordDate, String dateStr) {
        TMpdSorghumTransferOrder order = new TMpdSorghumTransferOrder();
        order.setOrderNo(generateOrderNo(recordDate));
        order.setOrderDate(recordDate);
        order.setMaterialCode(dto.getMaterialCode());
        order.setMaterialName(dto.getMaterialName());
        order.setUnit(dto.getUnit());
        order.setActualQuantity(BigDecimal.ZERO); // 初始数量为0，通过详情累加
        order.setInventoryQuantity(getCurrentInventoryQuantity(dto.getMaterialCode()));
        order.setStatus("0"); // 执行中，完成后会更新为1
        order.setDeleted(false);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());

        sorghumTransferOrderMapper.insert(order);
        log.info("创建主表记录成功，工单号：{}", order.getOrderNo());
        return order;
    }

    /**
     * 更新主表实际数量
     */
    private void updateMainOrderQuantity(TMpdSorghumTransferOrder order, BigDecimal additionalQuantity) {
        BigDecimal currentQuantity = order.getActualQuantity();
        if (currentQuantity == null) {
            currentQuantity = BigDecimal.ZERO;
        }

        BigDecimal newQuantity = currentQuantity.add(additionalQuantity);

        TMpdSorghumTransferOrder updateOrder = new TMpdSorghumTransferOrder();
        updateOrder.setId(order.getId());
        updateOrder.setActualQuantity(newQuantity);
        updateOrder.setStatus("1"); // 更新状态为已完成
        updateOrder.setUpdateTime(new Date());

        sorghumTransferOrderMapper.updateById(updateOrder);
        log.info("更新主表数量成功，工单ID：{}，新数量：{}", order.getId(), newQuantity);
    }

    /**
     * 根据详情记录求和更新主表
     */
    private void updateMainOrderWithDetailSum(TMpdSorghumTransferOrder order) {
        // 查询该主表下所有详情记录的汇总数据
        DetailSummaryDTO summaryDTO = stOrderDetailMapper.selectSummaryByOrderId(order.getId());

        TMpdSorghumTransferOrder updateOrder = new TMpdSorghumTransferOrder();
        updateOrder.setId(order.getId());

        if (summaryDTO != null) {
            // 更新实际数量、发出数量、入仓数量
            updateOrder.setActualQuantity(summaryDTO.getTotalActualQuantity() != null ?
                summaryDTO.getTotalActualQuantity() : BigDecimal.ZERO);
            updateOrder.setSendQuantity(summaryDTO.getTotalSendQuantity() != null ?
                summaryDTO.getTotalSendQuantity() : BigDecimal.ZERO);
            updateOrder.setInQuantity(summaryDTO.getTotalInQuantity() != null ?
                summaryDTO.getTotalInQuantity() : BigDecimal.ZERO);

            log.info("详情汇总 - 实际数量：{}，发出数量：{}，入仓数量：{}",
                summaryDTO.getTotalActualQuantity(),
                summaryDTO.getTotalSendQuantity(),
                summaryDTO.getTotalInQuantity());
        } else {
            // 如果没有详情记录，设置为0
            updateOrder.setActualQuantity(BigDecimal.ZERO);
            updateOrder.setSendQuantity(BigDecimal.ZERO);
            updateOrder.setInQuantity(BigDecimal.ZERO);
        }

        updateOrder.setStatus("1"); // 更新状态为已完成
        updateOrder.setUpdateTime(new Date());

        sorghumTransferOrderMapper.updateById(updateOrder);
        log.info("根据详情汇总更新主表成功，工单ID：{}，实际数量：{}，发出数量：{}，入仓数量：{}",
            order.getId(), updateOrder.getActualQuantity(),
            updateOrder.getSendQuantity(), updateOrder.getInQuantity());
    }

    /**
     * 生成工单号
     * 规则：GD + 年月日 + 三位流水号
     */
    private String generateOrderNo(Date orderDate) {
        if (orderDate == null) {
            orderDate = new Date();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        String dateStr = sdf.format(orderDate);

        // 查询当天最大流水号
        String maxSerialNo = sorghumTransferOrderMapper.selectMaxSerialNoByDate(orderDate);
        int serialNo = 1;
        if (maxSerialNo != null && !maxSerialNo.isEmpty()) {
            serialNo = Integer.parseInt(maxSerialNo) + 1;
        }

        return String.format("GD%s%03d", dateStr, serialNo);
    }

    /**
     * 获取当前库存数量（汇总的前处理存储仓的总库存）
     */
    private BigDecimal getCurrentInventoryQuantity(String materialCode) {
        // 这里应该调用库存管理服务获取实际库存
        // 暂时返回模拟数据
        return new BigDecimal("1000000");
    }
}
