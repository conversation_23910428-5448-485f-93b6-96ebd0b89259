package com.hvisions.rawmaterial.service;

import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.rawmaterial.dto.VentilationRecordDTO;
import com.hvisions.rawmaterial.dto.VentilationRecordQueryDTO;
import com.hvisions.rawmaterial.dto.VentilationTemperatureMaintainDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 通风记录服务接口
 * @Date: 2024/07/14
 */
public interface VentilationRecordService {

    /**
     * 创建通风记录（中控系统同步）
     * @param dto 通风记录信息
     * @return 通风记录ID
     */
    String createVentilationRecord(VentilationRecordDTO dto);

    /**
     * 更新通风记录状态为已完成（中控系统同步）
     * @param centralControlTaskId 中控系统任务ID
     * @param endTime 结束时间
     * @return 是否成功
     */
    Boolean completeVentilationRecord(String centralControlTaskId, java.util.Date endTime);

    /**
     * 维护温度信息
     * @param dto 温度维护信息
     * @return 是否成功
     */
    Boolean maintainTemperature(VentilationTemperatureMaintainDTO dto, UserInfoDTO userInfo);

    /**
     * 分页查询通风记录
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<VentilationRecordDTO> queryVentilationRecords(VentilationRecordQueryDTO queryDTO);

    /**
     * 根据ID查询通风记录详情
     * @param id 通风记录ID
     * @return 通风记录详情
     */
    VentilationRecordDTO getVentilationRecordById(Integer id);

    /**
     * 根据工单号查询通风记录
     * @param workOrderNumber 工单号
     * @return 通风记录详情
     */
    VentilationRecordDTO getVentilationRecordByWorkOrderNumber(String workOrderNumber);

    /**
     * 查询执行中的通风记录
     * @return 执行中的通风记录列表
     */
    List<VentilationRecordDTO> getExecutingRecords();

    /**
     * 删除通风记录
     * @param id 通风记录ID
     * @return 是否成功
     */
    Boolean deleteVentilationRecord(Integer id);

    /**
     * 生成工单号
     * @return 工单号
     */
    String generateWorkOrderNumber();
}
