package com.hvisions.rawmaterial.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.receive.SorghumReceiveDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.receive.UpdateFlourTransferDTO;
import com.hvisions.rawmaterial.dao.*;
import com.hvisions.rawmaterial.entity.*;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumProductionOrder;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumShipmentOrder;
import com.hvisions.rawmaterial.service.*;
import com.hvisions.rawmaterial.dao.RlManagementMapper;
import com.hvisions.rawmaterial.entity.TMpdRlManagement;
import com.hvisions.rawmaterial.service.sorghum.SorghumTransferOrderService;
import com.hvisions.rawmaterial.utils.DateUtil;
import com.hvisions.rawmaterial.utils.StringUtil;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class SorghumReceiveOrderDetailServiceImpl implements SorghumReceiveOrderDetailService {

    @Resource
    private SorghumTransferOrderService sorghumTransferOrderService;

    @Resource
    private SorghumShipmentOrderService sorghumShipmentOrderService;

    @Resource
    private SorghumProductionOrderService sorghumProductionOrderService;

    @Resource
    private FlourTransferOrderService flourTransferOrderService;

    @Resource
    private SorghumDispenseOrderService sorghumDispenseOrderService;

    @Resource
    private SorghumTransferOrderMapper sorghumTransferOrderMapper;

    @Resource
    private SorghumShipmentOrderMapper sorghumShipmentOrderMapper;

    @Resource
    private SorghumDispenseOrderMapper sorghumDispenseOrderMapper;

    @Resource
    private FlourTransferOrderMapper flourTransferOrderMapper;

    @Resource
    private SorghumProductionOrderMapper sorghumProductionOrderMapper;

    @Resource
    private RlManagementMapper rlManagementMapper;

    @Autowired
    private LogCaptureClient logCaptureClient;


    /***
     * @Description 布勒数据转换成工单详情记录
     *
     * <AUTHOR>
     * @Date 2023-8-11 9:19
     * @param sorghumReceiveDTOS
     * @param type 0 1 2 3 4 5
     * @return java.util.List<com.hvisions.purchase.dto.production.sorghum.DetailInsertDTO>
     **/
    public List<DetailInsertDTO> convert(List<SorghumReceiveDTO> sorghumReceiveDTOS, Integer type) {

        List<DetailInsertDTO> detailInsertDTOS = new ArrayList<>();
        for (SorghumReceiveDTO sorghumReceiveDTO : sorghumReceiveDTOS) {

            String[] sendStorages = StringUtil.isNotEmpty(sorghumReceiveDTO.getSenderStorageIdent()) ? sorghumReceiveDTO.getSenderStorageIdent().split(",") : new String[0];
            String[] acceptStorages = StringUtil.isNotEmpty(sorghumReceiveDTO.getReceiverStorageIdent()) ? sorghumReceiveDTO.getReceiverStorageIdent().split(",") : new String[0];
            String[] sendWeights = StringUtil.isNotEmpty(sorghumReceiveDTO.getSendStorageWeightActual()) ? sorghumReceiveDTO.getSendStorageWeightActual().split(",") : new String[0];
            String[] acceptWeights = StringUtil.isNotEmpty(sorghumReceiveDTO.getReceiverStorageWeightActual()) ? sorghumReceiveDTO.getReceiverStorageWeightActual().split(",") : new String[0];
            String[] material = covertMaterial(sorghumReceiveDTO.getProductName());
            String center = "";

            if (type == 0 || type == 1) { //二期到一期 和 一期到高粱暂存仓 n - n
                /**
                 * 多对多，根据多的哪个来判定单据数量，重量分配也以多的来存，多出来无法对应的以少的最后一个仓库来对应
                 */
                center = covertCenter(acceptStorages[0]);
                if (sendStorages.length > acceptStorages.length && sendWeights.length > 0) {

                    for (int i = 0; i < sendStorages.length; i++) {
                        TMpdRlManagement sendRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                                .eq(TMpdRlManagement::getDeleted, false)
                                .eq(TMpdRlManagement::getCode, covertStorage(sendStorages[i]))
                                .last("limit 1"));
                        TMpdRlManagement acceptRl = null;
                        if (i < (acceptStorages.length - 1)) {
                            acceptRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                                    .eq(TMpdRlManagement::getDeleted, false)
                                    .eq(TMpdRlManagement::getCode, covertStorage(acceptStorages[i]))
                                    .last("limit 1"));
                        } else {
                            acceptRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                                    .eq(TMpdRlManagement::getDeleted, false)
                                    .eq(TMpdRlManagement::getCode, covertStorage(acceptStorages[acceptStorages.length - 1]))
                                    .last("limit 1"));
                        }
                        DetailInsertDTO detailInsertDTO = new DetailInsertDTO();
                        detailInsertDTO.setJobIdent(sorghumReceiveDTO.getJobIdent());
                        detailInsertDTO.setCenter(center);
                        detailInsertDTO.setSendSiloId(sendRl.getId());
                        detailInsertDTO.setAcceptSiloId(acceptRl.getId());
                        detailInsertDTO.setMaterialCode(material[0]);
                        detailInsertDTO.setMaterialName(material[1]);
                        detailInsertDTO.setUnit("kg");
                        detailInsertDTO.setActualQuantity(new BigDecimal(sendWeights[i]));
                        detailInsertDTO.setActualBeginTime(sorghumReceiveDTO.getStartDateActual());
                        detailInsertDTO.setActualEndTime(sorghumReceiveDTO.getEndDateActual());
                        detailInsertDTOS.add(detailInsertDTO);
                    }
                } else if (sendStorages.length <= acceptStorages.length && acceptWeights.length > 0) {
                    for (int i = 0; i < acceptStorages.length; i++) {
                        TMpdRlManagement acceptRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                                .eq(TMpdRlManagement::getDeleted, false)
                                .eq(TMpdRlManagement::getCode, covertStorage(acceptStorages[i]))
                                .last("limit 1"));
                        TMpdRlManagement sendRl = null;
                        if (i < sendStorages.length - 1) {
                            sendRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                                    .eq(TMpdRlManagement::getDeleted, false)
                                    .eq(TMpdRlManagement::getCode, covertStorage(sendStorages[i]))
                                    .last("limit 1"));
                        } else {
                            sendRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                                    .eq(TMpdRlManagement::getDeleted, false)
                                    .eq(TMpdRlManagement::getCode, covertStorage(sendStorages[sendStorages.length - 1]))
                                    .last("limit 1"));
                        }
                        DetailInsertDTO detailInsertDTO = new DetailInsertDTO();
                        detailInsertDTO.setJobIdent(sorghumReceiveDTO.getJobIdent());
                        detailInsertDTO.setCenter(center);
                        detailInsertDTO.setSendSiloId(sendRl.getId());
                        detailInsertDTO.setAcceptSiloId(acceptRl.getId());
                        detailInsertDTO.setMaterialCode(material[0]);
                        detailInsertDTO.setMaterialName(material[1]);
                        detailInsertDTO.setUnit("kg");
                        detailInsertDTO.setActualQuantity(new BigDecimal(acceptWeights[i]));
                        detailInsertDTO.setActualBeginTime(sorghumReceiveDTO.getStartDateActual());
                        detailInsertDTO.setActualEndTime(sorghumReceiveDTO.getEndDateActual());
                        detailInsertDTOS.add(detailInsertDTO);
                    }
                }

            } else if (type == 2) { // 高粱暂存仓 到粉仓暂存仓 n - 1
                /**
                 * 多对一，按照多的筒仓来生成单据，即发出仓
                 */
                if (sendWeights.length > 0 && sendStorages.length == sendWeights.length) {
                    TMpdRlManagement acceptRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                            .eq(TMpdRlManagement::getDeleted, false)
                            .eq(TMpdRlManagement::getCode, covertStorage(acceptStorages[0]))
                            .last("limit 1"));
                    center = covertCenter(acceptStorages[0]);
                    for (int i = 0; i < sendStorages.length; i++) {
                        TMpdRlManagement sendRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                                .eq(TMpdRlManagement::getDeleted, false)
                                .eq(TMpdRlManagement::getCode, covertStorage(sendStorages[i]))
                                .last("limit 1"));
                        DetailInsertDTO detailInsertDTO = new DetailInsertDTO();
                        detailInsertDTO.setJobIdent(sorghumReceiveDTO.getJobIdent());
                        detailInsertDTO.setCenter(center);
                        detailInsertDTO.setSendSiloId(sendRl.getId());
                        detailInsertDTO.setAcceptSiloId(acceptRl.getId());
                        detailInsertDTO.setMaterialCode(material[0]);
                        detailInsertDTO.setMaterialName(material[1]);
                        detailInsertDTO.setUnit("kg");
                        detailInsertDTO.setActualQuantity(new BigDecimal(sendWeights[i]));
                        detailInsertDTO.setActualBeginTime(sorghumReceiveDTO.getStartDateActual());
                        detailInsertDTO.setActualEndTime(sorghumReceiveDTO.getEndDateActual());
                        detailInsertDTOS.add(detailInsertDTO);
                    }
                }

            } else if (type == 3) { // 粉仓到发放仓 1 - n
                /**
                 * 一对多，按照多的筒仓来生成单据，即接收仓
                 */
                if (acceptWeights.length > 0 && acceptStorages.length == acceptWeights.length) {
                    TMpdRlManagement sendRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                            .eq(TMpdRlManagement::getDeleted, false)
                            .eq(TMpdRlManagement::getCode, covertStorage(sendStorages[0]))
                            .last("limit 1"));
                    for (int i = 0; i < acceptStorages.length; i++) {
                        TMpdRlManagement acceptRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                                .eq(TMpdRlManagement::getDeleted, false)
                                .eq(TMpdRlManagement::getCode, covertStorage(acceptStorages[i]))
                                .last("limit 1"));
                        DetailInsertDTO detailInsertDTO = new DetailInsertDTO();
                        detailInsertDTO.setJobIdent(sorghumReceiveDTO.getJobIdent());
                        detailInsertDTO.setCenter(sorghumReceiveDTO.getCenterCode());
                        detailInsertDTO.setSendSiloId(sendRl.getId());
                        detailInsertDTO.setAcceptSiloId(acceptRl.getId());
                        detailInsertDTO.setMaterialCode(material[0]);
                        detailInsertDTO.setMaterialName(material[1]);
                        detailInsertDTO.setUnit("kg");
                        detailInsertDTO.setActualQuantity(new BigDecimal(acceptWeights[i]));
                        detailInsertDTO.setActualBeginTime(sorghumReceiveDTO.getStartDateActual());
                        detailInsertDTO.setActualEndTime(sorghumReceiveDTO.getEndDateActual());
                        detailInsertDTOS.add(detailInsertDTO);
                    }
                }

            } else if (type == 4) { // 发放仓发出 1 - 0
                if (sendStorages.length > 0 && sendWeights.length == sendStorages.length) {
                    for (int i = 0; i < sendStorages.length; i++) {
                        TMpdRlManagement sendRl = rlManagementMapper.selectOne(new LambdaQueryWrapper<TMpdRlManagement>()
                                .eq(TMpdRlManagement::getDeleted, false)
                                .eq(TMpdRlManagement::getCode, covertStorage(sendStorages[i]))
                                .last("limit 1"));
                        if (StringUtil.isNotEmpty(sendRl)) {
                            DetailInsertDTO detailInsertDTO = new DetailInsertDTO();
                            detailInsertDTO.setJobIdent(sorghumReceiveDTO.getJobIdent());
                            detailInsertDTO.setCenter(sorghumReceiveDTO.getCenterCode());
                            detailInsertDTO.setSendSiloId(sendRl.getId());
                            detailInsertDTO.setMaterialCode(material[0]);
                            detailInsertDTO.setMaterialName(material[1]);
                            detailInsertDTO.setUnit("kg");
                            detailInsertDTO.setActualQuantity(new BigDecimal(sendWeights[i]));
                            detailInsertDTO.setActualBeginTime(sorghumReceiveDTO.getStartDateActual());
                            detailInsertDTO.setActualEndTime(sorghumReceiveDTO.getEndDateActual());
                            detailInsertDTOS.add(detailInsertDTO);
                        }
                    }
                }
            }
        }

        return detailInsertDTOS;
    }


    @Override
    public Integer receiveSorghumTransfer(List<SorghumReceiveDTO> sorghumReceiveDTOS) {
        writerLog(sorghumReceiveDTOS.get(0).getCenterCode(), JSONObject.toJSONString(sorghumReceiveDTOS),"高粱转运接收数据");
        log.info("WinCos自动接收高粱转运---{}", sorghumReceiveDTOS);
        List<DetailInsertDTO> detailInsertDTOS = convert(sorghumReceiveDTOS, 0);
        for (DetailInsertDTO detailInsertDTO : detailInsertDTOS) {
            TMpdSorghumTransferOrder order = sorghumTransferOrderMapper.selectOne(new LambdaQueryWrapper<TMpdSorghumTransferOrder>()
                    .eq(TMpdSorghumTransferOrder::getDeleted, false)
                    .like(TMpdSorghumTransferOrder::getOrderDate, DateUtil.format(detailInsertDTO.getActualEndTime(),"yyyy-MM-dd"))
                    .orderByDesc(TMpdSorghumTransferOrder::getOrderDate)
                    .last("limit 1"));
            if (StringUtil.isEmpty(order)){
//                throw new BaseKnownException(10000, "不存在今日工单");
                return 0;
            }
            detailInsertDTO.setOrderId(order.getId());
        }
        return sorghumTransferOrderService.insertOrderDetail(detailInsertDTOS);

    }


    private void writerLog(String location,String logParameter, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setLogType(1);
        logDto.setLogParameter(logParameter);
        logDto.setControllerName("接收高粱生产数据");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular("接收高粱生产数据");
        logDto.setLogInvocation("sys");
        logDto.setLocation(location);
        logDto.setMethodName(methodName);
        logCaptureClient.logRecord(logDto);
    }

    // 3个高粱暂存仓，如果是 1 、2好筒仓入，转换成 入 3号筒仓，也就是说1、2两个筒仓默认没用，出的时候也是只有3号筒仓出
    @Override
    public Integer receiveSorghumShipment(List<SorghumReceiveDTO> sorghumReceiveDTOS) {
        writerLog(sorghumReceiveDTOS.get(0).getCenterCode(), JSONObject.toJSONString(sorghumReceiveDTOS),"高粱转粮接收数据");
        log.info("WinCos自动接收高粱转粮---{}", sorghumReceiveDTOS);
        List<DetailInsertDTO> detailInsertDTOS = convert(sorghumReceiveDTOS, 1);
        for (DetailInsertDTO detailInsertDTO : detailInsertDTOS) {
            TMpdSorghumShipmentOrder order = sorghumShipmentOrderMapper.selectOne(new LambdaQueryWrapper<TMpdSorghumShipmentOrder>()
                    .eq(TMpdSorghumShipmentOrder::getDeleted, false)
                    //.eq(TMpdSorghumShipmentOrder::getCenter, detailInsertDTO.getCenter())
                    .like(TMpdSorghumShipmentOrder::getOrderDate, DateUtil.format(detailInsertDTO.getActualEndTime(),"yyyy-MM-dd"))
                    .orderByDesc(TMpdSorghumShipmentOrder::getOrderDate)
                    .last("limit 1"));
            if (StringUtil.isEmpty(order)){
//                throw new BaseKnownException(10000, "不存在今日工单");
                return 0;
            }
            detailInsertDTO.setOrderId(order.getId());
        }
        return sorghumShipmentOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @Override
    public Integer receiveSorghumProduction(List<SorghumReceiveDTO> sorghumReceiveDTOS) {
        writerLog(sorghumReceiveDTOS.get(0).getCenterCode(), JSONObject.toJSONString(sorghumReceiveDTOS),"高粱粉接收数据");
        log.info("WinCos自动接收高粱生产---{}", sorghumReceiveDTOS);
        List<DetailInsertDTO> detailInsertDTOS = convert(sorghumReceiveDTOS, 2);
        for (DetailInsertDTO detailInsertDTO : detailInsertDTOS) {
            TMpdSorghumProductionOrder order = sorghumProductionOrderMapper.selectOne(new LambdaQueryWrapper<TMpdSorghumProductionOrder>()
                    .eq(TMpdSorghumProductionOrder::getDeleted, false)
                    .eq(TMpdSorghumProductionOrder::getCenter, detailInsertDTO.getCenter())
                    .like(TMpdSorghumProductionOrder::getOrderDate, DateUtil.format(detailInsertDTO.getActualEndTime(),"yyyy-MM-dd"))
                    .orderByDesc(TMpdSorghumProductionOrder::getOrderDate)
                    .last("limit 1"));
            if (StringUtil.isEmpty(order)){
//                throw new BaseKnownException(10000, "不存在今日工单");
                return 0;
            }
            detailInsertDTO.setOrderId(order.getId());
        }

        return sorghumProductionOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @Override
    public Integer receiveFlourTransfer(List<SorghumReceiveDTO> sorghumReceiveDTOS) {
        writerLog(sorghumReceiveDTOS.get(0).getCenterCode(), JSONObject.toJSONString(sorghumReceiveDTOS),"接收高粱粉转运接收数据");
        log.info("WinCos自动接收高粱粉转运---{}", sorghumReceiveDTOS);
        List<DetailInsertDTO> detailInsertDTOS = convert(sorghumReceiveDTOS, 3);
        for (DetailInsertDTO detailInsertDTO : detailInsertDTOS) {
            TMpdFlourTransferOrder order = flourTransferOrderMapper.selectOne(new LambdaQueryWrapper<TMpdFlourTransferOrder>()
                    .eq(TMpdFlourTransferOrder::getDeleted, false)
                    .eq(TMpdFlourTransferOrder::getCenter, detailInsertDTO.getCenter())
                    .like(TMpdFlourTransferOrder::getOrderDate, DateUtil.format(detailInsertDTO.getActualEndTime(),"yyyy-MM-dd"))
                    .orderByDesc(TMpdFlourTransferOrder::getOrderDate)
                    .last("limit 1"));
            if (StringUtil.isEmpty(order)){
//                throw new BaseKnownException(10000, "不存在今日工单");
                return 0;
            }
            detailInsertDTO.setOrderId(order.getId());
        }

        return flourTransferOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @Override
    public Integer receiveSorghumDispense(List<SorghumReceiveDTO> sorghumReceiveDTOS) {
        writerLog(sorghumReceiveDTOS.get(0).getCenterCode(), JSONObject.toJSONString(sorghumReceiveDTOS),"接收高粱粉发放接收数据");
        log.info("WinCos自动接收高粱粉发放---{}", sorghumReceiveDTOS);
        List<DetailInsertDTO> detailInsertDTOS = convert(sorghumReceiveDTOS, 4);
        for (DetailInsertDTO detailInsertDTO : detailInsertDTOS) {
            TMpdSorghumDispenseOrder order = sorghumDispenseOrderMapper.selectOne(new LambdaQueryWrapper<TMpdSorghumDispenseOrder>()
                    .eq(TMpdSorghumDispenseOrder::getDeleted, false)
                    .eq(TMpdSorghumDispenseOrder::getCenter, detailInsertDTO.getCenter())
                    .like(TMpdSorghumDispenseOrder::getOrderDate, DateUtil.format(detailInsertDTO.getActualEndTime(),"yyyy-MM-dd"))
                    .orderByDesc(TMpdSorghumDispenseOrder::getOrderDate)
                    .last("limit 1"));
            if (StringUtil.isEmpty(order)){
//                throw new BaseKnownException(10000, "不存在今日工单");
                return 0;
            }
            detailInsertDTO.setPostingTime(new Date());
            detailInsertDTO.setOrderId(order.getId());
        }

        return sorghumDispenseOrderService.insertOrderDetail(detailInsertDTOS);
    }

    /**
     * 更新高粱粉转运工单详情
     * @param updateFlourTransferDTO
     * @return
     */
    @Override
    public Integer updateFlourTransfer(UpdateFlourTransferDTO updateFlourTransferDTO) {
        writerUpdateLog(JSONObject.toJSONString(updateFlourTransferDTO), "更新高粱粉转运工单详情");
        return flourTransferOrderService.updateFlourTransfer(updateFlourTransferDTO);
    }

    /**
     * 更新高粱粉发放工单详情
     * @param updateFlourTransferDTO
     * @return
     */
    @Override
    public Integer updateSorghumDispense(UpdateFlourTransferDTO updateFlourTransferDTO) {
        writerUpdateLog(JSONObject.toJSONString(updateFlourTransferDTO), "更新高粱粉发放工单详情");
        return sorghumDispenseOrderService.updateSorghumDispense(updateFlourTransferDTO);
    }

    /**
     * 更新数据日志
     * @param logParameter
     * @param methodName
     */
    private void writerUpdateLog(String logParameter, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setLogType(1);
        logDto.setLogParameter(logParameter);
        logDto.setControllerName("更新高粱生产数据");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular("更新高粱生产数据");
        logDto.setLocation("");
        logDto.setLogInvocation("sys");
        logDto.setMethodName(methodName);
        logCaptureClient.logRecord(logDto);
    }

    public static String[] covertMaterial(String material) {
        String[] list = new String[2];
        if (material.equals("LGL1")) {
            list[0] = "11000564";
            list[1] = "LGL1";
        } else if (material.equals("LGL2")) {
            list[0] = "11000543";
            list[1] = "LGL2";
        } else if (material.equals("LGL3")) {
            list[0] = "11000544";
            list[1] = "LGL3";
        }
        return list;
    }

    /***
     * @Description 布勒筒仓号转换成mes筒仓
     *
     * <AUTHOR>
     * @Date 2023-8-4 9:20
     * @param blStorage 布勒筒仓号
     * @return java.lang.String mes筒仓号
     **/
    public static String covertStorage(String blStorage) {
        String storageCode = null;

        if (blStorage.contains("S")) { // 一期筒仓
            switch (blStorage) {
                case "S101":
                    storageCode = "GL1-1";
                    break;
                case "S102":
                    storageCode = "GL1-2";
                    break;
                case "S103":
                    storageCode = "GL1-3";
                    break;
                case "S104":
                    storageCode = "GL1-4";
                    break;
            }
        } else if (blStorage.contains("K")) { // 709筒仓
            switch (blStorage) {
                case "K101":
                    storageCode = "7091";
                    break;
                case "K102":
                    storageCode = "7092";
                    break;
                case "K103":
                    storageCode = "7093";
                    break;
                case "K104":
                    storageCode = "709F";
                    break;
                case "K201":
                    storageCode = "709F1";
                    break;
                case "K202":
                    storageCode = "709F2";
                    break;
                case "K203":
                    storageCode = "709F3";
                    break;
                case "K204":
                    storageCode = "709F4";
                    break;
                case "K205":
                    storageCode = "709F5";
                    break;
            }
        } else if (blStorage.contains("L")) { // 710筒仓
            switch (blStorage) {
                case "L101":
                    storageCode = "7101";
                    break;
                case "L102":
                    storageCode = "7102";
                    break;
                case "L103":
                    storageCode = "7103";
                    break;
                case "L104":
                    storageCode = "710F";
                    break;
                case "L201":
                    storageCode = "710F1";
                    break;
                case "L202":
                    storageCode = "710F2";
                    break;
                case "L203":
                    storageCode = "710F3";
                    break;
                case "L204":
                    storageCode = "710F4";
                    break;
                case "L205":
                    storageCode = "710F5";
                    break;
            }
        } else if (blStorage.contains("C")) { // 711筒仓
            switch (blStorage) {
                case "C101":
                    storageCode = "7111";
                    break;
                case "C102":
                    storageCode = "7112";
                    break;
                case "C103":
                    storageCode = "7113";
                    break;
                case "C104":
                    storageCode = "711F";
                    break;
                case "C201":
                    storageCode = "711F1";
                    break;
                case "C202":
                    storageCode = "711F2";
                    break;
                case "C203":
                    storageCode = "711F3";
                    break;
                case "C204":
                    storageCode = "711F4";
                    break;
                case "C205":
                    storageCode = "711F5";
                    break;
            }
        } else if (blStorage.contains("M")) { // 712筒仓
            switch (blStorage) {
                case "M101":
                    storageCode = "7121";
                    break;
                case "M102":
                    storageCode = "7122";
                    break;
                case "M103":
                    storageCode = "7123";
                    break;
                case "M104":
                    storageCode = "712F";
                    break;
                case "M201":
                    storageCode = "712F1";
                    break;
                case "M202":
                    storageCode = "712F2";
                    break;
                case "M203":
                    storageCode = "712F3";
                    break;
                case "M204":
                    storageCode = "712F4";
                    break;
                case "M205":
                    storageCode = "712F5";
                    break;
            }
        } else if (blStorage.contains("H")) { // 713筒仓
            switch (blStorage) {
                case "H101":
                    storageCode = "7131";
                    break;
                case "H102":
                    storageCode = "7132";
                    break;
                case "H103":
                    storageCode = "7133";
                    break;
                case "H104":
                    storageCode = "713F";
                    break;
                case "H201":
                    storageCode = "713F1";
                    break;
                case "H202":
                    storageCode = "713F2";
                    break;
                case "H203":
                    storageCode = "713F3";
                    break;
                case "H204":
                    storageCode = "713F4";
                    break;
                case "H205":
                    storageCode = "713F5";
                    break;
            }
        } else if (blStorage.contains("G")) { // 714筒仓
            switch (blStorage) {
                case "G101":
                    storageCode = "7141";
                    break;
                case "G102":
                    storageCode = "7142";
                    break;
                case "G103":
                    storageCode = "7143";
                    break;
                case "G104":
                    storageCode = "714F";
                    break;
                case "G201":
                    storageCode = "714F1";
                    break;
                case "G202":
                    storageCode = "714F2";
                    break;
                case "G203":
                    storageCode = "714F3";
                    break;
                case "G204":
                    storageCode = "714F4";
                    break;
                case "G205":
                    storageCode = "714F5";
                    break;
            }
        } else if (blStorage.contains("J")) { // 715筒仓
            switch (blStorage) {
                case "J101":
                    storageCode = "7151";
                    break;
                case "J102":
                    storageCode = "7152";
                    break;
                case "J103":
                    storageCode = "7153";
                    break;
                case "J104":
                    storageCode = "715F";
                    break;
                case "J201":
                    storageCode = "715F1";
                    break;
                case "J202":
                    storageCode = "715F2";
                    break;
                case "J203":
                    storageCode = "715F3";
                    break;
                case "J204":
                    storageCode = "715F4";
                    break;
                case "J205":
                    storageCode = "715F5";
                    break;
            }
        } else if (blStorage.contains("F")) { // 716筒仓
            switch (blStorage) {
                case "F101":
                    storageCode = "7161";
                    break;
                case "F102":
                    storageCode = "7162";
                    break;
                case "F103":
                    storageCode = "7163";
                    break;
                case "F104":
                    storageCode = "716F";
                    break;
                case "F201":
                    storageCode = "716F1";
                    break;
                case "F202":
                    storageCode = "716F2";
                    break;
                case "F203":
                    storageCode = "716F3";
                    break;
                case "F204":
                    storageCode = "716F4";
                    break;
                case "F205":
                    storageCode = "716F5";
                    break;
            }
        } else if (blStorage.contains("E")) { // 717筒仓
            switch (blStorage) {
                case "E101":
                    storageCode = "7171";
                    break;
                case "E102":
                    storageCode = "7172";
                    break;
                case "E103":
                    storageCode = "7173";
                    break;
                case "E104":
                    storageCode = "717F";
                    break;
                case "E201":
                    storageCode = "717F1";
                    break;
                case "E202":
                    storageCode = "717F2";
                    break;
                case "E203":
                    storageCode = "717F3";
                    break;
                case "E204":
                    storageCode = "717F4";
                    break;
                case "E205":
                    storageCode = "717F5";
                    break;
            }
        } else if (blStorage.contains("D")) { // 718筒仓
            switch (blStorage) {
                case "D101":
                    storageCode = "7181";
                    break;
                case "D102":
                    storageCode = "7182";
                    break;
                case "D103":
                    storageCode = "7183";
                    break;
                case "D104":
                    storageCode = "718F";
                    break;
                case "D201":
                    storageCode = "718F1";
                    break;
                case "D202":
                    storageCode = "718F2";
                    break;
                case "D203":
                    storageCode = "718F3";
                    break;
                case "D204":
                    storageCode = "718F4";
                    break;
                case "D205":
                    storageCode = "718F5";
                    break;
            }
        } else if (blStorage.contains("Q")) { // 719筒仓
            switch (blStorage) {
                case "Q101":
                    storageCode = "7191";
                    break;
                case "Q102":
                    storageCode = "7192";
                    break;
                case "Q103":
                    storageCode = "7193";
                    break;
                case "Q104":
                    storageCode = "719F";
                    break;
                case "Q201":
                    storageCode = "719F1";
                    break;
                case "Q202":
                    storageCode = "719F2";
                    break;
                case "Q203":
                    storageCode = "719F3";
                    break;
                case "Q204":
                    storageCode = "719F4";
                    break;
                case "Q205":
                    storageCode = "719F5";
                    break;
            }
        }
        return storageCode;
    }

    /***
     * @Description 根据布勒筒仓返回mes中心号
     *
     * <AUTHOR>
     * @Date 2023-8-11 10:15
     * @param blStorage
     * @return java.lang.String
     **/
    public static String covertCenter(String blStorage) {
        if (blStorage.contains("K")) { // 709筒仓
            return "709";
        } else if (blStorage.contains("L")) { // 710筒仓
            return "710";
        } else if (blStorage.contains("C")) { // 711筒仓
            return "711";
        } else if (blStorage.contains("M")) { // 712筒仓
            return "712";
        } else if (blStorage.contains("H")) { // 713筒仓
            return "713";
        } else if (blStorage.contains("G")) { // 714筒仓
            return "714";
        } else if (blStorage.contains("J")) { // 715筒仓
            return "715";
        } else if (blStorage.contains("F")) { // 716筒仓
            return "716";
        } else if (blStorage.contains("E")) { // 717筒仓
            return "717";
        } else if (blStorage.contains("D")) { // 718筒仓
            return "718";
        } else if (blStorage.contains("Q")) { // 719筒仓
            return "719";
        }
        return "";
    }
}
