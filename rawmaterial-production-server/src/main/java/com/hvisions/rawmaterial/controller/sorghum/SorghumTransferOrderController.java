package com.hvisions.rawmaterial.controller.sorghum;

import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailUpdateDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.detail.SorghumTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.service.sorghum.SorghumTransferOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱转运工单
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/sorghum/transfer/order")
@Api(tags = "高粱转运工单")
public class SorghumTransferOrderController {

    @Resource
    private SorghumTransferOrderService branTransferOrderService;


    @ApiOperation(value = "分页查询高粱转运工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<SorghumTransferOrderPageDTO> getSorghumTransferOrderPageList(@RequestBody SorghumTransferOrderPageQueryDTO queryDTO) {
        return branTransferOrderService.getSorghumTransferOrderPageList(queryDTO);
    }

    @ApiOperation(value = "批量新增高粱转运工单详情")
    @RequestMapping(value = "/detail/insert/batch", method = RequestMethod.POST)
    public Integer insertOrderDetail(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        return branTransferOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @ApiOperation(value = "修改高粱转运工单详情")
    @RequestMapping(value = "/detailUpdate", method = RequestMethod.POST)
    public Integer detailUpdate(@RequestBody DetailUpdateDTO updateDTO) {
        return branTransferOrderService.detailUpdate(updateDTO);
    }

    @ApiOperation(value = "分页查询高粱转运工单详细")
    @RequestMapping(value = "/getSorghumTransferOrderDetailPageList", method = RequestMethod.POST)
    public Page<SorghumTransferDetailListDTO> getSorghumTransferOrderDetailPageList(@RequestBody SorghumTransferOrderPageQueryDTO queryDTO) {
        return branTransferOrderService.getSorghumTransferOrderDetailPageList(queryDTO);
    }
}
