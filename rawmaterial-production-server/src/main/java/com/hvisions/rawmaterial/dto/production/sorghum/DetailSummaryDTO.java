package com.hvisions.rawmaterial.dto.production.sorghum;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 详情汇总DTO
 * @date 2025/01/19
 */
@Data
public class DetailSummaryDTO {
    
    /**
     * 总实际数量
     */
    private BigDecimal totalActualQuantity;
    
    /**
     * 总发出数量
     */
    private BigDecimal totalSendQuantity;
    
    /**
     * 总入仓数量
     */
    private BigDecimal totalInQuantity;
    
    /**
     * 详情记录数量
     */
    private Integer detailCount;
}
