package com.hvisions.rawmaterial.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 高粱转运工单
 * @date 2022/4/25 10:09
 */
@Table(name = "t_mpd_sorghum_transfer_order")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_sorghum_transfer_order", comment = "高粱转运工单")
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TableName("t_mpd_sorghum_transfer_order")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TMpdSorghumTransferOrder extends SysBase {

    /**
     * 计划id
     */
    //private Integer planId;
    /**
     * 工单号
     */
    @Column(name = "order_no", length = 20,columnDefinition = "varchar(20) COMMENT '工单号'")
    private String orderNo;
    /**
     * 工单日期
     */
    @Column(name = "order_date", columnDefinition = "datetime COMMENT '工单日期'")
    private Date orderDate;
    /**
     * 生产类型编码
     */
    //private String productionTypeCode;

    /**
     * 中心id
     */
    //private Integer centerId;
    /**
     * 中心编码
     */
    //private String center;

    /**
     * 物料id
     */
    @Column(name = "material_id", columnDefinition = "int COMMENT '物料ID'")
    private Integer materialId;
    /**
     * 物料编码
     */
    @Column(name = "material_code", columnDefinition = "varchar(255) COMMENT '物料编码'")
    private String materialCode;
    /**
     * 物料名称
     */
    @Column(name = "material_name", columnDefinition = "varchar(255) COMMENT '物料名称'")
    private String materialName;
    /**
     * 物料单位
     */
    @Column(name = "unit", columnDefinition = "varchar(255) COMMENT '物料单位'")
    private String unit;
    /**
     * 物料需求数量
     */
    //private BigDecimal requirementQuantity;
    /**
     * 当前库存数量（创建任务时，汇总的前处理存储仓的总库存）
     */
    @Column(name = "inventory_quantity", columnDefinition = "decimal(20,6) COMMENT '当前库存数量'")
    private BigDecimal inventoryQuantity;
    /**
     * 计划数量
     */
    //private BigDecimal planQuantity;
    /**
     * 实际数量（为当日记录累计完成的数量，数量来源于中控抓取的执行数据）
     */
    @Column(name = "actual_quantity", columnDefinition = "decimal(20,6) COMMENT '实际数量'")
    private BigDecimal actualQuantity;


    /**
     * 发出数量
     */
    @Column(name = "send_quantity", columnDefinition = "decimal(20,6) COMMENT '发出数量'")
    private BigDecimal sendQuantity;

    /**
     * 入仓 数量
     */
    @Column(name = "in_quantity", columnDefinition = "decimal(20,6) COMMENT '入仓数量'")
    private BigDecimal inQuantity;

    /**
     * 发出仓库ID
     */
    //private Integer sendWarehouseId;

    /**
     * 接收仓库ID
     */
    //private Integer acceptWarehouseId;

    /**
     * 状态（0：执行中，1：已完成）
     */
    @Column(name = "status", length = 1, columnDefinition = "varchar(1) COMMENT '状态'")
    private String status;

    /**
     * 备注
     */
    @Column(name = "remark", length = 200, columnDefinition = "varchar(200) COMMENT '备注'")
    private String remark;

    ///@Column(name = "central_task_no", length = 50, columnDefinition = "varchar(50) COMMENT '中控任务ID'")
    //private String centralTaskNo;
}
