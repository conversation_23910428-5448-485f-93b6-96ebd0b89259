package com.hvisions.rawmaterial.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 高粱转运工单详情
 * @date 2022/4/25 10:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_st_order_detail")
public class TMpdStOrderDetail extends SysBase {
    
    /**
     * 工单id
     */
    @Column(name = "order_id", columnDefinition = "int COMMENT '工单id'")
    private Integer orderId;
    /**
     * 分发筒仓id(库区)
     */
    @Column(name = "send_silo_id", columnDefinition = "int COMMENT '分发筒仓id(库区)'")
    private Integer sendSiloId;
    /**
     * 接收筒仓id(库区)
     */
    @Column(name = "accept_silo_id", columnDefinition = "int COMMENT '接收筒仓id(库区)'")
    private Integer acceptSiloId;

    /**
     * 物料id
     */
    @Column(name = "material_id", columnDefinition = "int COMMENT '物料id'")
    private Integer materialId;
    /**
     * 物料编码
     */
    @Column(name = "material_code", columnDefinition = "varchar(255) COMMENT '物料编码'")
    private String materialCode;
    /**
     * 物料名称
     */
    @Column(name = "material_name", columnDefinition = "varchar(255) COMMENT '物料名称'")
    private String materialName;
    /**
     * 物料单位
     */
    @Column(name = "unit", columnDefinition = "varchar(255) COMMENT '物料单位'")
    private String unit;
    /**
     * 物料需求数量
     */
    @Column(name = "requirement_quantity", columnDefinition = "decimal(20,6) COMMENT '物料需求数量'")
    private BigDecimal requirementQuantity;

    /**
     * 计划数量
     */
    //private BigDecimal planQuantity;
    /**
     * 实际数量（数量来源于中控执行的数据）
     */
    @Column(name = "actual_quantity", columnDefinition = "decimal(20,6) COMMENT '实际数量（数量来源于中控执行数据）'")
    private BigDecimal actualQuantity;
    /**
     * 批次
     */
    @Column(name = "batch", columnDefinition = "varchar(255) COMMENT '批次'")
    private String batch;
    /**
     * 实际开始时间（中控任务执行的开始时间）
     */
    @Column(name = "actual_begin_time", columnDefinition = "datetime COMMENT '实际开始时间（中控任务执行的开始时间）'")
    private Date actualBeginTime;
    /**
     * 实际结束时间（中控任务执行的结束时间）
     */
    @Column(name = "actual_end_time", columnDefinition = "datetime COMMENT '实际结束时间（中控任务执行的结束时间）'")
    private Date actualEndTime;

    /**
     * 执行状态： 0执行中，1已完成
     */
    @Column(name = "state", columnDefinition = "varchar(1) COMMENT '执行状态： 0执行中，1已完成'")
    private String state;
    /**
     * 是否是手动：0-自动，1-手动
     */
    @Column(name = "is_manual", columnDefinition = "varchar(1) COMMENT '是否是手动：0-自动，1-手动'")
    private String isManual;

    /**
     * 发出仓名称
     */
    @Column(name = "send_silo_name", columnDefinition = "varchar(255) COMMENT '发出仓名称'")
    private String sendSiloName;

    /**
     * 接收仓名称
     */
    @Column(name = "accept_silo_name", columnDefinition = "varchar(255) COMMENT '接收仓名称'")
    private String acceptSiloName;

    /**
     * 创建人姓名（手工创建的为创建人姓名；中控创建的为中控系统）
     */
    @Column(name = "creator_name", columnDefinition = "varchar(255) COMMENT '创建人姓名（手工创建的为创建人姓名；中控创建的为中控系统）'")
    private String creatorName;

    /**
     * 发出数量
     */
    @Column(name = "send_quantity", columnDefinition = "decimal(20,6) COMMENT '发出数量'")
    private BigDecimal sendQuantity;

    /**
     * 入仓 数量
     */
    @Column(name = "in_quantity", columnDefinition = "decimal(20,6) COMMENT '入仓数量'")
    private BigDecimal inQuantity;

    /**
     * 中控任务单号
     */
    @Column(name = "control_task_no", columnDefinition = "varchar(255) COMMENT '中控任务单号'")
    private String controlTaskNo;
}
