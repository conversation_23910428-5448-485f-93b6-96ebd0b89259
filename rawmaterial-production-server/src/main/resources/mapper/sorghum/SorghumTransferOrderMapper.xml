<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.rawmaterial.dao.SorghumTransferOrderMapper">

    <resultMap id="orderMap" type="com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageDTO">
        <id column="id" property="id"/>
        <result property="planId" column="plan_id"/>
        <result property="planNo" column="plan_no"/>
        <result property="orderNo" column="order_no"/>
        <result property="centerCode" column="center"/>
        <result property="orderDate" column="order_date"/>
        <result property="productionTypeCode" column="production_type_code"/>
        <result property="productionTypeName" column="production_type_name"/>
        <result property="sendWarehouseId" column="send_warehouse_id"/>
        <result property="acceptWarehouseId" column="accept_warehouse_id"/>
        <result property="sendWarehouse" column="send_warehouse"/>
        <result property="acceptWarehouse" column="accept_warehouse"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="unit" column="unit"/>
        <result property="requirementQuantity" column="requirement_quantity"/>
        <result property="inventoryQuantity" column="inventory_quantity"/>
        <result property="planQuantity" column="plan_quantity"/>
        <result property="actualQuantity" column="actual_quantity"/>
        <result property="createTime" column="create_time"/>
        <collection property="detailList"
                    column="id"
                    javaType="java.util.List" select="selectDetail"
        >
        </collection>
    </resultMap>

    <select id="getSorghumTransferOrderPageList_back" resultMap="orderMap">
        SELECT sso.*,pt.`name` production_type_name,p.plan_no,
        (SELECT w.`name` FROM t_mpd_warehouse w WHERE w.id = sso.send_warehouse_id)  send_warehouse,
        (SELECT w.`name` FROM t_mpd_warehouse w WHERE w.id = sso.accept_warehouse_id)  accept_warehouse

        FROM `t_mpd_sorghum_transfer_order` sso
        LEFT JOIN t_mpd_sorghum_transfer_plan p ON p.`id` = sso.plan_id AND p.deleted = 0
        LEFT JOIN t_mpd_production_type pt ON pt.`code` = sso.production_type_code AND pt.deleted = 0

        WHERE sso.deleted = 0
        <if test="materialCode != null and materialCode != ''">
            AND sso.`material_code` LIKE concat('%',#{materialCode},'%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND sso.`material_name` LIKE concat('%',#{materialName},'%')
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(sso.order_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        ORDER BY sso.order_date DESC,sso.id DESC
    </select>

    <resultMap id="orderMap_new" type="com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageDTO">
        <id column="id" property="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="orderDate" column="order_date"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="unit" column="unit"/>
        <result property="inventoryQuantity" column="inventory_quantity"/>
        <result property="actualQuantity" column="actual_quantity"/>
        <result property="createTime" column="create_time"/>
        <collection property="detailList"
                    column="id"
                    javaType="java.util.List" select="selectDetail_new"
        >
        </collection>
    </resultMap>

    <select id="getSorghumTransferOrderPageList" resultMap="orderMap_new">
        SELECT sso.id,sso.order_no,sso.order_date,sso.material_id,sso.material_code,sso.material_name,sso.unit,sso.inventory_quantity,
        sso.actual_quantity,sso.create_time
        FROM `t_mpd_sorghum_transfer_order` sso
        WHERE sso.deleted = 0
        <if test="materialCode != null and materialCode != ''">
            AND sso.`material_code` LIKE concat('%',#{materialCode},'%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND sso.`material_name` LIKE concat('%',#{materialName},'%')
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(sso.order_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        ORDER BY sso.order_date DESC,sso.id DESC
    </select>

    <select id="selectDetail" resultType="com.hvisions.rawmaterial.dto.production.sorghum.transfer.detail.SorghumTransferDetailListDTO">
        SELECT ssod.*,u.user_name create_name,
	    (SELECT rl.`name` FROM t_mpd_rl_management rl WHERE rl.id = ssod.send_silo_id AND rl.deleted = 0) sendSilo,
	    (SELECT rl.`name` FROM t_mpd_rl_management rl WHERE rl.id = ssod.accept_silo_id AND rl.deleted = 0) acceptSilo
        FROM t_mpd_st_order_detail ssod
        LEFT JOIN authority.sys_user u ON ssod.creator_id = u.id
        WHERE ssod.deleted = 0 AND ssod.order_id = #{id}
    </select>

    <select id="selectDetail_new" resultType="com.hvisions.rawmaterial.dto.production.sorghum.transfer.detail.SorghumTransferDetailListDTO">
        SELECT ssod.*,u.user_name create_name,
        (SELECT rl.`name` FROM t_mpd_material_silo rl WHERE rl.id = ssod.send_silo_id AND rl.deleted = 0) sendSilo,
        (SELECT rl.`name` FROM t_mpd_material_silo rl WHERE rl.id = ssod.accept_silo_id AND rl.deleted = 0) acceptSilo
        FROM t_mpd_st_order_detail ssod
        LEFT JOIN authority.sys_user u ON ssod.creator_id = u.id
        WHERE ssod.deleted = 0 AND ssod.order_id = #{id}
    </select>

    <select id="getSorghumTransferOrderDetailPageList" resultType="com.hvisions.rawmaterial.dto.production.sorghum.transfer.detail.SorghumTransferDetailListDTO">
        SELECT ssod.*,u.user_name create_name,
        (SELECT rl.`name` FROM t_mpd_material_silo rl WHERE rl.id = ssod.send_silo_id AND rl.deleted = 0) sendSilo,
        (SELECT rl.`name` FROM t_mpd_material_silo rl WHERE rl.id = ssod.accept_silo_id AND rl.deleted = 0) acceptSilo
        FROM t_mpd_st_order_detail ssod
        LEFT JOIN authority.sys_user u ON ssod.creator_id = u.id
        WHERE ssod.deleted = 0
        and ssod.flow_batch = #{batch}
        order by ssod.id desc
    </select>

    <select id="selectMaxSerialNoByDate" resultType="java.lang.String">
        SELECT
            MAX(SUBSTRING(order_no, 9, 3)) AS max_serial_no
        FROM
            t_mpd_sorghum_transfer_order
        WHERE
            deleted = 0
            AND order_no LIKE CONCAT('GD', DATE_FORMAT(#{orderDate}, '%y%m%d'), '%')
    </select>

    <select id="selectByOrderDate" resultType="com.hvisions.rawmaterial.entity.TMpdSorghumTransferOrder">
        SELECT
            *
        FROM t_mpd_sorghum_transfer_order
        WHERE deleted = 0
            AND DATE(order_date) = DATE(#{orderDate})
        LIMIT 1
    </select>

    <select id="selectByDateAndMaterialCode" resultType="com.hvisions.rawmaterial.entity.TMpdSorghumTransferOrder">
        SELECT
            *
        FROM t_mpd_sorghum_transfer_order
        WHERE deleted = 0
            AND DATE_FORMAT(order_date, '%Y%m%d') = #{dateStr}
            AND material_code = #{materialCode}
        LIMIT 1
    </select>

</mapper>
