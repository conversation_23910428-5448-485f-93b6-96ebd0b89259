<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.StOrderDetailMapper">

    <select id="selectByMaterialCodeAndQuantityAndTime" resultType="com.hvisions.rawmaterial.entity.TMpdStOrderDetail">
        SELECT
            *
        FROM t_mpd_st_order_detail
        WHERE deleted = 0
            AND material_code = #{materialCode}
            AND control_task_no = #{controlTaskNo}
            AND DATE_FORMAT(actualBeginTime, '%Y%m%d') = #{dateStr}
        LIMIT 1
    </select>

</mapper>
