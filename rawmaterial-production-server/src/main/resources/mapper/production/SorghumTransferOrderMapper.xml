<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.SorghumTransferOrderMapper">

    <!-- 根据日期和物料编码查询转运工单 -->
    <select id="selectByDateAndMaterialCode" resultType="com.hvisions.rawmaterial.entity.TMpdSorghumTransferOrder">
        SELECT
            *
        FROM t_mpd_sorghum_transfer_order
        WHERE deleted = 0
            AND material_code = #{materialCode}
            AND DATE_FORMAT(order_date, '%Y%m%d') = #{dateStr}
        LIMIT 1
    </select>

    <!-- 查询当天最大流水号 -->
    <select id="selectMaxSerialNoByDate" resultType="java.lang.String">
        SELECT
            RIGHT(order_no, 3) as serial_no
        FROM t_mpd_sorghum_transfer_order
        WHERE deleted = 0
            AND DATE_FORMAT(order_date, '%Y%m%d') = DATE_FORMAT(#{orderDate}, '%Y%m%d')
        ORDER BY order_no DESC
        LIMIT 1
    </select>

    <!-- 分页查询高粱转运工单列表 -->
    <select id="getSorghumTransferOrderPageList" resultType="com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageDTO">
        SELECT
            o.id,
            o.order_no,
            o.order_date,
            o.material_code,
            o.material_name,
            o.unit,
            o.actual_quantity,
            o.inventory_quantity,
            o.status,
            o.remark,
            o.create_time,
            o.update_time
        FROM t_mpd_sorghum_transfer_order o
        WHERE o.deleted = 0
        <if test="queryDTO.materialCode != null and queryDTO.materialCode != ''">
            AND o.material_code LIKE CONCAT('%', #{queryDTO.materialCode}, '%')
        </if>
        <if test="queryDTO.materialName != null and queryDTO.materialName != ''">
            AND o.material_name LIKE CONCAT('%', #{queryDTO.materialName}, '%')
        </if>
        <if test="queryDTO.orderNo != null and queryDTO.orderNo != ''">
            AND o.order_no LIKE CONCAT('%', #{queryDTO.orderNo}, '%')
        </if>
        <if test="queryDTO.status != null and queryDTO.status != ''">
            AND o.status = #{queryDTO.status}
        </if>
        <if test="queryDTO.startDate != null">
            AND o.order_date >= #{queryDTO.startDate}
        </if>
        <if test="queryDTO.endDate != null">
            AND o.order_date &lt;= #{queryDTO.endDate}
        </if>
        ORDER BY o.order_date DESC, o.create_time DESC
    </select>

    <!-- 分页查询高粱转运工单详情列表 -->
    <select id="getSorghumTransferOrderDetailPageList" resultType="com.hvisions.rawmaterial.dto.production.sorghum.transfer.detail.SorghumTransferDetailListDTO">
        SELECT
            d.id,
            d.order_id,
            o.order_no,
            o.order_date,
            d.send_silo_name,
            d.accept_silo_name,
            d.material_code,
            d.material_name,
            d.unit,
            d.actual_quantity,
            d.actual_begin_time,
            d.actual_end_time,
            d.control_task_no,
            d.state,
            d.is_manual,
            d.creator_name,
            d.create_time,
            d.update_time
        FROM t_mpd_st_order_detail d
        LEFT JOIN t_mpd_sorghum_transfer_order o ON d.order_id = o.id
        WHERE d.deleted = 0 AND o.deleted = 0
        <if test="queryDTO.materialCode != null and queryDTO.materialCode != ''">
            AND d.material_code LIKE CONCAT('%', #{queryDTO.materialCode}, '%')
        </if>
        <if test="queryDTO.materialName != null and queryDTO.materialName != ''">
            AND d.material_name LIKE CONCAT('%', #{queryDTO.materialName}, '%')
        </if>
        <if test="queryDTO.orderNo != null and queryDTO.orderNo != ''">
            AND o.order_no LIKE CONCAT('%', #{queryDTO.orderNo}, '%')
        </if>
        <if test="queryDTO.controlTaskNo != null and queryDTO.controlTaskNo != ''">
            AND d.control_task_no LIKE CONCAT('%', #{queryDTO.controlTaskNo}, '%')
        </if>
        <if test="queryDTO.state != null and queryDTO.state != ''">
            AND d.state = #{queryDTO.state}
        </if>
        <if test="queryDTO.startDate != null">
            AND o.order_date >= #{queryDTO.startDate}
        </if>
        <if test="queryDTO.endDate != null">
            AND o.order_date &lt;= #{queryDTO.endDate}
        </if>
        ORDER BY o.order_date DESC, d.actual_begin_time DESC
    </select>

</mapper>
