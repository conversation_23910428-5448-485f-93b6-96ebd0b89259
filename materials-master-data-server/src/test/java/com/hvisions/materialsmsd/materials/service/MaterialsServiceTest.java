package com.hvisions.materialsmsd.materials.service;

import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.entity.HvBmBom;
import com.hvisions.materialsmsd.repository.BomRepository;
import com.hvisions.materialsmsd.dao.MaterialMapper;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.materials.dto.MaterialQueryDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterial;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import com.hvisions.materialsmsd.repository.MaterialGroupRepository;
import com.hvisions.materialsmsd.service.MaterialService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class MaterialsServiceTest {


    @Autowired
    MaterialService materialService;

    @Autowired
    MaterialRepository materialRepository;
    @Autowired
    BomRepository bomRepository;

    @Autowired
    MaterialGroupRepository materialGroupRepository;

    @Resource(name = "material_extend")
    BaseExtendService materialExtendService;

    @Autowired(required = false)
    MaterialMapper materialMapper;


    /**
     * 添加物料主数据  测试
     */
    @Test
    @Transactional
    public void createMaterials() {
        HvBmMaterial hvBmMaterial = new HvBmMaterial();
        hvBmMaterial.setMaterialCode("testCode");
        hvBmMaterial.setMaterialDesc("testDesc");
        hvBmMaterial.setMaterialName("testName");
        hvBmMaterial.setMaterialType(materialGroupRepository.findAll().get(1).getId());
        hvBmMaterial.setUom(1);
        hvBmMaterial.setEigenvalue("testEigenvalue");
        int id = materialService.createMaterial(DtoMapper
                .convert(hvBmMaterial, MaterialDTO.class));
        Assert.assertTrue(id > 0);

    }

    /**
     * 更新物料主数据  测试
     */
    @Test
    @Transactional
    public void updateMaterials() {
        createMaterials();
        HvBmMaterial hvBmMaterial = materialRepository.findAll().get(0);
        hvBmMaterial.setMaterialName("updateCode");
        MaterialDTO materialDtos = DtoMapper.convert(hvBmMaterial, MaterialDTO.class);
        materialService.updateMaterial(materialDtos);
        HvBmMaterial materialsDTO = materialRepository.getOne(hvBmMaterial.getId());
        Assert.assertEquals("updateCode", materialsDTO.getMaterialName());
    }

    @Test
    public void contextLoads() {
        HvBmBom user = new HvBmBom();
        user.setBomCode("");
        user.setBomName("");
        user.setBomStatus(1);
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("bomCode", ExampleMatcher.GenericPropertyMatchers.startsWith())//模糊查询匹配开头，即{username}%
                .withMatcher("bomName", ExampleMatcher.GenericPropertyMatchers.contains())//全部模糊查询，即%{address}%
                .withIgnorePaths("bomCode").withIgnoreNullValues();//忽略字段，即不管password是什么值都不加入查询条件

        Example<HvBmBom> example = Example.of(user, matcher);
        List<HvBmBom> list = bomRepository.findAll(example);
        System.out.println(list);
    }

    /***
     * 获取所有物料主数据 测试
     */
    @Test
    @Transactional
    public void getAllLocation() {
        createMaterials();
        Assert.assertTrue(materialRepository.findAll().size() >= 1);
    }

    /**
     * 根据ID列表查询
     */
    @Test
    @Transactional
    public void getMaterialByIdIn() {
        createMaterials();
        List<Integer> list = new ArrayList<>();
        list.add(materialRepository.findAll().get(0).getId());
        list.add(materialRepository.findAll().get(1).getId());
        list.add(materialRepository.findAll().get(2).getId());
        List<MaterialDTO> dtos = materialService.getMaterialsByIdList(list);
        Assert.assertTrue(dtos.size() >= 1);
    }

    @Test
    public void get() {
        MaterialQueryDTO materialQueryDTO = new MaterialQueryDTO();
        List<MaterialDTO> materialByQuery = materialMapper.getMaterialByQuery(materialQueryDTO);
        System.out.println(materialByQuery);
    }


}
