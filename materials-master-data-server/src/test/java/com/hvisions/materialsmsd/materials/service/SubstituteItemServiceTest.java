package com.hvisions.materialsmsd.materials.service;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.bom.dto.SubstituteItemDTO;
import com.hvisions.materialsmsd.entity.HvBmBomItem;
import com.hvisions.materialsmsd.entity.HvBmSubstituteItem;
import com.hvisions.materialsmsd.repository.BomItemRepository;
import com.hvisions.materialsmsd.repository.SubstituteItemRepository;
import com.hvisions.materialsmsd.service.SubstituteItemService;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: SubstituteItemServiceTest</p >
 * <p>Description: SubstituteItemServiceTest </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SubstituteItemServiceTest {
    @Autowired
    SubstituteItemService substituteItemService;
    @Autowired
    SubstituteItemRepository substituteItemRepository;
    @Autowired
    BomItemRepository bomItemRepository;
    @Autowired
    MaterialRepository materialRepository;


    private static final String TEST_NAME = "testSubstituteItemName";
    private static final String TEST_CODE = "testSubstituteItemCode";
    private static final BigDecimal RATIO = BigDecimal.valueOf(10);

    @Test
    @Transactional
    public void createSubstituteItemTest() {
        SubstituteItemDTO substituteItemDTO = new SubstituteItemDTO();
        substituteItemDTO.setSubstituteItemName(TEST_NAME);
        substituteItemDTO.setSubstituteItemCode(TEST_CODE);
        substituteItemDTO.setBomItemId(bomItemRepository.findAll().get(0).getId());
        substituteItemDTO.setMaterialsId(materialRepository.findAll().get(0).getId());
        substituteItemDTO.setRatio(RATIO);
        int id = substituteItemService
                .createSubstituteItem(DtoMapper
                        .convert(substituteItemDTO, SubstituteItemDTO.class));
        Assert.assertTrue(id > 0);
    }

    @Test
    @Transactional
    public void updateSubstituteItemTest() {
        createSubstituteItemTest();
        int id = substituteItemRepository.getBySubstituteItemCode(TEST_CODE).getId();
        HvBmSubstituteItem hvBmSubstituteItem = substituteItemRepository.getOne(id);
        hvBmSubstituteItem.setSubstituteItemCode("testUpdateSuBItem");
        substituteItemService.updateSubstituteItem(DtoMapper.convert(hvBmSubstituteItem, SubstituteItemDTO.class));
        String substituteItem = substituteItemRepository.getOne(id).getSubstituteItemCode();
        Assert.assertEquals("testUpdateSuBItem", substituteItem);
    }


    @Test
    @Transactional
    public void deleteSubstituteItemTest() {
        createSubstituteItemTest();
        HvBmSubstituteItem hvBmSubstituteItem = substituteItemRepository.findAll().get(0);
        int id = hvBmSubstituteItem.getId();
        substituteItemService.deleteSubstituteItem(id);
        boolean substituteItem = substituteItemRepository
                .findAll()
                .stream()
                .noneMatch(t -> t.getId().equals(id));
        Assert.assertTrue(substituteItem);
    }

    @Test
    @Transactional
    public void getAllByBomItemId() {
        createSubstituteItemTest();
        List<SubstituteItemDTO> substituteItemDTOS = substituteItemService.getAllByBomItemId(bomItemRepository.findAll().get(0).getId());
        int id = substituteItemRepository.getBySubstituteItemCode(TEST_CODE).getId();

        boolean substituteItem = substituteItemDTOS
                .stream()
                .noneMatch(t -> t.getId().equals(id));
        Assert.assertFalse(substituteItem);
    }

    @Test
    @Transactional
    public void test100() {
        List<Integer> objects = new ArrayList<>();
        objects.add(2047);
        objects.add(2044);
        List<HvBmBomItem> allByBomIdIn =
                bomItemRepository.getAllByBomIdIn(objects);

        System.out.print(allByBomIdIn.toString());


    }

}
