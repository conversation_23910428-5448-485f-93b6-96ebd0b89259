package com.hvisions.materialsmsd.materials.service;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.bom.dto.BomDTO;
import com.hvisions.materialsmsd.entity.HvBmBom;
import com.hvisions.materialsmsd.repository.BomMaterialRepository;
import com.hvisions.materialsmsd.repository.BomRepository;
import com.hvisions.materialsmsd.service.BomService;
import com.hvisions.materialsmsd.service.UnitService;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: BomServiceTest</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/10</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BomServiceTest {
    @Autowired
    BomService bomService;
    @Autowired
    BomRepository bomRepository;
    @Autowired
    BomMaterialRepository bomMaterialRepository;
    @Autowired
    UnitService unitService;
    @Autowired
    MaterialRepository materialRepository;


    private static final String TEST_NAME = "createName";
    private static final String TEST_CODE = "createBomCode";
    private static final String TEST_Version = "createVersions";
    private static final int ZERO = 0;

    @Test
    @Transactional
    public void createBomTestFirst() {
        BomDTO bomDTO = new BomDTO();

        bomDTO.setBomCode(TEST_CODE);
        bomDTO.setBomVersions(TEST_Version);
        bomDTO.setBomName(TEST_NAME);
        bomDTO.setUnitId(unitService.findAll().get(0).getId());
        int id = bomService.createBom(DtoMapper.convert(bomDTO, BomDTO.class));
        Assert.assertTrue(id > ZERO);
    }


    @Test
    @Transactional
    public void createBomTestSecond() {
        BomDTO bomDTO = new BomDTO();

        bomDTO.setBomCode(TEST_CODE);
        bomDTO.setBomVersions(TEST_Version);
        bomDTO.setBomName(TEST_NAME);
        bomDTO.setUnitId(0);
        bomDTO.setMaterialsId(materialRepository.findAll().get(0).getId());
        Throwable throwable = null;
        int id = 0;
        try {
            id = bomService.createBom(bomDTO);
        } catch (Throwable ex) {
            throwable = ex;
        }
        Assert.assertTrue(throwable != null);
        Assert.assertEquals(throwable.getClass(), BaseKnownException.class);

        log.info(throwable.getMessage());

        bomDTO.setUnitId(unitService.findAll().get(0).getId());
        id = bomService.createBom(bomDTO);
        Assert.assertTrue(id > ZERO);
        int bomMaterialId = bomMaterialRepository.getHvBomMaterialByMaterialId(materialRepository.findAll().get(0).getId()).getBomId();
        Assert.assertEquals(bomMaterialId, id);
    }

//    @Test
//    public void createBomTest3() {
//        List<HvBmBom> list = new ArrayList<>();
//        for (int i = 0; i < 100; i++) {
//            HvBmBom hvBmBom = new HvBmBom();
//            hvBmBom.setBomCode(TEST_CODE + i);
//            hvBmBom.setBomVersions(TEST_Version + i);
//            hvBmBom.setBomName(TEST_NAME + i);
//            hvBmBom.setUnitId(2);
//            list.add(hvBmBom);
//        }
//        bomRepository.saveAll(list);
//    }


    @Test
    @Transactional
    public void updateBomTest() {
        createBomTestFirst();
        int bomId = bomRepository.findByBomCodeAndBomVersions(TEST_CODE, TEST_Version).getId();
        HvBmBom hvBmBom = bomRepository.getAllById(bomId);
        hvBmBom.setBomName("updateBomName");
        bomService.updateBom(DtoMapper.convert(hvBmBom, BomDTO.class));
        HvBmBom bom1 = bomRepository.getAllById(bomId);
        Assert.assertEquals("updateBomName", bom1.getBomName());
    }


    @Test
    @Transactional
    public void getBomByMaterialsIdTest() {
        createBomTestSecond();

        List<BomDTO> bomDto = bomService.getBomByMaterialsId(materialRepository.findAll().get(0).getId());
        Integer bomId = bomMaterialRepository.getHvBomMaterialByMaterialId(materialRepository.findAll().get(0).getId()).getBomId();
        Assert.assertTrue(bomDto.size() > 0);
        boolean bom = bomDto
                .stream()
                .noneMatch(t -> t.getId().equals(bomId));
        Assert.assertFalse(bom);
    }


    @Test
    @Transactional
    public void deleteBomByIdTest() {
        createBomTestFirst();
        HvBmBom bom = bomRepository.findAll().get(0);
        int id = bom.getId();
        bomService.deleteBom(id);
        boolean hvbom = bomRepository
                .findAll()
                .stream()
                .noneMatch(t -> t.getId().equals(id));
        Assert.assertTrue(hvbom);

    }


    @Test
    @Transactional
    public void setStatusTakeEffectTest() {
        createBomTestFirst();
        int id = bomRepository.findByBomCodeAndBomVersions(TEST_CODE, TEST_Version).getId();
        bomService.setStatusTakeEffect(id);
        Assert.assertTrue(bomRepository.findByBomCodeAndBomVersions(TEST_CODE, TEST_Version).getBomStatus() == 1);
    }

}
