package com.hvisions.materialsmsd.materials.service;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.bom.dto.BomItemDTO;
import com.hvisions.materialsmsd.bom.dto.BomItemIncreaseDTO;
import com.hvisions.materialsmsd.entity.HvBmBomItem;
import com.hvisions.materialsmsd.bom.enums.BomItemTypeEnum;
import com.hvisions.materialsmsd.repository.BomItemRepository;
import com.hvisions.materialsmsd.repository.BomRepository;
import com.hvisions.materialsmsd.service.BomItemService;
import com.hvisions.materialsmsd.service.BomService;
import com.hvisions.materialsmsd.service.MaterialGroupService;
import com.hvisions.materialsmsd.service.MaterialService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * <p>Title: BomItemServiceTest</p >
 * <p>Description: BomItemServiceTest </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BomItemServiceTest {


    @Autowired
    BomItemService bomItemService;
    @Autowired
    BomRepository bomRepository;

    @Autowired
    BomService bomService;
    @Autowired
    MaterialService materialService;
    @Autowired
    MaterialGroupService materialGroupService;
    @Autowired
    BomItemRepository bomItemRepository;


    private static final String TEST_CODE = "testBomItemCode";
    private static final String TEST_NAME = "testBomItemName";
    private static final BigDecimal TEST_COUNT = BigDecimal.valueOf(0.5);


    @Test
    @Transactional
    public void createBomItemTest() {
        BomItemDTO bomItemDTO = new BomItemDTO();
        bomItemDTO.setBomItemCode(TEST_CODE);
        bomItemDTO.setBomItemName(TEST_NAME);
        bomItemDTO.setBomItemCount(TEST_COUNT);
        bomItemDTO.setBomId(bomRepository.findAll().get(0).getId());
        bomItemDTO.setMaterialsId(materialService.getAllMaterials().get(0).getId());
        bomItemDTO.setBomItemType(2);
        int id = bomItemService.createBomItem(DtoMapper.convert(bomItemDTO, BomItemDTO.class));
        Assert.assertTrue(id > 0);
    }

    @Test
    @Transactional
    public void updateBomItemTest() {
        createBomItemTest();
        int id = bomItemRepository.getByBomItemCode(TEST_CODE).getId();
        HvBmBomItem hvBmBom = bomItemRepository.getById(id);
        hvBmBom.setBomItemCode("updateTest");
        hvBmBom.setBomId(bomRepository.findAll().get(2).getId());
        int setBomId = bomRepository.findAll().get(2).getId();
        bomItemService.updateBomItem(DtoMapper.convert(hvBmBom, BomItemDTO.class));
        HvBmBomItem hvBmBom1 = bomItemRepository.getById(id);
        int bomId = bomItemRepository.getById(id).getBomId();
        Assert.assertEquals("updateTest", hvBmBom1.getBomItemCode());
        Assert.assertEquals(setBomId, bomId);
    }


    public void deleteBomItemTest() {
        createBomItemTest();

        HvBmBomItem bomItem = bomItemRepository.findAll().get(0);
        int id = bomItem.getId();
        bomItemService.deleteBomItem(id);
        boolean hvbomItem = bomItemRepository
                .findAll()
                .stream()
                .noneMatch(t -> t.getId().equals(id));
        Assert.assertTrue(hvbomItem);


    }


    @Test
    @Transactional
    public void testEnum() {
        createBomItemTest();
        HvBmBomItem hvBmBomItem = bomItemRepository.getByBomItemCode(TEST_CODE);
        Integer typeId = hvBmBomItem.getBomItemType();
        BomItemTypeEnum bomItemTypeEnum = BomItemTypeEnum.valueOf(typeId);
        System.out.print("===================" + bomItemTypeEnum);
        System.out.print("-------------------" + bomItemTypeEnum.getCode());
    }


    @Test
    public void testBigDecimal1() {
        BigDecimal bigDecimal = new BigDecimal(1);
        BigDecimal b = BigDecimal.valueOf(2);
        bigDecimal.compareTo(b);
        boolean s = bigDecimal.compareTo(b) == -1;
        System.out.print("--------------" + s);
    }

    @Test
    @Transactional
    public void getBomItemByIdTest() {
        createBomItemTest();
        Integer id = bomItemRepository.getByBomItemCode(TEST_CODE).getId();
        BomItemIncreaseDTO bomItemIncreaseDTO = bomItemService.getBomItemById(id);
        boolean testMaterialCode = bomItemIncreaseDTO.getMaterialCode()
                .equals(materialService.getMaterialById
                        (bomItemIncreaseDTO.getMaterialsId()).getMaterialCode());
        boolean testBomItemCode = bomItemIncreaseDTO.getBomItemCode().equals(TEST_CODE);

        Assert.assertTrue(testMaterialCode);
        Assert.assertTrue(testBomItemCode);
    }
}
