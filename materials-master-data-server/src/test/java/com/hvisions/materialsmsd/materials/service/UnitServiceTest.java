package com.hvisions.materialsmsd.materials.service;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.bom.dto.UnitDTO;
import com.hvisions.materialsmsd.entity.HvBmUnit;
import com.hvisions.materialsmsd.service.UnitService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: UnitServiceTest</p >
 * <p>Description: 计量单位表测试</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/10</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class UnitServiceTest {
    @Autowired
    UnitService unitService;


    @Test
    @Transactional
    public void createUnitTest() {
        UnitDTO unitDTO = new UnitDTO();
        unitDTO.setSymbol("v");
        unitDTO.setDescription("伏特");
        int id = unitService.createUnit(unitDTO);
        Assert.assertTrue(id > 0);


        Assert.assertEquals("v", unitService.getUnitById(id).getSymbol());
        Assert.assertEquals("伏特", unitService.getUnitById(id).getDescription());
    }

    @Test
    @Transactional
    public void updateUnitTest() {
        createUnitTest();
        HvBmUnit unit = unitService.findAll().get(0);
        unit.setDescription("update伏特");
        int hvBmUnit = unitService.updateUnit(DtoMapper.convert(unit, UnitDTO.class));
        Assert.assertEquals("update伏特", unitService.getUnitById(hvBmUnit).getDescription());

    }

    @Test
    @Transactional
    public void findAllTest() {

        createUnitTest();
        List<HvBmUnit> hvBmUnits = unitService.findAll();
        Assert.assertTrue(hvBmUnits.size() > 0);
    }

    @Test
    @Transactional
    public void deleteUnitByIdTest() {
        createUnitTest();
        HvBmUnit unit = unitService.findAll().get(0);
        int id = unit.getId();

        unitService.deleteUnit(id);
        boolean hvBmUnits = unitService
                .findAll()
                .stream()
                .noneMatch(t -> t.getId().equals(id));
        Assert.assertTrue(hvBmUnits);


    }




}
