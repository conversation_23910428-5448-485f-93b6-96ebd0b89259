package com.hvisions.materialsmsd.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * <p>Title: HvBmBomMaterial</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/5</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Entity
@Data
public class HvBmBomMaterial {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * bom id
     */

    private Integer bomId;

    /**
     * material Id
     */

    private Integer materialId;

}
