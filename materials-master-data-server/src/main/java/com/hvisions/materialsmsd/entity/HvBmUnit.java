package com.hvisions.materialsmsd.entity;

import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;

/**
 * <p>Title: HvBmUnit</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/1</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@Entity
@Table(uniqueConstraints =
        {@UniqueConstraint(name = "单位编码唯一", columnNames = "symbol"),
                @UniqueConstraint(name = "单位描述唯一", columnNames = "description")})
public class HvBmUnit {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 单位编码
     */
    @NotBlank(message = "单位编码不能为空")
    private String symbol;
    /**
     * 中文符号
     */
    @NotBlank(message = "单位中文描述不能为空")
    private String description;

}
