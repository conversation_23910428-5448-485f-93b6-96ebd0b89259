package com.hvisions.materialsmsd.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotBlank;

/**
 * <p>Title: HvBmMaterialProperty</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
public class HvBmMaterialProperty extends SysBase {

    /**
     * 属性编码
     */
    @Column(updatable = false)
    @Length(max = 200)
    @NotBlank(message = "属性编码不能为空")
    private String code;

    /**
     * 设备属性类型编码
     */
    @Column(updatable = false)
    private String classCode;

    /**
     * 设备属性类型名称
     */
    @Column(updatable = false)
    private String className;


    /**
     * classId
     */
    @Column(updatable = false)
    private Integer classId;


    /**
     * 属性名
     */
    @Length(max = 200)
    @NotBlank(message = "属性名称不能为空")
    private String name;


    /**
     * 属性数据类型
     */
    @Column(updatable = false)
    private Integer dateType;

    /**
     * 是否是常量
     */
    @Column(updatable = false)
    private Boolean isConst = false;

    /**
     * 字符串值
     */
    @Length(max = 200, message = "最大支持200个字符")
    private String stringValue;
    /**
     * long型值
     */
    private Long longValue;
    /**
     * float型值
     */
    private Float floatValue;
    /**
     * 字符串原值
     */
    private String value;

    @ManyToOne
    private HvBmMaterial material;
}