package com.hvisions.materialsmsd.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotNull;

/**
 * <p>Title: HvBmMaterialType</p >
 * <p>Description: 物料分类 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-06</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@Entity
@Table(uniqueConstraints = {@UniqueConstraint(name = "物料类型名称不可重复", columnNames = "materialTypeName"),
        @UniqueConstraint(name = "物料类型编码不可重复", columnNames = "materialTypeCode")})
public class HvBmMaterialType extends SysBase {

    /**
     * 物料类型名称（唯一且不可修改）
     */
    @NotNull(message = "物料类型名称不能为空")
    private String materialTypeName;

    /**
     * 物料类型编码
     */
    @NotNull(message = "物料类型编码不能为空")
    @Column(unique = true, updatable = false)
    private String materialTypeCode;


    /**
     * 物料类型备注
     */
    private String materialTypeDesc;

    /**
     * 类型父级Id
     */
    private Integer parentId;

    /**
     * 排序字段
     */
    private Integer sortNum;


}