package com.hvisions.materialsmsd.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>Title: HvBmMaterialClass</p >
 * <p>Description: 物料类型属性</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(uniqueConstraints = @UniqueConstraint(columnNames = "code", name = "属性类型编码不能重复"))
public class HvBmMaterialClass extends SysBase {

    @Column(updatable = false)
    @NotBlank(message = "物料属性类型编码不能为空")
    @Length(max = 200)
    private String code;

    @NotBlank(message = "物料属性类型名称不能为空")
    @Length(max = 200)
    private String name;

    @OneToMany(mappedBy = "clazz", cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    private List<HvBmMaterialClassProperty> properties;

    @ManyToMany(mappedBy = "classes")
    private List<HvBmMaterial> materials;

}