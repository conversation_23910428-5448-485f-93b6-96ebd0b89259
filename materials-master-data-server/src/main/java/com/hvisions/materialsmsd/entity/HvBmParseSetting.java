package com.hvisions.materialsmsd.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * <p>Title: HvBmParseSetting</p>
 * <p>Description: 物料编码规则设置</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/14</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Entity
@Data
public class HvBmParseSetting {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;
    /**
     * 物料编码开始位置
     */
    private Integer codeStart;
    /**
     * 物料编码结束位置
     */
    private Integer codeEnd;
    /**
     * 物料特征值开始位置
     */
    private Integer eigenStart;
    /**
     * 物料特征值结束位置
     */
    private Integer eigenEnd;
    /**
     * 正则表达字符串
     */
    private String regular;
    /**
     * 解析规则，1：位置，2：正则表达式
     */
    private Integer parseType;
}









