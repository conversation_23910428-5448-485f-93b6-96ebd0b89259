package com.hvisions.materialsmsd.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>Title: HvBmBom</p >
 * <p>Description: bom</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/26</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Table(uniqueConstraints = @UniqueConstraint(name = "编码版本唯一", columnNames = {"bomCode", "bomVersions"}))
public class HvBmBom extends SysBase {

    /**
     * bom编码（非空）
     */
    @NotBlank(message = "bom编码不能为空")
    @Length(min = 1, max = 100, message = "bom编码支持1-100个字符")
    private String bomCode;
    /**
     * bom名称（非空）
     */
    @NotBlank(message = "bom名称为空")
    @Length(min = 1, max = 100, message = "bom名称支持1-100个字符")
    private String bomName;
    /**
     * bom描述
     */
    private String bomDesc;
    /**
     * bom版本（非空）
     */
    @NotBlank(message = "bom版本不能为空")
    @Length(min = 1, max = 100, message = "bom版本支持1-100个字符")
    private String bomVersions;
    /**
     * bom状态
     */
    @NotNull
    private Integer bomStatus;
    /**
     * 数量
     */
    @Min(value = 0, message = "bom数量不能小于0")
    private BigDecimal bomCount;
    /**
     * 计量单位
     */
    @NotNull(message = "计量单位不能为空")
    private Integer unitId;


}
