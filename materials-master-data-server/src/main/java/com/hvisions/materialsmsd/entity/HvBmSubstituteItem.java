package com.hvisions.materialsmsd.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>Title: HvBmSubstituteItem</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@Entity
@Table(uniqueConstraints = @UniqueConstraint(name = "替代物料编码和bomitemId俩列唯一", columnNames = {"substituteItemCode", "bomItemId"}))
public class HvBmSubstituteItem extends SysBase {


    /**
     * 替代物编码
     */
    @NotBlank(message = "替代物编码不能为空")
    private String substituteItemCode;

    /**
     * 替代物名称
     */
    @NotBlank(message = "替代物名称不能为空")
    private String substituteItemName;

    /**
     * materials ID
     */
    @NotNull(message = "物料主键不能为空")
    private Integer materialsId;
    /**
     * bomItem ID
     */
    @NotNull(message = "bomItem主键不能为空")
    private Integer bomItemId;
    /**
     * 替代比例
     */
    @NotNull(message = "替代比例不能为空")
    private BigDecimal ratio;

}
