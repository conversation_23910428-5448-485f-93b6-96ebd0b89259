package com.hvisions.materialsmsd.entity;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;

/**
 * <p>Title: HvBmMaterialGroup</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/4</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Entity
@Data
@Table(uniqueConstraints = @UniqueConstraint(name = "物料分组编码不允许重复", columnNames = "groupCode"))
public class HvBmMaterialGroup {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;
    /**
     * 物料类型编码
     */
    @NotBlank(message = "物料类型编码不能为空")
    @Length(min = 1, max = 100, message = "物料类型编码支持1-100个字符")
    @Column(updatable = false)
    private String groupCode;


    /**
     * 物料类型描述
     */
    @NotBlank(message = "物料类型名称不能为空")
    private String groupName;

}
