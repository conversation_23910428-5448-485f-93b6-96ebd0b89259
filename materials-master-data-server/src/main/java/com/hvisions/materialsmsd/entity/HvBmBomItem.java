package com.hvisions.materialsmsd.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>Title: HvBmBomItem</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/27</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Table(uniqueConstraints = @UniqueConstraint(name = "物料ID和BomId两列唯一", columnNames = {"bomId", "materialsId"}))
public class HvBmBomItem extends SysBase {
    /**
     * bom 主键
     */
    @NotNull(message = "bom主键不能为空")
    private Integer bomId;
    /**
     * bomItem编码（非空）
     */
    @NotBlank(message = "bomItem编码不能为空")
    private String bomItemCode;
    /**
     * bomItem类型
     */
    @NotNull(message = "bomItem类型不能为空")
    private Integer bomItemType;
    /**
     * bomItem数量
     */

    private BigDecimal bomItemCount;
    /**
     * bomItem名称（非空）
     */
    @NotBlank(message = "bomItem名称不能为空")
    private String bomItemName;
    /**
     * materials主键
     */
    @NotNull(message = "物料主键不能为空")
    private Integer materialsId;


}
