package com.hvisions.materialsmsd.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>Title: HvBmMaterial</p>
 * <p>Description: 行政部门表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(uniqueConstraints = {@UniqueConstraint(name = "编码和特征值俩列唯一", columnNames = {"materialCode", "eigenvalue"})})
public class HvBmMaterial extends SysBase {
    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    @Column(updatable = false)
    @Length(min = 1, max = 100, message = "物料编码支持1-100字符")
    private String materialCode;

    /**
     * 物料名称
     */
    @NotBlank(message = "物料名称不允许为空")
    private String materialName;

    /**
     * 物料类型
     */
    @NotNull(message = "物料类型不允许为空")
    private Integer materialType;
    /**
     * 根物料类型（用于根类型查询）
     */
    private Integer rootMaterialType;

    /**
     * 物料描述
     */
    private String materialDesc;

    /**
     * 物料分组
     */
    private Integer materialGroup;
    /**
     * 单位
     */
    @NotNull(message = "计量单位不允许为空")
    private Integer uom;
    /**
     * 是否追溯
     */
    private Boolean serialNumberProfile;

    /**
     * 系统代码
     */
    private Integer sysNum;
    /***
     * 特征值
     */
    @Column(updatable = false)
    private String eigenvalue;

    /**
     * 图片ID
     */
    private Integer photoId;

    /**
     * 物料类型属性
     */
    @ManyToMany
    @JoinTable(name = "hv_bm_material_material_class", joinColumns = @JoinColumn(name = "material_id"),
            inverseJoinColumns = @JoinColumn(name = "material_class_id"))
    List<HvBmMaterialClass> classes;

    /**
     * 物料属性
     */
    @OneToMany(mappedBy = "material", cascade = CascadeType.ALL)
    List<HvBmMaterialProperty> properties;

}
