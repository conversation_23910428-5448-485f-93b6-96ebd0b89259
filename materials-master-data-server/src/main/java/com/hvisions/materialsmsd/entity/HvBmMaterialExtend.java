package com.hvisions.materialsmsd.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * <p>Title: HvBmMaterialExtend</p >
 * <p>Description: 物料扩展表</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/6/13</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@Entity
public class HvBmMaterialExtend {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 物料ID
     */
    private Integer materialId;

}
