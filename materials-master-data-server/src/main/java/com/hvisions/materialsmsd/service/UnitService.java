package com.hvisions.materialsmsd.service;

import com.hvisions.common.service.BaseService;
import com.hvisions.materialsmsd.bom.dto.UnitDTO;
import com.hvisions.materialsmsd.entity.HvBmUnit;
import com.hvisions.materialsmsd.query.UnitQuery;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: UnitService</p >
 * <p>Description: 计量单位服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/3</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface UnitService extends BaseService<HvBmUnit, Integer> {

    /**
     * 获取所有计量单位信息
     *
     * @return 计量单位信息列表
     */
    @Override
    List<HvBmUnit> findAll();

    /**
     * 增加unit
     *
     * @param unitDTO 单位信息
     * @return 新增ID
     */
    int createUnit(UnitDTO unitDTO);

    /**
     * 根据ID删除计量单位
     *
     * @param id id
     */
    void deleteUnit(Integer id);

    /**
     * 更新unit
     *
     * @param unitDTO 传入对象
     * @return id
     */
    int updateUnit(UnitDTO unitDTO);

    /**
     * 根据ID获取计量单位
     *
     * @param id 计量单位ID
     * @return unitDto 计量单位信息
     */
    UnitDTO getUnitById(Integer id);

    /**
     * 分页查询
     *
     * @param unitDTO 传入对象
     * @return 分页信息
     */
    Page<UnitDTO> findUnitBySymbolOrDescription(UnitQuery unitDTO);

    /**
     * 初始化单位;
     */
    void initialUnits();

}
