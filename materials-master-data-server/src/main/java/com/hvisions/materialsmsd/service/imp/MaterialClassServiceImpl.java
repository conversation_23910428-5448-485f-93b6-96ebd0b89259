package com.hvisions.materialsmsd.service.imp;

import cn.hutool.core.lang.Assert;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.materials.classdto.MaterialClassDTO;
import com.hvisions.materialsmsd.materials.classdto.MaterialClassQuery;
import com.hvisions.materialsmsd.entity.HvBmMaterial;
import com.hvisions.materialsmsd.entity.HvBmMaterialClass;
import com.hvisions.materialsmsd.entity.HvBmMaterialProperty;
import com.hvisions.materialsmsd.repository.MaterialClassRepository;
import com.hvisions.materialsmsd.repository.MaterialPropertyRepository;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import com.hvisions.materialsmsd.service.MaterialClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: MaterialClassServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class MaterialClassServiceImpl implements MaterialClassService {

    private final MaterialPropertyRepository materialPropertyRepository;
    private final MaterialClassRepository materialClassRepository;

    private final MaterialRepository materialRepository;

    @Autowired
    public MaterialClassServiceImpl(MaterialPropertyRepository materialPropertyRepository, MaterialClassRepository materialClassRepository, MaterialRepository materialRepository) {
        this.materialPropertyRepository = materialPropertyRepository;
        this.materialClassRepository = materialClassRepository;
        this.materialRepository = materialRepository;
    }

    /**
     * 创建物料属性类型
     *
     * @param classDTO 属性类型
     * @return 主键
     */
    @Override
    public Integer create(MaterialClassDTO classDTO) {
        Assert.isNull(classDTO.getId(), "创建时不能传递主键");
        HvBmMaterialClass clazz = materialClassRepository.save(DtoMapper.convert(classDTO, HvBmMaterialClass.class));
        return clazz.getId();
    }

    /**
     * 修改物料属性类型
     *
     * @param classDTO 属性类型
     */
    @Override
    public void update(MaterialClassDTO classDTO) {
        Assert.notNull(classDTO.getId(), "更新时主键不能为空");
        Optional<HvBmMaterialClass> clazz = materialClassRepository.findById(classDTO.getId());
        clazz.ifPresent(c -> {
            c.setName(classDTO.getName());
            materialClassRepository.save(c);
        });
    }

    /**
     * 获取物料属性类型
     *
     * @param id 属性类型主键
     * @return 属性类型
     */
    @Override
    public MaterialClassDTO findById(Integer id) {
        return materialClassRepository.findById(id)
                .map(t -> DtoMapper.convert(t, MaterialClassDTO.class))
                .orElse(null);
    }

    /**
     * 获取物料属性类型
     *
     * @param code 属性类型编码
     * @return 属性类型
     */
    @Override
    public MaterialClassDTO findByCode(String code) {
        return DtoMapper.convert(materialClassRepository.findByCode(code), MaterialClassDTO.class);
    }

    /**
     * 获取物料属性类型分页数据
     *
     * @param query 属性类型编码
     * @return 属性类型分页数据
     */
    @Override
    public Page<MaterialClassDTO> findPage(MaterialClassQuery query) {
        return DtoMapper.convertPage(
                materialClassRepository.findAllByCodeContainsOrNameContaining(
                        query.getCodeOrName(), query.getCodeOrName(), query.getRequest())
                , MaterialClassDTO.class);
    }

    /**
     * 删除物料属性类型
     *
     * @param id 属性类型
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {
        Optional<HvBmMaterialClass> clazz = materialClassRepository.findById(id);
        //需要把配置了类型的物料的属性全部删除
        clazz.ifPresent(t -> {
            List<HvBmMaterial> materials = t.getMaterials();
            for (HvBmMaterial material : materials) {
                material.getClasses().remove(t);
                List<HvBmMaterialProperty> properties = material.getProperties().stream()
                        .filter(m -> m.getClassCode().equals(t.getCode())).collect(Collectors.toList());
                material.getProperties().removeAll(properties);
                materialRepository.save(material);
                materialPropertyRepository.deleteAll(properties);
            }
        });
        materialClassRepository.deleteById(id);
    }

    /**
     * 根据物料id查询物料属性列表
     *
     * @param id 物料di
     * @return 物料属性类型列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MaterialClassDTO> findByMaterialId(Integer id) {
        Optional<HvBmMaterial> material = materialRepository.findById(id);
        List<MaterialClassDTO> classDTOS = new ArrayList<>();
        material.ifPresent(t -> {
            List<MaterialClassDTO> classes = DtoMapper.convertList(t.getClasses(), MaterialClassDTO.class);
            classDTOS.addAll(classes);
        });
        return classDTOS;
    }

    /**
     * 向物料添加物料属性类型
     *
     * @param materialId 物料di
     * @param classId    属性类型id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addClassToMaterial(Integer materialId, Integer classId) {
        Optional<HvBmMaterial> material = materialRepository.findById(materialId);
        Optional<HvBmMaterialClass> materialClass = materialClassRepository.findById(classId);
        Assert.isTrue(material.isPresent(), "物料信息不存在");
        Assert.isTrue(materialClass.isPresent(), "物料属性类型不存在");
        Assert.isFalse(material.get().getClasses().contains(materialClass.get()), "物料已经存在此类型");
        List<HvBmMaterialProperty> hvBmMaterialProperties = DtoMapper.convertList(materialClass.get().getProperties(),
                HvBmMaterialProperty.class);
        hvBmMaterialProperties.forEach(t -> {
            t.setClassCode(materialClass.get().getCode());
            t.setClassName(materialClass.get().getName());
            t.setId(null);
            t.setClassId(materialClass.get().getId());
            t.setMaterial(material.get());
        });
        material.get().getClasses().add(materialClass.get());
        material.get().getProperties().addAll(hvBmMaterialProperties);
        materialRepository.save(material.get());
    }

    /**
     * 删除物料的物料属性类型
     *
     * @param materialId 物料di
     * @param classId    属性类型id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeClassToMaterial(Integer materialId, Integer classId) {
        Optional<HvBmMaterial> material = materialRepository.findById(materialId);
        Optional<HvBmMaterialClass> materialClass = materialClassRepository.findById(classId);
        Assert.isTrue(material.isPresent(), "物料信息不存在");
        Assert.isTrue(materialClass.isPresent(), "物料属性类型不存在");
        if (material.get().getClasses().contains(materialClass.get())) {
            material.get().getClasses().remove(materialClass.get());
            List<HvBmMaterialProperty> propertyList = material.get().getProperties().stream()
                    .filter(t -> t.getClassCode() != null)
                    .collect(Collectors.toList());
            propertyList =
                    propertyList.stream().filter(t -> t.getClassCode().equals(materialClass.get().getCode())).collect(Collectors.toList());
            materialPropertyRepository.deleteAll(propertyList);
            material.get().getProperties().removeAll(propertyList);
            materialRepository.save(material.get());
        }
    }
}