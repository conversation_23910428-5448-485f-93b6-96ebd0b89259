package com.hvisions.materialsmsd.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.materials.dto.MaterialTypeDTO;
import com.hvisions.materialsmsd.materials.dto.MaterialTypeQueryDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterialType;
import com.hvisions.materialsmsd.materials.enums.MaterialExceptionEnum;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import com.hvisions.materialsmsd.repository.MaterialTypeRepository;
import com.hvisions.materialsmsd.service.MaterialTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: MaterialTypeServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-06</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class MaterialTypeServiceImpl implements MaterialTypeService {


    private final MaterialTypeRepository materialTypeRepository;

    private final MaterialRepository materialRepository;

    private final int ZERO = 0;


    @Autowired
    public MaterialTypeServiceImpl(MaterialTypeRepository materialTypeRepository, MaterialRepository materialRepository) {
        this.materialTypeRepository = materialTypeRepository;
        this.materialRepository = materialRepository;
    }

    /**
     * 创建物料类型
     *
     * @param materialTypeDTO 物料类型对象
     * @return 物料类型对象
     */
    @Override
    public MaterialTypeDTO createMaterialType(MaterialTypeDTO materialTypeDTO) {
        if (materialTypeDTO.getParentId() != null) {
            materialTypeDTO.setSortNum(999);
        } else {
            materialTypeDTO.setParentId(0);
            materialTypeDTO.setSortNum(7);
        }
        HvBmMaterialType save = materialTypeRepository.save(DtoMapper.convert(materialTypeDTO, HvBmMaterialType.class));

        return DtoMapper.convert(save, MaterialTypeDTO.class);
    }

    /**
     * 更新物料类型
     *
     * @param materialTypeDTO 物料类型对象
     * @return 物料类型对象
     */
    @Override
    public MaterialTypeDTO updateMaterialType(MaterialTypeDTO materialTypeDTO) {
        HvBmMaterialType save = materialTypeRepository.save(DtoMapper.convert(materialTypeDTO,
                HvBmMaterialType.class));
        return DtoMapper.convert(save, MaterialTypeDTO.class);
    }

    /**
     * 根据物料类型ID删除物料类型
     *
     * @param id 物料类型ID
     */
    @Override
    public void deleteTypeById(int id) {
        if (materialRepository.existsByMaterialType(id)) {
            throw new BaseKnownException(MaterialExceptionEnum.MATERIAL_TYPE_IS_USE);
        }
        List<MaterialTypeDTO> materialTypeByParentId = getMaterialTypeByParentId(id);
        if (materialTypeByParentId.size() > 0) {
            throw new BaseKnownException(MaterialExceptionEnum.MATERIAL_TYPE_HAVE_DATA);
        }
        materialTypeRepository.deleteById(id);
    }


    /**
     * 根据物料类型ID查询物料类型
     *
     * @param id ID
     * @return 物料  类型信息
     */
    @Override
    public MaterialTypeDTO getMaterialTypeById(int id) {
        return DtoMapper.convert(materialTypeRepository.getOne(id), MaterialTypeDTO.class);
    }

    /**
     * 根据物料类型ID列表查询物料类型信息列表
     *
     * @param idIn 物料类型ID列表
     * @return 物料类型信息列表
     */
    @Override
    public List<MaterialTypeDTO> getMaterialTypeListByIdIn(List<Integer> idIn) {
        return DtoMapper.convertList(materialTypeRepository.getAllByIdIn(idIn), MaterialTypeDTO.class);
    }

    /**
     * 根据物料编码查询物料类型信息
     *
     * @param code 物料类型Code
     * @return 物料类型信息≈¬
     */
    @Override
    public MaterialTypeDTO getMaterialTypeByCode(String code) {
        return DtoMapper.convert(materialTypeRepository.getAllByMaterialTypeCode(code), MaterialTypeDTO.class);
    }

    /**
     * 获取所有物料类型
     *
     * @return 物料类型列表
     */
    @Override
    public List<MaterialTypeDTO> findAll() {
        List<MaterialTypeDTO> typeDTOS = DtoMapper.convertList(materialTypeRepository.findAll(), MaterialTypeDTO.class);
        List<MaterialTypeDTO> collect =
                typeDTOS.stream().sorted(Comparator.comparing(MaterialTypeDTO::getSortNum,
                        Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
        return collect;
    }

    /**
     * 获取根结点物料类型
     */
    @Override
    public List<MaterialTypeDTO> getRootMaterialType() {
        List<MaterialTypeDTO> typeDTOS = DtoMapper.convertList(materialTypeRepository.getAllByParentId(ZERO), MaterialTypeDTO.class);
        List<MaterialTypeDTO> collect =
                typeDTOS.stream().sorted(Comparator.comparing(MaterialTypeDTO::getSortNum,
                        Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
        return collect;
    }

    /**
     * 根据物料类型父级别ID查询
     *
     * @param parentId 父级ID
     */
    @Override
    public List<MaterialTypeDTO> getMaterialTypeByParentId(int parentId) {
        return DtoMapper.convertList(materialTypeRepository.getAllByParentId(parentId), MaterialTypeDTO.class);
    }

    /**
     * 分页查询物料类型
     *
     * @param materialTypeQueryDTO 物料类型查询条件
     * @return 物料类型分页信息
     */
    @Override
    public Page<MaterialTypeDTO> getMaterialTypeByQuery(MaterialTypeQueryDTO materialTypeQueryDTO) {


        Page<HvBmMaterialType> hvBmMaterialTypes;
        materialTypeQueryDTO.setSortCol("sortNum");
        materialTypeQueryDTO.setSort(true);
        //转换DTO
        if (materialTypeQueryDTO.getMaterialTypeCode() == null && materialTypeQueryDTO.getMaterialTypeName() == null) {
            materialTypeQueryDTO.setParentId(ZERO);
        }
        HvBmMaterialType hvBmMaterialType = DtoMapper.convert(materialTypeQueryDTO, HvBmMaterialType.class);
        //查询
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("materialTypeCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("materialTypeName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("parentId", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        Example<HvBmMaterialType> example = Example.of(hvBmMaterialType, exampleMatcher);
        hvBmMaterialTypes = materialTypeRepository.findAll(example, materialTypeQueryDTO.getRequest());
        return DtoMapper.convertPage(hvBmMaterialTypes, MaterialTypeDTO.class);
    }

    /**
     * 根据子级节点获取上级id
     *
     * @param materialTypeId 物料类型ID
     * @return id列表
     */
    @Override
    public List<Integer> getParentIdBySonId(int materialTypeId) {
        List<Integer> list = new ArrayList<>();
        int parentId;
        list.add(materialTypeId);
        Optional<HvBmMaterialType> HvMaterialType = materialTypeRepository.findById(materialTypeId);
        if (HvMaterialType.isPresent()) {
            HvBmMaterialType materialType = HvMaterialType.get();
            while (materialType.getParentId() != 0) {
                parentId = materialType.getParentId();
                materialType = materialTypeRepository.getOne(parentId);
                list.add(materialType.getId());
            }
            Collections.reverse(list);
        }
        return list;
    }

    /**
     * 获取成品半成品对象
     *
     * @return 成品半成品信息
     */
    @Override
    public List<MaterialTypeDTO> getProduct() {
        HvBmMaterialType product = materialTypeRepository.getAllByMaterialTypeCode("product");

        HvBmMaterialType semiProduct = materialTypeRepository.getAllByMaterialTypeCode("semi_product");

        List<MaterialTypeDTO> typeDTOS = new ArrayList<>();

        MaterialTypeDTO pro = DtoMapper.convert(product, MaterialTypeDTO.class);

        MaterialTypeDTO semi = DtoMapper.convert(semiProduct, MaterialTypeDTO.class);

        typeDTOS.add(pro);
        typeDTOS.add(semi);
        return typeDTOS;
    }


}