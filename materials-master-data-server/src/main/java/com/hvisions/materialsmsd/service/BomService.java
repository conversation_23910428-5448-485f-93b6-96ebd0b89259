package com.hvisions.materialsmsd.service;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.materialsmsd.bom.dto.BaseBomDTO;
import com.hvisions.materialsmsd.bom.dto.BomAllDTO;
import com.hvisions.materialsmsd.bom.dto.BomDTO;
import com.hvisions.materialsmsd.bom.dto.BomQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: BomService</p >
 * <p>Description: bom服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/26</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface BomService extends EntitySaver<BomDTO> {

    /**
     * 添加bom信息
     *
     * @param bomDTO bomDto对象
     * @return bom id
     */
    int createBom(BaseBomDTO bomDTO);


    /**
     * 修改物料信息及扩展属性
     *
     * @param bomDTO 传入DTO对象
     * @return bomId
     */

    int updateBom(BaseBomDTO bomDTO);

    /**
     * 根据materialsId 获取bom信息
     *
     * @param materialsId 物料主数据id
     * @return Bom信息列表
     */
    List<BomDTO> getBomByMaterialsId(Integer materialsId);

    /**
     * 根据materialId 获取bomID
     *
     * @param materialId 物料ID
     * @return bomID
     */
    int getBomIdByMaterialId(Integer materialId);

    /**
     * 根据BOMId 返回所有BOM数据
     *
     * @param id bomId
     * @return 所有BOM信息
     */
    BomAllDTO getBomBomItemSubstituteItemByBomId(Integer id);


    /***
     * 删除bom信息
     * @param id bomId
     */
    void deleteBom(int id);


    /**
     * 修改state状态
     *
     * @param id bomId
     */

    void setStatusTakeEffect(int id);

    /**
     * bom归档
     *
     * @param id bomId
     */
    void discardedBom(int id);

    /**
     * 复制bom信息
     *
     * @param id          bomId
     * @param bomVersions 版本
     * @return 复制的bomId
     */
    int copyBom(int id, String bomVersions);

    /**
     * 根据编码状态名称版本模糊查询
     *
     * @param bomQueryDTO 传入DTO
     * @return 查询信息
     */
    Page<BomDTO> findByBomCodeOrBomNameOrBomVersionsOrBomStatus(BomQueryDTO bomQueryDTO);

    /**
     * 导出所有BOM信息
     *
     * @return bom信息Excel
     * @throws IOException            io异   常
     * @throws IllegalAccessException field访问异常
     */

    ResultVO<ExcelExportDto> exportBomAndBomItemAndSubstituteItem() throws IOException, IllegalAccessException;


    /**
     * 导出选中的BOM信息（根据ID列表导出）
     *
     * @param idList bomId列表
     * @return excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    ResultVO<ExcelExportDto> exportBomAllByIdList(List<Integer> idList) throws IOException, IllegalAccessException;

    /**
     * 导出bom信息（支持超链接）
     *
     * @return excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    ResponseEntity<byte[]> exportBomAll() throws IOException, IllegalAccessException;


    /**
     * 导出所有BOM信息
     *
     * @param idList bomId列表
     * @return excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResponseEntity<byte[]> exportBomByIdList(List<Integer> idList) throws IOException, IllegalAccessException;

    /**
     * 获取导入模板(支持超链接)
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResponseEntity<byte[]> getBomImportTemplate() throws IOException, IllegalAccessException;

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException;


    /**
     * 导入所有物料信息
     *
     * @param file 物料信息文档
     * @return ImportResult列表
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    List<ImportResult> importBom(MultipartFile file) throws IllegalAccessException, ParseException, IOException;

    /***
     * 通过bomCode获取bomVersions
     * @param bomCode bomCode
     * @return 版本列表
     */
    List<String> getBomVersionsByBomCode(String bomCode);


    /**
     * 获取生效bom
     *
     * @return 生效bom信息列表
     */
    List<BomDTO> getTakeEffectBom();

    /**
     * 获取基础bom信息
     *
     * @param bomCode    bom编码
     * @param bomVersion bom版本
     * @return 基础bom信息
     */
    BaseBomDTO getBaseBomDTOByCodeAndVersion(String bomCode, String bomVersion);

}
