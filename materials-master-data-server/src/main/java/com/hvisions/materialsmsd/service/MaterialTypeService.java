package com.hvisions.materialsmsd.service;

import com.hvisions.materialsmsd.materials.dto.MaterialTypeDTO;
import com.hvisions.materialsmsd.materials.dto.MaterialTypeQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: MaterialTypeService</p >
 * <p>Description: 物料类型维护服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-06</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */

public interface MaterialTypeService {


    /**
     * 创建物料类型
     *
     * @param materialTypeDTO 物料类型对象
     * @return 物料类型对象
     */
    MaterialTypeDTO createMaterialType(MaterialTypeDTO materialTypeDTO);


    /**
     * 更新物料类型
     *
     * @param materialTypeDTO 物料类型对象
     * @return 更新类型对象
     */
    MaterialTypeDTO updateMaterialType(MaterialTypeDTO materialTypeDTO);


    /**
     * 根据物料类型ID删除物料类型
     *
     * @param id 物料类型ID
     */
    void deleteTypeById(int id);


    /**
     * 根据物料类型ID查询物料类型
     *
     * @param id ID
     * @return 物料类型信息
     */
    MaterialTypeDTO getMaterialTypeById(int id);


    /**
     * 根据物料类型ID列表查询物料类型信息列表
     *
     * @param idIn 物料类型ID列表
     * @return 物料类型信息列表
     */
    List<MaterialTypeDTO> getMaterialTypeListByIdIn(List<Integer> idIn);

    /**
     * 根据物料编码查询物料类型信息
     *
     * @param Code 物料类型Code
     * @return 物料类型信息≈¬
     */
    MaterialTypeDTO getMaterialTypeByCode(String Code);


    /**
     * 获取所有物料类型
     *
     * @return 物料类型列表
     */
    List<MaterialTypeDTO> findAll();

    /**
     * 获取根结点物料类型
     *
     * @return 物料类型信息列表
     */
    List<MaterialTypeDTO> getRootMaterialType();


    /**
     * 根据物料类型父级别ID查询
     *
     * @param parentId 父级id
     * @return 物料类型信息列表
     */
    List<MaterialTypeDTO> getMaterialTypeByParentId(int parentId);


    /**
     * 分页查询物料类型
     *
     * @param materialTypeQueryDTO 物料类型查询条件
     * @return 物料类型分页信息
     */
    Page<MaterialTypeDTO> getMaterialTypeByQuery(MaterialTypeQueryDTO materialTypeQueryDTO);

    /**
     * 根据子级节点获取上级id
     *
     * @param materialTypeId 物料类型ID
     * @return id列表
     */
    List<Integer> getParentIdBySonId(int materialTypeId);

    /**
     * 获取产品副产品对象
     *
     * @return 物料类型信息列表
     */
    List<MaterialTypeDTO> getProduct();
}