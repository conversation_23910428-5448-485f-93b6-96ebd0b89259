package com.hvisions.materialsmsd.service.imp;

import com.hvisions.common.service.imp.BaseServiceImpl;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterial;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import com.hvisions.materialsmsd.service.BaseMaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>Title: DemoEntityServiceImp</p>
 * <p>Description: 服务实现层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class BaseMaterialServiceImpl extends BaseServiceImpl<HvBmMaterial, Integer> implements BaseMaterialService {

    private final MaterialRepository materialRepository;

    @Autowired
    public BaseMaterialServiceImpl(MaterialRepository materialRepository) {
        //调用父类的构造函数，传入jpa对象，实现普通的增删改查功能
        super(materialRepository);
        this.materialRepository = materialRepository;
    }


    @Override
    public void saveOrUpdate(MaterialDTO materialDTO) {

    }
}
