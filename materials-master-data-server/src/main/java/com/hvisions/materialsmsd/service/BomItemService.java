package com.hvisions.materialsmsd.service;

import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.materialsmsd.bom.dto.BomItemDTO;
import com.hvisions.materialsmsd.bom.dto.BomItemIncreaseDTO;

import java.util.List;

/**
 * <p>Title: BomItemService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface BomItemService extends EntitySaver<BomItemIncreaseDTO> {

    /**
     * 添加bomItem信息
     *
     * @param bomItemDTO 传入DTO对象
     * @return bomItemId
     */
    int createBomItem(BomItemDTO bomItemDTO);

    /**
     * 修改bomItem信息
     *
     * @param bomItemDTO 传入DTO对象
     * @return bomItemId
     */
    int updateBomItem(BomItemDTO bomItemDTO);

    /**
     * 删除bomItem
     *
     * @param id bomItemId
     */
    void deleteBomItem(int id);

    /**
     * 通过bomId查询BomItem信息
     *
     * @param bomId BomId
     * @return bomItem信息列表
     */
    List<BomItemIncreaseDTO> getAllByBomId(int bomId);

    /**
     * 通过ID查询bomItem信息
     *
     * @param id bomItemId
     * @return BomItem信息
     */
    BomItemIncreaseDTO getBomItemById(int id);
}
