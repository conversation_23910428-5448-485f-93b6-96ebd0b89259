package com.hvisions.materialsmsd.service.imp;

import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.bom.dto.BaseSubstituteItemDTO;
import com.hvisions.materialsmsd.bom.dto.SubstituteItemDTO;
import com.hvisions.materialsmsd.entity.HvBmBom;
import com.hvisions.materialsmsd.entity.HvBmBomItem;
import com.hvisions.materialsmsd.entity.HvBmSubstituteItem;
import com.hvisions.materialsmsd.bom.enums.BomExceptionEnum;
import com.hvisions.materialsmsd.repository.BomItemRepository;
import com.hvisions.materialsmsd.repository.BomRepository;
import com.hvisions.materialsmsd.repository.SubstituteItemRepository;
import com.hvisions.materialsmsd.repository.UnitRepository;
import com.hvisions.materialsmsd.service.SubstituteItemService;
import com.hvisions.materialsmsd.utils.Utils;
import com.hvisions.materialsmsd.entity.HvBmMaterial;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.hvisions.materialsmsd.bom.enums.BomExceptionEnum.*;

/**
 * <p>Title: SubstituteItemServiceImp</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class SubstituteItemServiceImpl implements SubstituteItemService {

    private final SubstituteItemRepository substituteItemRepository;

    private final MaterialRepository materialRepository;

    private final UnitRepository unitRepository;
    private final BomItemRepository bomItemRepository;

    private final BomRepository bomRepository;

    @Resource(name = "substitute_item_extend")
    BaseExtendService substituteItemExtendService;

    @Autowired
    public SubstituteItemServiceImpl(SubstituteItemRepository substituteItemRepository, MaterialRepository materialRepository, UnitRepository unitRepository, BomItemRepository bomItemRepository, BomRepository bomRepository) {

        this.substituteItemRepository = substituteItemRepository;
        this.materialRepository = materialRepository;
        this.unitRepository = unitRepository;
        this.bomItemRepository = bomItemRepository;
        this.bomRepository = bomRepository;
    }

    /**
     * 添加 substituteItem信息
     *
     * @param substituteItemDTO 替代物料DTO对象
     * @return 新增的替代物料id
     */
    @Override
    public int createSubstituteItem(BaseSubstituteItemDTO substituteItemDTO) {
        HvBmSubstituteItem hvBmSubstituteItem = createOrUpdateSub(substituteItemDTO);
        return hvBmSubstituteItem.getId();
    }


    private void assessBomStatus(int bomItemId) {
        HvBmBomItem hvBmBomItem = bomItemRepository.getOne(bomItemId);
        HvBmBom hvBmBom = bomRepository.getOne(hvBmBomItem.getBomId());
        if (hvBmBom.getBomStatus() != 0) {
            throw new BaseKnownException(NOW_NEWLY_CANNOT_BE_MODIFIED);
        }
    }

    private void assessParameter(BaseSubstituteItemDTO substituteItemDTO, Integer materialsId) {
        // mid 必须大于0
        if (materialsId <= 0) {
            throw new BaseKnownException(MATERIALS_CANNOT_BE_ZERO);
        }
        //判断mid是否存在
        boolean hvBmMaterials = materialRepository.existsById(materialsId);
        if (!hvBmMaterials) {
            throw new BaseKnownException(MATERIALS_NOT_EXISTS);
        }
        Integer bomItemId = substituteItemDTO.getBomItemId();
        if (bomItemId <= 0) {
            throw new BaseKnownException(BOM_ITEM_CANNOT_BE_ZERO);
        }
        //判断bom是否存在
        boolean bomIdExists = bomItemRepository.existsById(bomItemId);
        if (!bomIdExists) {
            throw new BaseKnownException(BOM_ITEM_NOT_EXISTS);
        }
    }

    /**
     * 修改 substituteItem信息
     *
     * @param substituteItemDTO 传入替代物料DTO对象
     * @return 替代物料id
     */
    @Override
    public int updateSubstituteItem(BaseSubstituteItemDTO substituteItemDTO) {

        HvBmSubstituteItem hvBmSubstituteItem = createOrUpdateSub(substituteItemDTO);
        return hvBmSubstituteItem.getId();
    }

    /**
     * 新增或更新替代料
     *
     * @param substituteItemDTO 替代料对象
     * @return 替代物料实体类
     */
    private HvBmSubstituteItem createOrUpdateSub(BaseSubstituteItemDTO substituteItemDTO) {
        assessBomStatus(substituteItemDTO.getBomItemId());
        Integer materialsId = substituteItemDTO.getMaterialsId();
        //mid大于0
        assessParameter(substituteItemDTO, materialsId);
        BigDecimal one = BigDecimal.valueOf(1);
        BigDecimal zero = BigDecimal.valueOf(0);
        assessBomStatus(substituteItemDTO.getBomItemId());
        //判断替代物料替代比例是否符合规定
        if (substituteItemDTO.getRatio().compareTo(one) == 1) {
            throw new BaseKnownException(BomExceptionEnum.SUBSTITUTE_ITEM_COUNT_OUT_OF_RANGE);
        }
        if (substituteItemDTO.getRatio().compareTo(zero) == -1) {
            throw new BaseKnownException(BomExceptionEnum.SUBSTITUTE_ITEM_COUNT_OUT_OF_RANGE);
        }
        HvBmSubstituteItem hvBmSubstituteItem = substituteItemRepository.save(DtoMapper.convert(substituteItemDTO, HvBmSubstituteItem.class));
        if (substituteItemDTO.getId() != null) {
            Utils.updateExtendInfo(substituteItemDTO.getExtend(), hvBmSubstituteItem.getId(),
                    substituteItemExtendService);
        } else {
            Utils.addExtendInfo(substituteItemDTO.getExtend(), hvBmSubstituteItem.getId(), substituteItemExtendService);
        }
        return hvBmSubstituteItem;
    }

    /**
     * 删除SubstituteItem信息
     *
     * @param id substituteItem主键ID
     */
    @Override
    public void deleteSubstituteItem(int id) {
        HvBmSubstituteItem hvBmSubstituteItem = substituteItemRepository.getOne(id);
        assessBomStatus(hvBmSubstituteItem.getBomItemId());
        substituteItemRepository.deleteById(id);
        substituteItemExtendService.deleteExtendInfo(id);
    }


    /**
     * 通过Id查询substituteItem
     *
     * @param id 替代物料ID
     * @return 替代物料DTO
     */
    @Override
    public SubstituteItemDTO getAllById(int id) {
        SubstituteItemDTO substituteItemDTO = DtoMapper.convert(substituteItemRepository.getOne(id), SubstituteItemDTO.class);
        Map<String, Object> map = substituteItemExtendService.getExtend(id);
        HvBmMaterial material = materialRepository.getOne(substituteItemDTO.getMaterialsId());
        substituteItemDTO.setMaterialCode(material.getMaterialCode());
        substituteItemDTO.setEigenvalue(material.getEigenvalue());
        substituteItemDTO.setMaterialName(material.getMaterialName());
        substituteItemDTO.setMaterialUnitName(unitRepository.getById(material.getUom()).getSymbol());
        substituteItemDTO.setExtend(map);
        return substituteItemDTO;
    }

    /**
     * 通过BomItemId查询
     *
     * @param bomItemId bomItem主键ID
     * @return SubstituteItem数据
     */
    @Override
    public List<SubstituteItemDTO> getAllByBomItemId(int bomItemId) {
        List<HvBmSubstituteItem> hvBmSubstituteItems = substituteItemRepository.getAllByBomItemId(bomItemId);

        List<ExtendInfo> extendInfo = substituteItemExtendService.getAll();

        List<SubstituteItemDTO> dto = DtoMapper.convertList(hvBmSubstituteItems, SubstituteItemDTO.class);

        joinDTOWithExtend(dto, extendInfo);
        List<HvBmMaterial> hvBmMaterials = materialRepository.findAll();

        for (SubstituteItemDTO substituteItemDTO : dto) {
            Optional<HvBmMaterial> hvBmMaterial = hvBmMaterials.stream().filter(t -> t.getId().equals(substituteItemDTO.getMaterialsId())).findFirst();
            hvBmMaterial.ifPresent(hvBmMaterial1 -> substituteItemDTO.setMaterialCode(hvBmMaterial1.getMaterialCode()));
            hvBmMaterial.ifPresent(hvBmMaterial1 -> substituteItemDTO.setEigenvalue(hvBmMaterial1.getEigenvalue()));
            hvBmMaterial.ifPresent(hvBmMaterial1 -> substituteItemDTO.setMaterialName(hvBmMaterial1.getMaterialName()));
        }

        return dto;
    }

    /**
     * dto拼接到
     *
     * @param substituteItemDTOS dto列表
     * @param extendInfos        扩展信息列表
     */
    private void joinDTOWithExtend(Iterable<SubstituteItemDTO> substituteItemDTOS, Iterable<ExtendInfo> extendInfos) {
        for (SubstituteItemDTO substituteItemDTO : substituteItemDTOS) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (substituteItemDTO.getId() == extendInfo.getEntityId()) {
                    substituteItemDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }

    @Override
    public void saveOrUpdate(SubstituteItemDTO substituteItemDTO) {
        //根据传入的material code 判断material是否存在 如果存在插入ID
        HvBmMaterial hvBmMaterial = materialRepository.getByMaterialCodeAndEigenvalue(substituteItemDTO.getMaterialCode(), substituteItemDTO.getEigenvalue());
        if (hvBmMaterial == null) {
            throw new BaseKnownException(MATERIALS_NOT_EXISTS);
        }
        //插入SubstituteItem的maiterialID
        substituteItemDTO.setMaterialsId(hvBmMaterial.getId());
        //获取bomid BomitemMateriID 确定bomItemID
        HvBmBom hvBmBom = bomRepository.findFirstByBomCodeAndBomVersions(substituteItemDTO.getBomCode(), substituteItemDTO.getBomVersions());
        if (hvBmBom == null) {
            throw new BaseKnownException(BOM_NOT_EXISTS);
        }
        if (hvBmBom.getBomStatus() != 0) {
            throw new BaseKnownException(NOW_NEWLY_CANNOT_BE_MODIFIED);
        }
        //根据BomItem的materialCode 获取 material信息
        HvBmMaterial bomItemMaterialCode = materialRepository.findFirstByMaterialCode(substituteItemDTO.getBomItemMaterialCode());
        //判断是否存在
        if (bomItemMaterialCode == null) {
            throw new BaseKnownException(BOM_ITEM_MATERIAL_CODE_EXISTS);
        }
        //根据bomId materialId 查询BomItem
        HvBmBomItem hvBmBomItem = bomItemRepository.getByBomIdAndMaterialsId(hvBmBom.getId(), bomItemMaterialCode.getId());
        //根据传入的BomItem code 判断BomItem 是否存在 如果存在插入ID
        if (hvBmBomItem == null) {
            throw new BaseKnownException(BOM_ITEM_NOT_EXISTS);
        }
        //如果存在插入BomItem信息
        substituteItemDTO.setBomItemId(hvBmBomItem.getId());
        //根据SubstituteItemCode和BomItemID查询是否有相同数据
        HvBmSubstituteItem hvBmSubstituteItemOld = substituteItemRepository.getBySubstituteItemCodeAndBomItemId(substituteItemDTO.getSubstituteItemCode(), hvBmBomItem.getId());
        //存在相同数据则修改 没有则新增
        if (hvBmSubstituteItemOld != null) {
            substituteItemDTO.setId(hvBmSubstituteItemOld.getId());
            updateSubstituteItem(substituteItemDTO);
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(substituteItemDTO.getId());
            extendInfo.setValues(substituteItemDTO.getExtend());
            substituteItemExtendService.updateExtendInfo(extendInfo);
        } else {
            int entityId = createSubstituteItem(substituteItemDTO);
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(entityId);
            extendInfo.setValues(substituteItemDTO.getExtend());
            substituteItemExtendService.addExtendInfo(extendInfo);
        }

    }
}
