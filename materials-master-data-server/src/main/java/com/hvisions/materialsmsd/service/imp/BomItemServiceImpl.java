package com.hvisions.materialsmsd.service.imp;

import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.SysBaseDTO;
import com.hvisions.materialsmsd.bom.dto.BaseBomItemDTO;
import com.hvisions.materialsmsd.bom.dto.BomItemDTO;
import com.hvisions.materialsmsd.bom.dto.BomItemIncreaseDTO;
import com.hvisions.materialsmsd.bom.dto.SubstituteItemDTO;
import com.hvisions.materialsmsd.entity.HvBmBom;
import com.hvisions.materialsmsd.entity.HvBmBomItem;
import com.hvisions.materialsmsd.entity.HvBmUnit;
import com.hvisions.materialsmsd.repository.BomItemRepository;
import com.hvisions.materialsmsd.repository.BomRepository;
import com.hvisions.materialsmsd.repository.UnitRepository;
import com.hvisions.materialsmsd.service.BomItemService;
import com.hvisions.materialsmsd.service.SubstituteItemService;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterial;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import com.hvisions.materialsmsd.service.MaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.hvisions.materialsmsd.bom.enums.BomExceptionEnum.*;

/**
 * <p>Title: BomItemServiceImp</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class BomItemServiceImpl implements BomItemService {


    private final BomItemRepository bomItemRepository;
    private final MaterialRepository materialRepository;
    private final BomRepository bomRepository;
    private final UnitRepository unitRepository;
    private final SubstituteItemService substituteItemService;
    private final int NEWLY = 0;
    private final MaterialService materialService;
    @Resource(name = "bom_item_extend")
    BaseExtendService bomItemExtendService;

    @Autowired
    public BomItemServiceImpl(BomItemRepository bomItemRepository,
                              MaterialRepository materialRepository,
                              BomRepository bomRepository, UnitRepository unitRepository, SubstituteItemService substituteItemService, MaterialService materialService) {

        this.bomItemRepository = bomItemRepository;
        this.materialRepository = materialRepository;
        this.bomRepository = bomRepository;
        this.unitRepository = unitRepository;
        this.substituteItemService = substituteItemService;
        this.materialService = materialService;
    }

    @Override
    public void saveOrUpdate(BomItemIncreaseDTO bomItemIncreaseDTO) {
        HvBmBomItem hvBmBomItem = DtoMapper.convert(bomItemIncreaseDTO, HvBmBomItem.class);
        //通过传入BOM 编码版本查询BOM
        HvBmBom hvBmBom = bomRepository.findFirstByBomCodeAndBomVersions(bomItemIncreaseDTO.getBomCode(), bomItemIncreaseDTO.getBomVersions());
        //判断为空 报异常
        if (hvBmBom == null) {
            throw new BaseKnownException(BOM_NOT_EXISTS);
        }
        //判断bom是否为新增 只有新增的bom能增加bomItem
        if (hvBmBom.getBomStatus() != NEWLY) {
            throw new BaseKnownException(NOW_NEWLY_CANNOT_BE_MODIFIED);
        }
        //通过传入material编码查询materialId
        HvBmMaterial hvBmMaterial = materialRepository.getByMaterialCodeAndEigenvalue(bomItemIncreaseDTO.getMaterialCode(), bomItemIncreaseDTO.getEigenvalue());
        if (hvBmMaterial == null) {
            throw new BaseKnownException(MATERIALS_NOT_EXISTS);
        }
        //获取之前数据ID
        HvBmBomItem hvBmBomItemOld = bomItemRepository.getByBomIdAndMaterialsId(hvBmBom.getId(), hvBmMaterial.getId());

        hvBmBomItem.setMaterialsId(hvBmMaterial.getId());
        hvBmBomItem.setBomId(hvBmBom.getId());
        if (hvBmBomItemOld != null) {
            hvBmBomItem.setId(hvBmBomItemOld.getId());
            updateBomItem(DtoMapper.convert(hvBmBomItem, BomItemDTO.class));
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(hvBmBomItem.getId());
            extendInfo.setValues(bomItemIncreaseDTO.getExtend());
            bomItemExtendService.updateExtendInfo(extendInfo);
        } else {
            int entityId = createBomItem(DtoMapper.convert(hvBmBomItem, BomItemDTO.class));
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(entityId);
            extendInfo.setValues(bomItemIncreaseDTO.getExtend());
            bomItemExtendService.addExtendInfo(extendInfo);
        }
    }

    /**
     * 添加bomItem信息
     *
     * @param bomItemDTO dto
     * @return bomItem id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createBomItem(BomItemDTO bomItemDTO) {
        assessBomStatus(bomItemDTO.getBomId());
        Integer materialsId = bomItemDTO.getMaterialsId();
        BigDecimal one = BigDecimal.valueOf(1);
        BigDecimal zero = BigDecimal.valueOf(0);

        assessParameter(bomItemDTO, materialsId);
        if (bomItemDTO.getBomItemCount() != null) {
            if (bomItemDTO.getBomItemCount().compareTo(zero) == -1) {
                throw new BaseKnownException(BOM_ITEM_COUNT_OUT_OF_RANGE);
            }
        }
        HvBmBomItem hvBmBomitem = bomItemRepository.save(DtoMapper.convert(bomItemDTO, HvBmBomItem.class));
        if (bomItemDTO.getExtend() != null) {
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(hvBmBomitem.getId());
            extendInfo.setValues(bomItemDTO.getExtend());
            bomItemExtendService.addExtendInfo(extendInfo);
        }
        return hvBmBomitem.getId();
    }

    /**
     * 判断添加bomItem传参是否为空
     *
     * @param bomItemDTO  bomItem对象
     * @param materialsId 物料ID
     */
    private void assessParameter(BomItemDTO bomItemDTO, Integer materialsId) {
        //是否传入materialId
        if (materialsId == null) {
            throw new BaseKnownException(MATERIALS_NOT_NULL);
        }
        // mid 必须大于0
        if (materialsId <= 0) {
            throw new BaseKnownException(MATERIALS_CANNOT_BE_ZERO);
        }
        //判断mid是否存在
        boolean hvBmMaterials = materialRepository.existsById(materialsId);
        if (!hvBmMaterials) {
            throw new BaseKnownException(MATERIALS_NOT_EXISTS);
        }
        Integer bomId = bomItemDTO.getBomId();
        if (bomId == null) {
            throw new BaseKnownException(BOM_ID_NOT_NULL);
        }
        if (bomId <= 0) {
            throw new BaseKnownException(BOM_CANNOT_BE_ZERO);
        }
        //判断bom是否存在
        boolean bomIdExists = bomRepository.existsById(bomId);
        if (!bomIdExists) {
            throw new BaseKnownException(BOM_NOT_EXISTS);
        }
    }

    /**
     * 判断bom状态
     *
     * @param bomId bomId
     * @return true or false
     */
    private boolean assessBomStatus(int bomId) {
        HvBmBom hvBmBom = bomRepository.getOne(bomId);
        Integer bomStatus = hvBmBom.getBomStatus();
        if (bomStatus != NEWLY) {
            throw new BaseKnownException(NOW_NEWLY_CANNOT_BE_MODIFIED);
        }
        return true;
    }

    /**
     * 添加bomItem信息
     *
     * @param bomItemDTO dto
     * @return bomItem id
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateBomItem(BomItemDTO bomItemDTO) {
        assessBomStatus(bomItemDTO.getBomId());
        Integer materialsId = bomItemDTO.getMaterialsId();
        BigDecimal one = BigDecimal.valueOf(1);
        BigDecimal zero = BigDecimal.valueOf(0);
        assessParameter(bomItemDTO, materialsId);

        //判断bomItem数量是否符合规定
        if (bomItemDTO.getBomItemCount().compareTo(zero) == -1) {
            throw new BaseKnownException(BOM_ITEM_COUNT_OUT_OF_RANGE);
        }

        HvBmBomItem hvBmBomitem = bomItemRepository.save(DtoMapper.convert(bomItemDTO, HvBmBomItem.class));
        if (bomItemDTO.getExtend() != null) {
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(hvBmBomitem.getId());
            extendInfo.setValues(bomItemDTO.getExtend());
            bomItemExtendService.updateExtendInfo(extendInfo);
        }
        return hvBmBomitem.getId();
    }

    /**
     * 通过bomId查询bomItem
     *
     * @param bomId bom主键
     * @return bomItem信息
     */
    @Override
    public List<BomItemIncreaseDTO> getAllByBomId(int bomId) {
        List<HvBmBomItem> hvBmBomItems = bomItemRepository.findAllByBomId(bomId);
        List<ExtendInfo> extendInfo = bomItemExtendService.getAll();
        List<BomItemIncreaseDTO> dto = DtoMapper.convertList(hvBmBomItems, BomItemIncreaseDTO.class);
        joinIncreaseDTOWithExtend(dto, extendInfo);
        List<Integer> materialIds = dto.stream().map(BaseBomItemDTO::getMaterialsId).collect(Collectors.toList());
        List<Integer> bomIds = dto.stream().map(BaseBomItemDTO::getBomId).collect(Collectors.toList());
        List<MaterialDTO> hvBmMaterials = materialService.getMaterialsByIdList(materialIds);
        List<HvBmBom> hvBmBoms = bomRepository.getAllByIdIn(bomIds);
        for (BomItemIncreaseDTO bomItemIncreaseDTO : dto) {
            Optional<MaterialDTO> hvBmMaterial = hvBmMaterials.stream().filter(t -> t.getId().equals(bomItemIncreaseDTO.getMaterialsId())).findFirst();
            hvBmMaterial.ifPresent(hvBmMaterial1 -> bomItemIncreaseDTO.setMaterialCode(hvBmMaterial1.getMaterialCode()));
            hvBmMaterial.ifPresent(hvBmMaterial1 -> bomItemIncreaseDTO.setEigenvalue(hvBmMaterial1.getEigenvalue()));
            hvBmMaterial.ifPresent(hvBmMaterial1 -> bomItemIncreaseDTO.setMaterialName(hvBmMaterial1.getMaterialName()));
            if (hvBmMaterial.isPresent()) {
                HvBmUnit byId = unitRepository.getById(hvBmMaterial.get().getUom());
                bomItemIncreaseDTO.setMaterialUnitName(byId.getDescription());
            }
            Optional<HvBmBom> hvBmBom = hvBmBoms.stream().filter(t -> t.getId().equals(bomItemIncreaseDTO.getBomId())).findAny();
            hvBmBom.ifPresent(hBomM -> bomItemIncreaseDTO.setBomCode(hBomM.getBomCode()));
            hvBmBom.ifPresent(hvBmBom1 -> bomItemIncreaseDTO.setBomVersions(hvBmBom1.getBomVersions()));
            bomItemIncreaseDTO.setMaterialDTO(hvBmMaterial.get());
        }
        return dto;
    }


    /**
     * 通过id查询bomItem
     */
    @Override
    public BomItemIncreaseDTO getBomItemById(int id) {
        //根据ID查询
        HvBmBomItem hvBmBomItems = bomItemRepository.getById(id);
        //获取扩展字段信息
        Map<String, Object> extend = bomItemExtendService.getExtend(id);
        BomItemIncreaseDTO dto = DtoMapper.convert(hvBmBomItems, BomItemIncreaseDTO.class);
        dto.setExtend(extend);
        HvBmMaterial material = materialRepository.getOne(dto.getMaterialsId());
        dto.setMaterialCode(material.getMaterialCode());
        dto.setEigenvalue(material.getEigenvalue());
        dto.setMaterialName(material.getMaterialName());
        dto.setMaterialUnitName(unitRepository.getById(material.getUom()).getSymbol());
        dto.setMaterialDTO(DtoMapper.convert(material, MaterialDTO.class));
        return dto;
    }

    /**
     * 删除bomItem信息
     *
     * @param id bomItemId
     */
    @Override
    public void deleteBomItem(int id) {
        HvBmBomItem hvBmBomItem = bomItemRepository.getById(id);
        assessBomStatus(hvBmBomItem.getBomId());
        List<SubstituteItemDTO> allByBomItemId =
                substituteItemService.getAllByBomItemId(id);
        List<Integer> subId = allByBomItemId.stream().map(SysBaseDTO::getId).collect(Collectors.toList());
        for (Integer sId : subId) {
            substituteItemService.deleteSubstituteItem(sId);
        }
        bomItemRepository.deleteById(id);
    }

    /**
     * dto拼接到
     *
     * @param bomItemDTOS dto列表
     * @param extendInfos 扩展信息列表
     */
    private void joinDTOWithExtend(Iterable<BomItemDTO> bomItemDTOS, Iterable<ExtendInfo> extendInfos) {
        for (BomItemDTO bomItemDTO : bomItemDTOS) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (bomItemDTO.getId() == extendInfo.getEntityId()) {
                    bomItemDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }

    /**
     * dto拼接到
     *
     * @param bomItemDTOS dto列表
     * @param extendInfos 扩展信息列表
     */
    private void joinIncreaseDTOWithExtend(List<BomItemIncreaseDTO> bomItemDTOS, Iterable<ExtendInfo> extendInfos) {
        for (BomItemIncreaseDTO bomItemIncreaseDTO : bomItemDTOS) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (bomItemIncreaseDTO.getId() == extendInfo.getEntityId()) {
                    bomItemIncreaseDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }


}
