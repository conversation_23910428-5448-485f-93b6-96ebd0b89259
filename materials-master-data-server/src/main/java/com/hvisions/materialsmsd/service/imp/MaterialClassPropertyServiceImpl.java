package com.hvisions.materialsmsd.service.imp;

import cn.hutool.core.lang.Assert;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.materials.classdto.MaterialClassPropertyDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterial;
import com.hvisions.materialsmsd.entity.HvBmMaterialClass;
import com.hvisions.materialsmsd.entity.HvBmMaterialClassProperty;
import com.hvisions.materialsmsd.entity.HvBmMaterialProperty;
import com.hvisions.materialsmsd.materials.enums.MaterialPropertyDataType;
import com.hvisions.materialsmsd.repository.MaterialClassPropertyRepository;
import com.hvisions.materialsmsd.repository.MaterialClassRepository;
import com.hvisions.materialsmsd.repository.MaterialPropertyRepository;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import com.hvisions.materialsmsd.service.MaterialClassPropertyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: MaterialClassPropertyServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class MaterialClassPropertyServiceImpl implements MaterialClassPropertyService {


    private final MaterialClassPropertyRepository materialClassPropertyRepository;
    private final MaterialRepository materialRepository;
    private final MaterialPropertyRepository materialPropertyRepository;
    private final MaterialClassRepository materialClassRepository;

    @Autowired
    public MaterialClassPropertyServiceImpl(MaterialClassPropertyRepository materialClassPropertyRepository, MaterialRepository materialRepository, MaterialPropertyRepository materialPropertyRepository, MaterialClassRepository materialClassRepository) {
        this.materialClassPropertyRepository = materialClassPropertyRepository;
        this.materialRepository = materialRepository;
        this.materialPropertyRepository = materialPropertyRepository;
        this.materialClassRepository = materialClassRepository;
    }

    /**
     * 创建属性
     *
     * @param propertyDTO 属性
     * @return 主键
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(MaterialClassPropertyDTO propertyDTO) {
        Assert.isNull(propertyDTO.getId(), "新增不能传递id");
        Assert.notNull(propertyDTO.getDateType(), "数据类型不能为空");
        Assert.notNull(propertyDTO.getClassId(), "属性类型信息不能为空");
        Optional<HvBmMaterialClass> clazz = materialClassRepository.findById(propertyDTO.getClassId());
        Assert.isTrue(clazz.isPresent(), "属性类型不存在");
        Assert.isTrue(clazz.get().getProperties().stream().noneMatch(t -> t.getCode().equals(propertyDTO.getCode())),
                "属性已经存在");
        //如果类型存在，那么这个类型的属性添加这个属性
        HvBmMaterialClass eclazz = clazz.get();
        HvBmMaterialClassProperty property = DtoMapper.convert(propertyDTO, HvBmMaterialClassProperty.class);
        setValue(property);
        property.setClazz(eclazz);
        //所有配置了类型的物料，都需要添加这个属性
        for (HvBmMaterial material : eclazz.getMaterials()) {
            HvBmMaterialProperty materialProperty = DtoMapper.convert(property, HvBmMaterialProperty.class);
            materialProperty.setId(null);
            materialProperty.setClassCode(eclazz.getCode());
            materialProperty.setClassId(eclazz.getId());
            materialProperty.setMaterial(material);
            materialPropertyRepository.save(materialProperty);
        }
        materialClassPropertyRepository.save(property);
        return property.getId();
    }

    /**
     * 更新属性
     *
     * @param propertyDTO 属性
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MaterialClassPropertyDTO propertyDTO) {
        Assert.notNull(propertyDTO.getId(), "新增不能传递id");
        Optional<HvBmMaterialClassProperty> pro = materialClassPropertyRepository.findById(propertyDTO.getId());
        if (pro.isPresent()) {
            HvBmMaterialClassProperty t = pro.get();
            t.setName(propertyDTO.getName());
            t.setValue(propertyDTO.getValue());
            setValue(t);
            materialClassPropertyRepository.save(t);
            //如果是常量类型，需要更新所有配置了这个属性的物料属性
            if (t.getIsConst()) {
                List<HvBmMaterialProperty> propertyList
                        = materialPropertyRepository.findAllByCodeAndClassCode(t.getCode(), t.getCode());
                for (HvBmMaterialProperty equipmentProperty : propertyList) {
                    equipmentProperty.setValue(t.getValue());
                    equipmentProperty.setFloatValue(t.getFloatValue());
                    equipmentProperty.setStringValue(t.getStringValue());
                    equipmentProperty.setLongValue(t.getLongValue());
                }
                materialPropertyRepository.saveAll(propertyList);
            }
        }
    }


    /**
     * 设置属性值
     *
     * @param propertyEntity 实体对象
     */
    private void setValue(HvBmMaterialClassProperty propertyEntity) {
        switch (MaterialPropertyDataType.getByCode(propertyEntity.getDateType())) {
            case STRING:
                propertyEntity.setStringValue(propertyEntity.getValue());
                break;
            case LONG:
                try {
                    propertyEntity.setLongValue(Long.parseLong(propertyEntity.getValue()));
                } catch (Exception ex) {
                    propertyEntity.setLongValue(0L);
                }
                break;
            case FLOAT:
                try {
                    propertyEntity.setFloatValue(Float.parseFloat(propertyEntity.getValue()));
                } catch (Exception ex) {
                    propertyEntity.setFloatValue(0F);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 删除属性
     *
     * @param id 主键
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {

        //删除属性。并且一并删除所有配置了这个属性的物料属性
        Optional<HvBmMaterialClassProperty> property = materialClassPropertyRepository.findById(id);
        if (property.isPresent()) {
            Optional<HvBmMaterialClass> byId = materialClassRepository.findById(property.get().getClazz().getId());
            byId.get().getProperties().remove(property.get());

            List<HvBmMaterialProperty> allByCodeAndClassCode =
                    materialPropertyRepository.findAllByCodeAndClassCode(property.get().getCode(),
                            property.get().getClazz().getCode());

            for (HvBmMaterialProperty hvBmMaterialProperty : allByCodeAndClassCode) {
                hvBmMaterialProperty.getMaterial().getProperties().remove(hvBmMaterialProperty);
            }
            materialPropertyRepository.deleteAll(allByCodeAndClassCode);
            materialClassPropertyRepository.deleteById(property.get().getId());
        }
    }

    /**
     * 获取属性
     *
     * @param id 物料类型id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MaterialClassPropertyDTO> findByMaterialClassId(Integer id) {
        Optional<HvBmMaterialClass> clazz = materialClassRepository.findById(id);
        List<MaterialClassPropertyDTO> result = new ArrayList<>();
        clazz.ifPresent(t -> {
            result.addAll(DtoMapper.convertList(t.getProperties(), MaterialClassPropertyDTO.class));
        });
        return result;
    }
}