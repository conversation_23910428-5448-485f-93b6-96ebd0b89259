package com.hvisions.materialsmsd.service;

import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.materialsmsd.bom.dto.BaseSubstituteItemDTO;
import com.hvisions.materialsmsd.bom.dto.SubstituteItemDTO;

import java.util.List;

/**
 * <p>Title: SubstituteItemService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/29</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface SubstituteItemService extends EntitySaver<SubstituteItemDTO> {

    /**
     * 添加SubstituteItem信息
     *
     * @param substituteItemDTO 传入dTO对象
     * @return SubstituteItemId
     */
    int createSubstituteItem(BaseSubstituteItemDTO substituteItemDTO);

    /**
     * 更新SubstituteItem信息
     *
     * @param substituteItemDTO 传入dTO对象
     * @return id
     */
    int updateSubstituteItem(BaseSubstituteItemDTO substituteItemDTO);

    /**
     * 删除SubstituteItem信息
     *
     * @param id SubstituteItemID
     */
    void deleteSubstituteItem(int id);

    /**
     * 通过ID查询SubstituteItem
     *
     * @param id SubstituteItemID
     * @return SubstituteItem数据
     */
    SubstituteItemDTO getAllById(int id);

    /**
     * 通过BomItemId查询
     *
     * @param bomItemId bomItemID
     * @return SubstituteItem数据
     */

    List<SubstituteItemDTO> getAllByBomItemId(int bomItemId);

}
