package com.hvisions.materialsmsd.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.service.imp.BaseServiceImpl;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.bom.dto.UnitDTO;
import com.hvisions.materialsmsd.bom.enums.BomExceptionEnum;
import com.hvisions.materialsmsd.entity.HvBmUnit;
import com.hvisions.materialsmsd.materials.enums.MaterialExceptionEnum;
import com.hvisions.materialsmsd.query.UnitQuery;
import com.hvisions.materialsmsd.repository.BomRepository;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import com.hvisions.materialsmsd.repository.UnitRepository;
import com.hvisions.materialsmsd.service.UnitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>Title: UnitServiceImp</p >
 * <p>Description: 计量单位服务实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/3</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
@Slf4j
public class UnitServiceImpl extends BaseServiceImpl<HvBmUnit, Integer> implements UnitService {

    private final UnitRepository unitRepository;
    private final MaterialRepository materialRepository;
    private final BomRepository bomRepository;

    @Autowired
    public UnitServiceImpl(UnitRepository unitRepository, MaterialRepository materialRepository, BomRepository bomRepository) {
        super(unitRepository);
        this.unitRepository = unitRepository;
        this.materialRepository = materialRepository;
        this.bomRepository = bomRepository;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<HvBmUnit> findAll() {
        return unitRepository.findAll();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int createUnit(UnitDTO unitDTO) {
        if (unitDTO.getSymbol() == null) {
            throw new BaseKnownException(BomExceptionEnum.UNIT_SYMBOL_NOT_NULL);
        }
        return assess(unitDTO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteUnit(Integer id) {
        boolean exists = materialRepository.existsByUom(id);
        if (exists) {
            throw new BaseKnownException(MaterialExceptionEnum.UOM_IS_USE);
        }
        boolean existsByBom = bomRepository.existsByUnitId(id);
        if (existsByBom) {
            throw new BaseKnownException(MaterialExceptionEnum.UOM_IS_USE);
        }
        delete(id);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int updateUnit(UnitDTO unitDTO) {
        if (unitDTO.getSymbol() == null) {
            throw new BaseKnownException(BomExceptionEnum.UNIT_SYMBOL_NOT_NULL);
        }
        if (unitDTO.getDescription() == null) {
            throw new BaseKnownException(BomExceptionEnum.DESCRIPTION_NOT_NULL);
        }
        HvBmUnit hvBmUnit = unitRepository.save(DtoMapper.convert(unitDTO, HvBmUnit.class));
        return hvBmUnit.getId();
    }

    private int assess(UnitDTO unitDTO) {
        boolean symbolExist = unitRepository.existsBySymbol(unitDTO.getSymbol());
        if (symbolExist) {
            throw new BaseKnownException(BomExceptionEnum.SYMBOL_EXISTS);
        }
        boolean descExists = unitRepository.existsByDescription(unitDTO.getDescription());
        if (descExists) {
            throw new BaseKnownException(BomExceptionEnum.DESC_EXISTS);
        }

        if (unitDTO.getDescription() == null) {
            throw new BaseKnownException(BomExceptionEnum.DESCRIPTION_NOT_NULL);
        }
        HvBmUnit hvBmUnit = unitRepository.save(DtoMapper.convert(unitDTO, HvBmUnit.class));
        return hvBmUnit.getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UnitDTO getUnitById(Integer id) {
        return DtoMapper.convert(unitRepository.getById(id), UnitDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<UnitDTO> findUnitBySymbolOrDescription(UnitQuery unitDTO) {
        Page<HvBmUnit> hvBmUnits;
        //转换DTO
        HvBmUnit hvBmUnit = DtoMapper.convert(unitDTO, HvBmUnit.class);
        //查询
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
            .withMatcher("symbol", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
            .withMatcher("description", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        Example<HvBmUnit> example = Example.of(hvBmUnit, exampleMatcher);
        //根据materialQueryDTO传入的参数 查询所有 进行匹配
        hvBmUnits = unitRepository.findAll(example, unitDTO.getRequest());
        return DtoMapper.convertPage(hvBmUnits, UnitDTO.class);

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void initialUnits() {
        List<HvBmUnit> units = unitRepository.findAll();
        //如果有数据就直接返回
        if(units.size()>0){
            log.info("已经有单位数据，不执行初始化操作");
            return;
        }
        Map<String, String> map = new LinkedHashMap<>();
        map.put("box","箱");
        map.put("pcs", "件");
        map.put("t", "吨");
        map.put("kg", "千克");
        map.put("g", "克");
        map.put("L", "升");
        map.put("mL", "毫升");
        map.put("m³", "立方米");
        map.put("cm³", "立方厘米");
        map.put("㎡", "平方米");
        map.put("c㎡", "平方厘米");
        map.put("m", "米");
        map.put("dm", "分米");
        map.put("cm", "厘米");
        Set<String> keys = map.keySet();
        List<HvBmUnit> list = new ArrayList<>();
        for (String key : keys) {
            HvBmUnit unit = new HvBmUnit();
            unit.setSymbol(key);
            unit.setDescription(map.get(key));
            list.add(unit);
        }
        unitRepository.saveAll(list);
    }


}
