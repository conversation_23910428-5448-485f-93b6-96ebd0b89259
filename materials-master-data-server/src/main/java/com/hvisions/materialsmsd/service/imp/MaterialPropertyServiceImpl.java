package com.hvisions.materialsmsd.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.materials.classdto.AddMaterialPropertyDTO;
import com.hvisions.materialsmsd.materials.classdto.MaterialPropertyDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterial;
import com.hvisions.materialsmsd.entity.HvBmMaterialProperty;
import com.hvisions.materialsmsd.materials.enums.MaterialPropertyDataType;
import com.hvisions.materialsmsd.repository.MaterialPropertyRepository;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import com.hvisions.materialsmsd.service.MaterialPropertyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: MaterialPropertyServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class MaterialPropertyServiceImpl implements MaterialPropertyService {

    private final MaterialPropertyRepository materialPropertyRepository;

    private final MaterialRepository materialRepository;

    @Autowired
    public MaterialPropertyServiceImpl(MaterialPropertyRepository materialPropertyRepository, MaterialRepository materialRepository) {
        this.materialPropertyRepository = materialPropertyRepository;
        this.materialRepository = materialRepository;
    }

    /**
     * 修改物料属性
     *
     * @param propertyDTOs 属性
     */
    @Override
    public void update(List<MaterialPropertyDTO> propertyDTOs) {
        for (MaterialPropertyDTO propertyDTO : propertyDTOs) {
            Assert.notNull(propertyDTO.getId(), "修改时需要传递主键");
            Optional<HvBmMaterialProperty> property = materialPropertyRepository.findById(propertyDTO.getId());
            property.ifPresent(t -> {
                if (!t.getIsConst()) {
                    t.setValue(propertyDTO.getValue());
                    setValue(t);
                    materialPropertyRepository.save(t);
                }
            });
        }
    }

    /**
     * 设置属性值
     *
     * @param propertyEntity 实体对象
     */
    private void setValue(HvBmMaterialProperty propertyEntity) {
        switch (MaterialPropertyDataType.getByCode(propertyEntity.getDateType())) {
            case STRING:
                propertyEntity.setStringValue(propertyEntity.getValue());
                break;
            case LONG:
                try {
                    propertyEntity.setLongValue(Long.parseLong(propertyEntity.getValue()));
                } catch (Exception ex) {
                    propertyEntity.setLongValue(0L);
                }
                break;
            case FLOAT:
                try {
                    propertyEntity.setFloatValue(Float.parseFloat(propertyEntity.getValue()));
                } catch (Exception ex) {
                    propertyEntity.setFloatValue(0F);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 获取物料属性
     *
     * @param id 物料id
     * @return 物料属性列表
     */
    @Override
    public List<MaterialPropertyDTO> findByMaterialId(Integer id) {
        Optional<HvBmMaterial> material = materialRepository.findById(id);
        if (material.isPresent()) {
            List<MaterialPropertyDTO> materialPropertyDTOS = DtoMapper.convertList(material.get().getProperties(), MaterialPropertyDTO.class);
            materialPropertyDTOS = materialPropertyDTOS.stream().sorted(
                    Comparator.comparing(MaterialPropertyDTO::getClassId,
                            Comparator.nullsFirst(Integer::compareTo))).collect(Collectors.toList());
            return materialPropertyDTOS;
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 添加物料属性
     *
     * @param propertyDTO 物料属性
     */
    @Override
    public void addPropertyToMaterials(AddMaterialPropertyDTO propertyDTO) {
        Optional<HvBmMaterial> material = materialRepository.findById(propertyDTO.getMaterialId());
        Assert.isTrue(material.isPresent(), "物料信息不存在");
        HvBmMaterialProperty convert = DtoMapper.convert(propertyDTO, HvBmMaterialProperty.class);
        convert.setMaterial(material.get());
        setValue(convert);
        materialPropertyRepository.save(convert);
    }

    /**
     * 根据Id删除物料属性
     *
     * @param id 物料属性ID
     */
    @Override
    public void deletePropertyById(int id) {
        Optional<HvBmMaterialProperty> property = materialPropertyRepository.findById(id);
        Assert.isTrue(property.isPresent(), "物料属性不存在");
        Assert.isTrue(property.get().getClassCode() == null, "物料属性类型下属性无法删除");
        Optional<HvBmMaterial> material = materialRepository.findById(property.get().getMaterial().getId());
        Assert.isTrue(material.isPresent(), "物料信息不存在");
        material.get().getProperties().remove(property.get());
        materialPropertyRepository.deleteById(id);
    }


}