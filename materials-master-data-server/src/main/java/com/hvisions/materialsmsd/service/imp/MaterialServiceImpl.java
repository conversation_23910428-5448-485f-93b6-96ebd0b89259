package com.hvisions.materialsmsd.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.materialsmsd.SysBaseDTO;
import com.hvisions.materialsmsd.bom.dto.BomMaterialDTO;
import com.hvisions.materialsmsd.bom.dto.UnitDTO;
import com.hvisions.materialsmsd.consts.MaterialConst;
import com.hvisions.materialsmsd.dao.MaterialMapper;
import com.hvisions.materialsmsd.entity.*;
import com.hvisions.materialsmsd.materials.dto.*;
import com.hvisions.materialsmsd.materials.enums.MaterialExceptionEnum;
import com.hvisions.materialsmsd.materials.enums.MaterialParseTypeEnum;
import com.hvisions.materialsmsd.repository.*;
import com.hvisions.materialsmsd.service.BaseMaterialService;
import com.hvisions.materialsmsd.service.MaterialService;
import com.hvisions.materialsmsd.service.MaterialTypeService;
import com.hvisions.materialsmsd.service.UnitService;
import com.hvisions.materialsmsd.utils.RegularUtils;
import com.hvisions.materialsmsd.utils.Utils;
import com.netflix.ribbon.proxy.annotation.TemplateName;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.hvisions.materialsmsd.materials.enums.MaterialExceptionEnum.*;

/**
 * <p>Title: MaterialsMasterDataExtendServiceImp</p >
 * <p>Description: 扩展服务</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
@Slf4j
public class MaterialServiceImpl implements MaterialService {


    private final MaterialRepository materialRepository;

    private final MaterialTypeRepository materialTypeRepository;

    private final UnitRepository unitRepository;

    private final MaterialGroupRepository materialGroupRepository;

    private final BomMaterialRepository bomMaterialRepository;

    private final BomItemRepository bomItemRepository;

    private final SubstituteItemRepository substituteItemRepository;

    private final BomRepository bomRepository;

    private final UnitService unitService;

    private final MaterialTypeService materialTypeService;

    private final ParseSettingRepository parseSettingRepository;

    private final MaterialMapper materialMapper;

    private final BaseMaterialService baseMaterialService;

    private static final int TWO = 2;

    @Resource(name = "material_extend")
    BaseExtendService materialExtendService;


    @Autowired
    public MaterialServiceImpl(MaterialRepository materialRepository,
                               MaterialTypeRepository materialTypeRepository,
                               UnitRepository unitRepository,
                               MaterialGroupRepository materialGroupRepository,
                               BomMaterialRepository bomMaterialRepository,
                               BomItemRepository bomItemRepository,
                               SubstituteItemRepository substituteItemRepository,
                               BomRepository bomRepository,
                               MaterialTypeService materialTypeService,
                               ParseSettingRepository parseSettingRepository,
                               MaterialMapper materialMapper,
                               UnitService unitService,
                               BaseMaterialService baseMaterialService
    ) {
        this.materialTypeRepository = materialTypeRepository;
        this.unitRepository = unitRepository;
        this.materialGroupRepository = materialGroupRepository;
        this.bomMaterialRepository = bomMaterialRepository;
        this.bomItemRepository = bomItemRepository;
        this.substituteItemRepository = substituteItemRepository;
        this.materialRepository = materialRepository;
        this.bomRepository = bomRepository;
        this.materialTypeService = materialTypeService;
        this.parseSettingRepository = parseSettingRepository;
        this.materialMapper = materialMapper;
        this.unitService = unitService;
        this.baseMaterialService = baseMaterialService;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void saveOrUpdate(MaterialDTO materialDTO) {
        //转换成对应的实体类
        HvBmMaterial hvBmMaterial = DtoMapper.convert(materialDTO, HvBmMaterial.class);
        //获取分组id
        if (materialDTO.getMaterialGroupCode() != null && StringUtils.isNotBlank(materialDTO.getMaterialGroupCode())) {
            HvBmMaterialGroup group = materialGroupRepository.getByGroupCode(materialDTO.getMaterialGroupCode());
            if (group != null) {
                hvBmMaterial.setMaterialGroup(group.getId());
            }
        }
        //获取单位id
        if (materialDTO.getUomName() != null && StringUtils.isNotBlank(materialDTO.getUomName())) {
            HvBmUnit unit = unitRepository.getBySymbol(materialDTO.getUomName());
            if (unit == null) {
                throw new BaseKnownException(UOM_NAME_ERROR);
            }
            hvBmMaterial.setUom(unit.getId());
        } else {
            throw new BaseKnownException(UOM_NAME_ERROR);
        }
        if (materialDTO.getMaterialTypeCode() != null && StringUtils.isNotBlank(materialDTO.getMaterialTypeCode())) {
            HvBmMaterialType materialType = materialTypeRepository.getAllByMaterialTypeCode(materialDTO.getMaterialTypeCode());
            if (materialType == null) {
                throw new BaseKnownException(MATERIAL_TYPE_NAME_ERROR);
            }
            hvBmMaterial.setMaterialType(materialType.getId());
        }
        //根据编码特征值查询是否已经存在兑应的实体
        HvBmMaterial hvBmMaterialOld = materialRepository.getByMaterialCodeAndEigenvalue(hvBmMaterial.getMaterialCode(), hvBmMaterial.getEigenvalue());
        if (hvBmMaterial.getMaterialDesc() == null) {
            hvBmMaterial.setMaterialDesc("");
        }
        //设置rootParentId，用于查询操作
        List<Integer> parentIds = materialTypeService.getParentIdBySonId(hvBmMaterial.getMaterialType());
        if (parentIds.size() > 0) {
            hvBmMaterial.setRootMaterialType(parentIds.get(0));
        }
        //如果存在，更新，不存在新增
        if (hvBmMaterialOld != null) {
            hvBmMaterial.setId(hvBmMaterialOld.getId());
            hvBmMaterial.setPhotoId(hvBmMaterialOld.getPhotoId());
            materialRepository.save(hvBmMaterial);
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(hvBmMaterial.getId());
            extendInfo.setValues(materialDTO.getExtend());
            materialExtendService.updateExtendInfo(extendInfo);
        } else {
            materialRepository.save(hvBmMaterial);
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(hvBmMaterial.getId());
            extendInfo.setValues(materialDTO.getExtend());
            materialExtendService.addExtendInfo(extendInfo);
        }
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public int createMaterial(BaseMaterialDTO materialDTO) {
        //1.判断逻辑 传入计量单位ID 物料类型ID是否存在
        existsParameter(materialDTO);
        if (materialDTO.getEigenvalue() == null) {
            materialDTO.setEigenvalue("1");
        }
        HvBmMaterial hvBmMaterial = DtoMapper.convert(materialDTO, HvBmMaterial.class);
        if (materialDTO.getMaterialType() != null) {
            List<Integer> parentIds = materialTypeService.getParentIdBySonId(hvBmMaterial.getMaterialType());
            if (parentIds.size() > 0) {
                hvBmMaterial.setRootMaterialType(parentIds.get(0));
            }
        }
        materialRepository.save(hvBmMaterial);
        //3.判断传入DTO是否有扩展属性字段 不为空则添加 为空略过
        Utils.addExtendInfo(materialDTO.getExtend(), hvBmMaterial.getId(), materialExtendService);
        return hvBmMaterial.getId();
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public int updateMaterial(BaseMaterialDTO materialDTO) {
        existsParameter(materialDTO);
        HvBmMaterial hvBmMaterial = DtoMapper.convert(materialDTO, HvBmMaterial.class);
        if (materialDTO.getMaterialType() != null) {
            List<Integer> parentIds = materialTypeService.getParentIdBySonId(hvBmMaterial.getMaterialType());
            if (parentIds.size() > 0) {
                hvBmMaterial.setRootMaterialType(parentIds.get(0));
            }
        }
        materialRepository.save(hvBmMaterial);
        Utils.updateExtendInfo(materialDTO.getExtend(), hvBmMaterial.getId(), materialExtendService);
        return hvBmMaterial.getId();
    }


    /**
     * 验证传入计量单位ID 物料类型ID是否存在
     *
     * @param materialDTO 物料信息
     */
    private void existsParameter(BaseMaterialDTO materialDTO) {
        if (materialDTO.getUom() != null) {
            boolean uomExists = unitRepository.existsById(materialDTO.getUom());
            if (!uomExists) {
                throw new BaseKnownException(UOM_ERROR);
            }
        }
        if (materialDTO.getMaterialGroup() != null) {
            boolean materialGroup = materialGroupRepository.existsById(materialDTO.getMaterialGroup());
            if (!materialGroup) {
                throw new BaseKnownException(MATERIAL_TYPE_ERROR);
            }
        }
        if (materialDTO.getMaterialType() == null) {
            throw new BaseKnownException(MATERIAL_TYPE_NOT_NULL);
        }
    }


    /**
     * 加入物料单元符号，分组信息,物料类型信息
     *
     * @param dto 物料对象
     */
    private void setMaterialGroupAndUomAndType(MaterialDTO dto) {
        //插入单元符号
        Optional<HvBmUnit> unit = unitRepository.findById(dto.getUom());
        unit.ifPresent(hvBmUnit -> dto.setUomName(hvBmUnit.getSymbol()));
        //加入类型描述 / code
        if (dto.getMaterialGroup() != null) {
            Optional<HvBmMaterialGroup> materialGroup = materialGroupRepository.findById(dto.getMaterialGroup());
            if (materialGroup.isPresent()) {
                dto.setMaterialGroupDesc(materialGroup.get().getGroupName());
                dto.setMaterialGroupCode(materialGroup.get().getGroupCode());
            }
        }
        if (dto.getMaterialType() != null) {
            Optional<HvBmMaterialType> materialType = materialTypeRepository.findById(dto.getMaterialType());
            if (materialType.isPresent()) {
                dto.setMaterialTypeName(materialType.get().getMaterialTypeName());
                dto.setMaterialTypeCode(materialType.get().getMaterialTypeCode());
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MaterialDTO getMaterialById(Integer id) {
        //1.通过传入ID 获取数据
        HvBmMaterial hvBmMaterial = materialRepository.getOne(id);
        //2.转换Dto
        MaterialDTO dto = DtoMapper.convert(hvBmMaterial, MaterialDTO.class);
        //插入单元符号 加入类型描述
        setMaterialGroupAndUomAndType(dto);
        //3.加入扩展信息
        Map<String, Object> extendInfo = materialExtendService.getExtend(id);
        dto.setExtend(extendInfo);
        //4. 查询与物料绑定都bom
        HvBmBomMaterial hvBmBomMaterial = bomMaterialRepository.getHvBomMaterialByMaterialId(id);
        if (hvBmBomMaterial != null) {
            Optional<HvBmBom> bmBom = bomRepository.findById(hvBmBomMaterial.getBomId());
            bmBom.ifPresent(hvBmBom -> dto.setBomCode(hvBmBom.getBomCode()));
            bmBom.ifPresent(hvBmBom -> dto.setBomName(hvBmBom.getBomName()));
            bmBom.ifPresent(hvBmBom -> dto.setBomId(hvBmBom.getId()));
        }
        return dto;
    }

    /**
     * 根据批次号匹配物料信息
     *
     * @param setting  转换设置
     * @param batchNum 物料批次号
     * @return 对应的物料信息
     */
    private MaterialDTO regularBatchNum(HvBmParseSetting setting, String batchNum) {
        Map<String, String> matching = RegularUtils.matching(setting.getRegular(), batchNum);
        String code = matching.get("code");
        String eigenvalue = matching.get("eigenvalue");
        String id = matching.get("id");
        //如果ID为空  判断编码加特征值是否存在 如果其中一个为空 则无法确认物料信息
        MaterialDTO materialDTO = null;
        if (id == null) {
            if (eigenvalue == null) {
                List<HvBmMaterial> material
                        = materialRepository.getHvBmMaterialByMaterialCode(code);
                List<MaterialDTO> materialDTOS = DtoMapper.convertList(material, MaterialDTO.class);
                if (materialDTOS.size() > 0) {
                    if (materialDTOS.size() == 1) {
                        materialDTO = materialDTOS.get(0);
                    } else {
                        Optional<MaterialDTO> max = materialDTOS.stream().max(Comparator.comparingInt(SysBaseDTO::getId));
                        materialDTO = max.get();
                    }
                }
            } else {
                HvBmMaterial byMaterialCodeAndEigenvalue =
                        materialRepository.getByMaterialCodeAndEigenvalue(code, eigenvalue);
                materialDTO = DtoMapper.convert(byMaterialCodeAndEigenvalue, MaterialDTO.class);
            }
        } else {
            HvBmMaterial materialById = materialRepository.getOne(Integer.valueOf(id));
            materialDTO = DtoMapper.convert(materialById, MaterialDTO.class);
        }
        if (materialDTO == null) {
            throw new BaseKnownException(MaterialExceptionEnum.REGEX_ERROR_MATERIAL_NOT_FIND);
        }
        return materialDTO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer getMaterialIdByMaterialCode(String materialCode, String eigenvalue) {
        //根据编码和特征值查询物料信息
        HvBmMaterial hvBmMaterial = materialRepository
                .getByMaterialCodeAndEigenvalue(materialCode, eigenvalue);
        //如果查询为空 抛异常
        if (hvBmMaterial == null) {
            throw new BaseKnownException(MATERIAL_CODE_EIGENVALUE_ERROR);
        }
        //获取查询物料信息的ID
        return hvBmMaterial.getId();
    }

    /**
     * 根据物料编码和特征值查询物料
     *
     * @param materialCode 物料编码
     * @param eigenvalue   特征值
     * @return 物料信息
     */
    @Override
    public MaterialDTO getMaterialByMaterialCode(String materialCode, String eigenvalue) {
        HvBmMaterial hvBmMaterial = materialRepository
                .getByMaterialCodeAndEigenvalue(materialCode, eigenvalue);
        MaterialDTO materialDTO = DtoMapper.convert(hvBmMaterial, MaterialDTO.class);
        if (materialDTO != null) {
            setMaterialGroupAndUomAndType(materialDTO);
        }
        return materialDTO;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public List<BaseMaterialDTO> getAllMaterials() {
        //获取所有的设备信息
        List<HvBmMaterial> materialMasterData = materialRepository.findAll();

        //获取所有的设备扩展信息
        List<ExtendInfo> extendInfos = materialExtendService.getAll();
        //转换类型
        List<BaseMaterialDTO> materialDTOS = DtoMapper.convertList(materialMasterData, BaseMaterialDTO.class);

        //内存中拼接设备基础信息和扩展信息
        joinBaseDTOWithExtend(materialDTOS, extendInfos);

        return materialDTOS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<MaterialDTO> findByCodeOrName(MaterialQueryDTO materialQueryDTO) {
        //分页信息
        Page<HvBmMaterial> hvBmMaterials;
        //转换DTO
        HvBmMaterial hvBmMaterial = DtoMapper.convert(materialQueryDTO, HvBmMaterial.class);
        //查询
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("materialName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("materialCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("materialType", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("materialGroup", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        Example<HvBmMaterial> example = Example.of(hvBmMaterial, exampleMatcher);
        //根据materialQueryDTO传入的参数 查询所有 进行匹配
        hvBmMaterials = materialRepository.findAll(example, materialQueryDTO.getRequest());
        Page<MaterialDTO> materialDTOS = DtoMapper.convertPage(hvBmMaterials, MaterialDTO.class);
        List<ExtendInfo> extendInfos = materialExtendService.getExtend(hvBmMaterials.stream().map(SysBase::getId).collect(Collectors.toList()));
        //内存中拼接设备基础信息和扩展信息
        joinDTOWithExtend(materialDTOS, extendInfos);
        for (MaterialDTO materialDTO : materialDTOS) {
            //查找type数据 如果存在 执行SET方法 如果不存在 跳过
            setMaterialGroupAndUomAndType(materialDTO);
            //查询是否与bom有关联关系 ，如果有插入bom的code
            HvBmBomMaterial bomMaterial = bomMaterialRepository.getHvBomMaterialByMaterialId(materialDTO.getId());
            if (bomMaterial != null) {
                materialDTO.setBomCode(bomRepository.getOne(bomMaterial.getBomId()).getBomCode());
            }

        }
        return materialDTOS;
    }

    /**
     * 分页查询
     *
     * @param materialQueryDTO 查询条件对象
     * @return 分页信息
     */
    @Override
    public Page<MaterialDTO> findByQuery(MaterialQueryDTO materialQueryDTO) {
        Page<MaterialDTO> materialDTOS = PageHelperUtil.getPage(materialMapper::getMaterialByQuery,
                materialQueryDTO);
        List<Integer> integers = new ArrayList<>();
        materialDTOS.forEach(materialDTO -> integers.add(materialDTO.getId()));
        List<ExtendInfo> extendInfos = materialExtendService.getExtend(integers);
        //内存中拼接设备基础信息和扩展信息
        for (MaterialDTO materialDTO : materialDTOS) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (materialDTO.getId() == extendInfo.getEntityId()) {
                    materialDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
        return materialDTOS;
    }

    /**
     * 根据code name 模糊查询
     *
     * @param materialNameCodeDTO 查询条件对象
     * @return 分页信息
     */
    @Override
    public Page<MaterialDTO> getAllByCodeOrName(MaterialNameCodeDTO materialNameCodeDTO) {
        Page<MaterialDTO> materialDTOS;

        if (materialNameCodeDTO.getIsSearchProduct() == null || !materialNameCodeDTO.getIsSearchProduct()) {
            Page<HvBmMaterial> allByMaterialCodeOrMaterialName = materialRepository.getAllByMaterialCodeContainsOrMaterialNameContains(materialNameCodeDTO.getNameOrCode(),
                    materialNameCodeDTO.getNameOrCode(), materialNameCodeDTO.getRequest());
            materialDTOS = DtoMapper.convertPage(allByMaterialCodeOrMaterialName, MaterialDTO.class);
        } else {
            int productId = getProductId();
            int semiProductId = getSemiProductId();
            Page<HvBmMaterial> allByMaterialTypeByStringCodeOrName = materialRepository.getAllByMaterialTypeByStringCodeOrName(productId, semiProductId,
                    materialNameCodeDTO.getNameOrCode(), materialNameCodeDTO.getNameOrCode(),
                    materialNameCodeDTO.getRequest());
            materialDTOS = DtoMapper.convertPage(allByMaterialTypeByStringCodeOrName, MaterialDTO.class);
        }
        //内存中拼接设备基础信息和扩展信息
        for (MaterialDTO materialDTO : materialDTOS) {
            //查找type数据 如果存在 执行SET方法 如果不存在 跳过
            setMaterialGroupAndUomAndType(materialDTO);
            //查询是否与bom有关联关系 ，如果有插入bom的code
            HvBmBomMaterial bomMaterial = bomMaterialRepository.getHvBomMaterialByMaterialId(materialDTO.getId());
            if (bomMaterial != null) {
                Optional<HvBmBom> byId = bomRepository.findById(bomMaterial.getBomId());
                byId.ifPresent(hvBmBom -> materialDTO.setBomCode(hvBmBom.getBomCode()));
            }
        }
        return materialDTOS;
    }


    /**
     * {@inheritDoc}
     */
    private void joinDTOWithExtend(Iterable<MaterialDTO> masterDataDTOS, Iterable<ExtendInfo> extendInfos) {
        for (MaterialDTO materialDTO : masterDataDTOS) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (materialDTO.getId() == extendInfo.getEntityId()) {
                    materialDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }


    private void joinBaseDTOWithExtend(Iterable<BaseMaterialDTO> masterDataDTOS, Iterable<ExtendInfo> extendInfos) {
        for (BaseMaterialDTO materialDTO : masterDataDTOS) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (materialDTO.getId() == extendInfo.getEntityId()) {
                    materialDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResultVO<ExcelExportDto> exportMaterials() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result = export();
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(MaterialConst.MATERIAL_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * 导出所有物料信息
     *
     * @return 物料信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResponseEntity<byte[]> export() throws IOException, IllegalAccessException {
        List<MaterialDTO> materialDTOS = DtoMapper.convertList(materialRepository.findAll(), MaterialDTO.class);

        for (MaterialDTO materialDTO : materialDTOS) {
            setMaterialGroupAndUomAndType(materialDTO);
            Map<String, Object> extendInfo = materialExtendService.getExtend(materialDTO.getId());
            materialDTO.setExtend(extendInfo);

        }
        List<ImportMaterialDTO> exportMaterialDTOS = DtoMapper.convertList(materialDTOS, ImportMaterialDTO.class);
        return ExcelUtil.generateImportFile(exportMaterialDTOS, MaterialConst.MATERIAL_EXPORT_FILE_NAME, ImportMaterialDTO.class, materialExtendService.getExtendColumnInfo());
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public ResultVO<ExcelExportDto> getMaterialsImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(ImportMaterialDTO.class, MaterialConst.MATERIAL_EXPORT_FILE_NAME, materialExtendService.getExtendColumnInfo());
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(MaterialConst.MATERIAL_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = true)
    public ImportResult importMaterials(MultipartFile file) throws
            IllegalAccessException, ParseException, IOException {
        return ExcelUtil.importEntity(file, MaterialDTO.class, this, materialExtendService.getExtendColumnInfo());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteMaterialById(Integer id) {
        if (!materialRepository.existsById(id)) {
            throw new BaseKnownException(MATERIAL_NOT_EXISTS);
        }
        //如果material与Bom,bomItem,substituteItem存在关联关系 不允许删除
        if (bomMaterialRepository.existsByMaterialId(id)) {
            throw new BaseKnownException(MATERIAL_BOM_RELEVANCY);
        }
        //判断物料是否被Item使用
        if (bomItemRepository.existsByMaterialsId(id)) {
            throw new BaseKnownException(MATERIAL_BOM_ITEM_RELEVANCY);
        }
        //判断物料是否被替代料使用
        if (substituteItemRepository.existsByMaterialsId(id)) {
            throw new BaseKnownException(MATERIAL_SUBSTITUTE_ITEM_RELEVANCY);
        }
        materialRepository.deleteById(id);
        materialExtendService.deleteExtendInfo(id);

    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Integer createBomMaterial(BomMaterialDTO bomMaterialDTO) {
        HvBmBomMaterial hvBmBomMaterial = new HvBmBomMaterial();
        if ("".equals(bomMaterialDTO.getBomVersions()) || bomMaterialDTO.getBomCode() == null) {
            throw new BaseKnownException(MaterialExceptionEnum.BOM_CODE_OR_VERSION_NOT_NULL);
        }
        Integer bomId;
        //获取要绑定的BOMID
        if (bomMaterialDTO.getBomId() == null) {
            HvBmBom byBomCodeAndBomVersions = bomRepository.findByBomCodeAndBomVersions(bomMaterialDTO.getBomCode(), bomMaterialDTO.getBomVersions());
            if (byBomCodeAndBomVersions == null) {
                throw new BaseKnownException(MaterialExceptionEnum.BOM_NOT_FIND);
            }
            bomId = byBomCodeAndBomVersions.getId();
        } else {
            bomId = bomMaterialDTO.getBomId();
        }
        //判断bomID是否已被绑定
        boolean bomIdExists = bomMaterialRepository.existsByBomId(bomId);
        if (bomIdExists) {
            throw new BaseKnownException(MaterialExceptionEnum.BOM_MATERIAL_IS_EXISTS);
        }
        //判断物料是否已绑定BOM
        boolean materialsExists = bomMaterialRepository.existsByMaterialId(bomMaterialDTO.getMaterialId());
        if (materialsExists) {
            throw new BaseKnownException(MaterialExceptionEnum.MATERIAL_HAVE_BOM);
        }
        //判断BOM状态是否为生效归档
        HvBmBom hvBmBom = bomRepository.getOne(bomId);
        if (hvBmBom.getBomStatus() == TWO) {
            throw new BaseKnownException(BOM_STATUS_ARCHIVED);
        }
        hvBmBomMaterial.setBomId(bomId);
        hvBmBomMaterial.setMaterialId(bomMaterialDTO.getMaterialId());
        //加入关联关系
        HvBmBomMaterial dto = bomMaterialRepository.save(DtoMapper.convert(hvBmBomMaterial, HvBmBomMaterial.class));

        return dto.getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBomMaterialByMaterialId(int materialId) {
        bomMaterialRepository.deleteByMaterialId(materialId);
    }

    /**
     * {@inheritDoc}
     */
    private MaterialDTO regularBatchNumFixPosition(String batchNum, HvBmParseSetting parseSetting) {
        MaterialDTO materialDTO = null;
        try {
            String code = "";
            try {
                code = batchNum.substring(parseSetting.getCodeStart() - 1, parseSetting.getCodeEnd());
            } catch (Exception ignore) {
            }
            String eigenvalue = "";
            try {
                eigenvalue = batchNum.substring(parseSetting.getEigenStart() - 1, parseSetting.getEigenEnd());
            } catch (Exception ignore) {
            }
            //如果物料特征值都是空报错
            if (StringUtils.isEmpty(code) && StringUtils.isEmpty(eigenvalue)) {
                throw new BaseKnownException(MaterialExceptionEnum.REGEX_ERROR_MATERIAL_NOT_FIND);
            }
            //如果特征值为空
            else if (StringUtils.isEmpty(eigenvalue)) {
                List<HvBmMaterial> material
                        = materialRepository.getHvBmMaterialByMaterialCode(code);
                List<MaterialDTO> materialDTOS = DtoMapper.convertList(material, MaterialDTO.class);
                if (materialDTOS.size() > 0) {
                    if (materialDTOS.size() == 1) {
                        materialDTO = materialDTOS.get(0);
                    } else {
                        Optional<MaterialDTO> max = materialDTOS.stream().max(Comparator.comparingInt(SysBaseDTO::getId));
                        materialDTO = max.get();
                    }
                }
            } else {
                HvBmMaterial byMaterialCodeAndEigenvalue =
                        materialRepository.getByMaterialCodeAndEigenvalue(code, eigenvalue);
                materialDTO = DtoMapper.convert(byMaterialCodeAndEigenvalue, MaterialDTO.class);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BaseKnownException(MaterialExceptionEnum.REGEX_ERROR_MATERIAL_NOT_FIND);
        }
        if (materialDTO == null) {
            throw new BaseKnownException(MaterialExceptionEnum.REGEX_ERROR_MATERIAL_NOT_FIND);
        }
        return materialDTO;
    }

    /**
     * 获取物料类型为产品的ID
     *
     * @return ID
     */
    private int getProductId() {
        HvBmMaterialType product = materialTypeRepository.getAllByMaterialTypeCode("product");
        return product.getId();
    }

    /**
     * 获取物料类型为半成品品的ID
     *
     * @return ID
     */
    private int getSemiProductId() {
        HvBmMaterialType semiProduct = materialTypeRepository.getAllByMaterialTypeCode("semi_product");
        return semiProduct.getId();
    }

    /**
     * 获取所有成品 半成品物料信息
     *
     * @param materialQueryDTO 查询条件
     * @return 物料信息
     */
    @Override
    public Page<MaterialDTO> getMaterial(QueryDTO materialQueryDTO) {
        materialQueryDTO.setSort(true);
        if (materialQueryDTO.getSortCol() == null) {
            materialQueryDTO.setSortCol("material_name");
            materialQueryDTO.setDirection(true);
        }
        List<Integer> integers = new ArrayList<>();
        Page<MaterialDTO> materialDTOS = PageHelperUtil.getPage(materialMapper::getMaterials, materialQueryDTO);
        if (materialQueryDTO.getIsExtend() != null) {
            if (materialQueryDTO.getIsExtend()) {
                materialDTOS.forEach(materialDTO -> integers.add(materialDTO.getId()));
                List<ExtendInfo> extendInfos = materialExtendService.getExtend(integers);
                for (MaterialDTO materialDTO : materialDTOS) {
                    for (ExtendInfo extendInfo : extendInfos) {
                        if (materialDTO.getId() == extendInfo.getEntityId()) {
                            materialDTO.setExtend(extendInfo.getValues());
                            break;
                        }
                    }
                }
            }
        }
        return materialDTOS;
    }

    /**
     * 获取所有成品 半成品物料信息
     *
     * @return 物料信息
     */
    @Override
    public List<MaterialDTO> getAllProductAndByProduct() {
        int productId = getProductId();
        int semiProductId = getSemiProductId();
        List<MaterialDTO> materialDTOS = DtoMapper.convertList(materialRepository.getListByMaterialType(
                productId,
                semiProductId), MaterialDTO.class);
        setBomMessage(materialDTOS);
        return materialDTOS;
    }

    /**
     * 根据物料类型查询物料
     *
     * @param typeId 物料类型id
     * @return 物料信息
     */
    @Override
    public List<MaterialDTO> getMaterialByTypeId(int typeId) {
        List<HvBmMaterial> allByMaterialType = materialRepository.getAllByMaterialType(typeId);
        List<MaterialDTO> materialDTOS = DtoMapper.convertList(allByMaterialType, MaterialDTO.class);
        setBomMessage(materialDTOS);
        List<HvBmMaterialType> hvBmMaterialTypes = materialTypeRepository.findAll();
        List<HvBmMaterialGroup> groups = materialGroupRepository.findAll();
        for (MaterialDTO materialDTO : materialDTOS) {
            Optional<HvBmMaterialType> type =
                    hvBmMaterialTypes.stream().filter(t -> t.getId().equals(materialDTO.getMaterialType())).findFirst();
            if (type.isPresent()) {
                materialDTO.setMaterialTypeName(type.get().getMaterialTypeName());
                materialDTO.setMaterialTypeCode(type.get().getMaterialTypeCode());
            }
            Optional<HvBmMaterialGroup> group =
                    groups.stream().filter(t -> t.getId().equals(materialDTO.getMaterialGroup())).findFirst();
            if (group.isPresent()) {
                materialDTO.setMaterialGroupCode(group.get().getGroupCode());
                materialDTO.setMaterialGroupDesc(group.get().getGroupName());
            }
        }
        List<ExtendInfo> extend = materialExtendService.getExtend(materialDTOS.stream().map(t -> t.getId()).collect(Collectors.toList()));
        joinDTOWithExtend(materialDTOS, extend);
        return materialDTOS;
    }


    private void setBomMessage(Iterable<MaterialDTO> materialDTOS) {
        for (MaterialDTO materialDTO : materialDTOS) {
            HvBmBomMaterial hvBomMaterialByMaterialId =
                    bomMaterialRepository.getHvBomMaterialByMaterialId(materialDTO.getId());
            if (hvBomMaterialByMaterialId != null) {
                materialDTO.setBomId(hvBomMaterialByMaterialId.getBomId());

                Optional<HvBmBom> byId = bomRepository.findById(hvBomMaterialByMaterialId.getBomId());
                if (byId.isPresent()) {
                    materialDTO.setBomCode(byId.get().getBomCode());
                    materialDTO.setBomName(byId.get().getBomName());
                    materialDTO.setBomVersion(byId.get().getBomVersions());
                }
            }

        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MaterialDTO findMaterialByBatch(String batchNum) {
        HvBmParseSetting setting = parseSettingRepository.findTop();
        if (setting == null) {
            throw new BaseKnownException(MaterialExceptionEnum.MATERIAL_PARSE_NOT_SET);
        } else {
            //如果是固定位置
            if (MaterialParseTypeEnum.FIX_POSITION.getCode().equals(setting.getParseType())) {
                return regularBatchNumFixPosition(batchNum, setting);
            } else if (MaterialParseTypeEnum.REGULAR.getCode().equals(setting.getParseType())) {
                try {
                    return regularBatchNum(setting, batchNum);
                } catch (Exception ex) {
                    throw new BaseKnownException(MaterialExceptionEnum.REGEX_ERROR_MATERIAL_NOT_FIND);
                }
            } else {
                throw new BaseKnownException(MaterialExceptionEnum.MATERIAL_PARSE_NOT_SET);
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void updateSetting(ParseSettingDTO parseSettingDTO) {
        parseSettingDTO.valid();
        HvBmParseSetting setting = parseSettingRepository.findTop();
        int id = 0;
        if (setting == null) {
            setting = new HvBmParseSetting();
        } else {
            id = setting.getId();
        }
        BeanUtils.copyProperties(parseSettingDTO, setting);
        if (id > 0) {
            setting.setId(id);
        }
        parseSettingRepository.save(setting);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ParseSettingDTO getSetting() {
        HvBmParseSetting setting = parseSettingRepository.findTop();
        if (setting == null) {
            return new ParseSettingDTO();
        }
        return DtoMapper.convert(setting, ParseSettingDTO.class);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public List<MaterialDTO> getMaterialsByIdList(List<Integer> list) {
        List<HvBmMaterial> hvBmMaterials = materialRepository.getHvBmMaterialByIdIn(list);
        List<MaterialDTO> dtos = DtoMapper.convertList(hvBmMaterials, MaterialDTO.class);
        List<ExtendInfo> extendInfos = materialExtendService.getExtend(hvBmMaterials.stream().map(SysBase::getId).collect(Collectors.toList()));
        joinDTOWithExtend(dtos, extendInfos);
        for (MaterialDTO materialDTO : dtos) {
            setMaterialGroupAndUomAndType(materialDTO);
            HvBmBomMaterial hvBomMaterial = bomMaterialRepository.getHvBomMaterialByMaterialId(materialDTO.getId());
            //如果物料与BOM有关联关系 加入BOM编码
            if (hvBomMaterial != null) {
                Optional<HvBmBom> hvBmBom = bomRepository.findById(hvBomMaterial.getBomId());
                hvBmBom.ifPresent(hvBmBom1 -> materialDTO.setBomCode(hvBmBom1.getBomCode()));
            }

        }
        return dtos;
    }

    /**
     * 根据物料编码模糊查询 物料特征值物料编码 以及拼接字符串
     *
     * @param materialCodeQueryDTO 物料编码和物料特征值以及拼接字符串dto
     * @return 物料编码特征值信息列表
     */
    @Override
    public List<MaterialDTO> getHvBmMaterialByMaterialCodeLike(MaterialCodeQueryDTO materialCodeQueryDTO) {

        List<HvBmMaterial> hvBmMaterials;
        //转换DTO
        HvBmMaterial hvBmMaterial = DtoMapper.convert(materialCodeQueryDTO, HvBmMaterial.class);
        //查询
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("materialCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("materialName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("eigenvalue", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("materialType", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("materialGroup", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        Example<HvBmMaterial> example = Example.of(hvBmMaterial, exampleMatcher);
        hvBmMaterials = materialRepository.findAll(example);
        List<MaterialDTO> dtos = DtoMapper.convertList(hvBmMaterials, MaterialDTO.class);
        //循环加入单位名称
        for (MaterialDTO materialDTO : dtos) {
            setMaterialGroupAndUomAndType(materialDTO);
        }

        return dtos;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void updateMaterialRootType() {
        log.info("补充所有物料的rootType信息");
        List<HvBmMaterial> materials = materialRepository.findAllByRootMaterialTypeNull();
        for (HvBmMaterial material : materials) {
            if (material.getMaterialType() != null && material.getMaterialType() > 0) {
                List<Integer> parentIds = materialTypeService.getParentIdBySonId(material.getMaterialType());
                if (parentIds.size() > 0) {
                    material.setRootMaterialType(parentIds.get(0));
                    materialRepository.save(material);
                    log.info("物料{},已经创建根类型", material.getMaterialCode());
                }
            }
        }
    }

    @Transactional
    @Override
    public ResultVO<Integer> materialPushDataSaveAndUpdateAndDelete(MDMPushMaterialDTO mdmPush) {
        log.info("接收到mdm的物料数据{}",mdmPush);
        PkgMatQueryDto mdmPushMaterialDTO = mdmPush.getPkgMatQuery();
        //上面的物料类型编码我们只要A001、A003、A005
        if(!"A001".equals(mdmPushMaterialDTO.getMatTypeCode()) && !"A003".equals(mdmPushMaterialDTO.getMatTypeCode()) && !"A005".equals(mdmPushMaterialDTO.getMatTypeCode())){
            return new ResultVO<>(200, "success");
        }

        MaterialTypeDTO materialTypeByCode = null;
        HvBmUnit unit = null;
        if (Objects.nonNull(mdmPushMaterialDTO.getMatTypeCode())) {
            materialTypeByCode = materialTypeService.getMaterialTypeByCode(mdmPushMaterialDTO.getMatTypeCode());
        }
        if (Objects.nonNull(mdmPushMaterialDTO.getBasicMeaUnitId())) {
            unit = unitRepository.getBySymbol(mdmPushMaterialDTO.getBasicMeaUnitId());
        }

        //物料类型没有
        if (Objects.nonNull(mdmPushMaterialDTO.getMatTypeCode()) && Objects.isNull(materialTypeByCode)) {
            MaterialTypeDTO materialTypeDTO = new MaterialTypeDTO();
            materialTypeDTO.setMaterialTypeName(mdmPushMaterialDTO.getMatTypeCode());
            materialTypeDTO.setMaterialTypeCode(mdmPushMaterialDTO.getMatTypeCode());
            materialTypeByCode = materialTypeService.createMaterialType(materialTypeDTO);
        }

        //单位不存在
        if (Objects.nonNull(mdmPushMaterialDTO.getBasicMeaUnitId()) && Objects.isNull(unit)) {
            UnitDTO unitDTO = new UnitDTO();
            unitDTO.setSymbol(mdmPushMaterialDTO.getBasicMeaUnitId());
            unitDTO.setDescription(mdmPushMaterialDTO.getBasicMeaUnitId());
            unitService.createUnit(unitDTO);
            unit = unitRepository.getBySymbol(unitDTO.getSymbol());
        }
        MaterialTypeDTO finalMaterialTypeByCode = materialTypeByCode;
        List<BaseMaterialDTO> allMaterial = baseMaterialService.findAll().stream()
                .map(t -> DtoMapper.convert(t, BaseMaterialDTO.class)).collect(Collectors.toList());
        List<BaseMaterialDTO> collect = allMaterial.stream()
                .filter(item -> item.getMaterialCode().equals(mdmPushMaterialDTO.getSapMatCode()) && item.getMaterialType().toString().equals(finalMaterialTypeByCode.getId().toString())).collect(Collectors.toList());

        if ("1".equals(mdmPushMaterialDTO.getHubStateInd().toString())) {
            //新增，修改
            if (CollectionUtils.isNotEmpty(collect)) {
//                ，修改不能修改物料类型
                BaseMaterialDTO baseMaterialDTO = new BaseMaterialDTO();
                baseMaterialDTO.setId(collect.get(0).getId());
                baseMaterialDTO.setMaterialCode(mdmPushMaterialDTO.getSapMatCode());
                baseMaterialDTO.setMaterialName(mdmPushMaterialDTO.getMatName());
                baseMaterialDTO.setUom(unit.getId());
                baseMaterialDTO.setMaterialType(collect.get(0).getMaterialType());
                updateMaterial(baseMaterialDTO);
                return new ResultVO<>(200, "success");
            } else {
                //新增
                BaseMaterialDTO baseMaterialDTO = new BaseMaterialDTO();
                baseMaterialDTO.setMaterialCode(mdmPushMaterialDTO.getSapMatCode());
                baseMaterialDTO.setMaterialName(mdmPushMaterialDTO.getMatName());
                baseMaterialDTO.setUom(unit.getId());
                baseMaterialDTO.setMaterialType(materialTypeByCode.getId());
                createMaterial(baseMaterialDTO);
                return new ResultVO<>(200, "success");
            }
        } else if ("-1".equals(mdmPushMaterialDTO.getHubStateInd().toString())) {
            //删除
           if(CollectionUtils.isNotEmpty(collect)){
               deleteMaterialById(collect.get(0).getId());
               return new ResultVO<>(200, "success");
           }
        }
        return new ResultVO<>(200, "success");
    }

    @Transactional
    @Override
    public ResultVO<Integer> rowMaterialPushDataSaveAndUpdateAndDelete(MDMPushRowMaterialDTO mdmPushRow) {
        log.info("接收到mdm的原辅物料数据{}",mdmPushRow);
        PkgMatQueryDto mdmPushMaterialDTO = mdmPushRow.getPkgJmsRawPackMaterials();
        //上面的物料类型编码我们只要A001、A003、A005
        if(!"ZROH".equals(mdmPushMaterialDTO.getMatTypeCode())){
            return new ResultVO<>(200, "success");
        }
        MaterialTypeDTO materialTypeByCode = null;
        HvBmUnit unit = null;
        if (Objects.nonNull(mdmPushMaterialDTO.getMatTypeCode())) {
            materialTypeByCode = materialTypeService.getMaterialTypeByCode(mdmPushMaterialDTO.getMatTypeCode());
        }
        if (Objects.nonNull(mdmPushMaterialDTO.getBasicMeaUnitId())) {
            unit = unitRepository.getBySymbol(mdmPushMaterialDTO.getBasicMeaUnitId());
        }

        //物料类型没有
        if (Objects.nonNull(mdmPushMaterialDTO.getMatTypeCode()) && Objects.isNull(materialTypeByCode)) {
            MaterialTypeDTO materialTypeDTO = new MaterialTypeDTO();
            materialTypeDTO.setMaterialTypeName(mdmPushMaterialDTO.getMatTypeCode());
            materialTypeDTO.setMaterialTypeCode(mdmPushMaterialDTO.getMatTypeCode());
            materialTypeByCode = materialTypeService.createMaterialType(materialTypeDTO);
        }

        //单位不存在
        if (Objects.nonNull(mdmPushMaterialDTO.getBasicMeaUnitId()) && Objects.isNull(unit)) {
            UnitDTO unitDTO = new UnitDTO();
            unitDTO.setSymbol(mdmPushMaterialDTO.getBasicMeaUnitId());
            unitDTO.setDescription(mdmPushMaterialDTO.getBasicMeaUnitId());
            unitService.createUnit(unitDTO);
            unit = unitRepository.getBySymbol(unitDTO.getSymbol());
        }
        MaterialTypeDTO finalMaterialTypeByCode = materialTypeByCode;
        List<BaseMaterialDTO> allMaterial = baseMaterialService.findAll().stream()
                .map(t -> DtoMapper.convert(t, BaseMaterialDTO.class)).collect(Collectors.toList());
        List<BaseMaterialDTO> collect = allMaterial.stream()
                .filter(item -> item.getMaterialCode().equals(mdmPushMaterialDTO.getSapMatCode()) && item.getMaterialType().toString().equals(finalMaterialTypeByCode.getId().toString())).collect(Collectors.toList());
        if ("1".equals(mdmPushMaterialDTO.getHubStateInd().toString())) {
            //新增，修改
            if (CollectionUtils.isNotEmpty(collect)) {
//                ，修改不能修改物料类型
                BaseMaterialDTO baseMaterialDTO = new BaseMaterialDTO();
                baseMaterialDTO.setId(collect.get(0).getId());
                baseMaterialDTO.setMaterialCode(mdmPushMaterialDTO.getSapMatCode());
                baseMaterialDTO.setMaterialName(mdmPushMaterialDTO.getMatName());
                baseMaterialDTO.setUom(unit.getId());
                baseMaterialDTO.setMaterialType(collect.get(0).getMaterialType());
                updateMaterial(baseMaterialDTO);
                return new ResultVO<>(200, "success");
            } else {
                //新增
                BaseMaterialDTO baseMaterialDTO = new BaseMaterialDTO();
                baseMaterialDTO.setMaterialCode(mdmPushMaterialDTO.getSapMatCode());
                baseMaterialDTO.setMaterialName(mdmPushMaterialDTO.getMatName());
                baseMaterialDTO.setUom(unit.getId());
                baseMaterialDTO.setMaterialType(materialTypeByCode.getId());
                createMaterial(baseMaterialDTO);
                return new ResultVO<>(200, "success");
            }
        } else if ("-1".equals(mdmPushMaterialDTO.getHubStateInd().toString())) {
            //删除
            if(CollectionUtils.isNotEmpty(collect)){
                deleteMaterialById(collect.get(0).getId());
                return new ResultVO<>(200, "success");
            }
        }
        return new ResultVO<>(200, "success");
    }
}
