package com.hvisions.materialsmsd.service.imp;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.materialsmsd.SysBaseDTO;
import com.hvisions.materialsmsd.consts.BomConst;
import com.hvisions.materialsmsd.bom.dto.*;
import com.hvisions.materialsmsd.entity.*;
import com.hvisions.materialsmsd.bom.enums.BomExceptionEnum;
import com.hvisions.materialsmsd.repository.*;
import com.hvisions.materialsmsd.service.BomItemService;
import com.hvisions.materialsmsd.service.BomService;
import com.hvisions.materialsmsd.service.SubstituteItemService;
import com.hvisions.materialsmsd.utils.Utils;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterial;
import com.hvisions.materialsmsd.entity.SysBase;
import com.hvisions.materialsmsd.materials.enums.MaterialExceptionEnum;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.hvisions.materialsmsd.bom.enums.BomExceptionEnum.*;
import static com.hvisions.materialsmsd.utils.Utils.addExtendInfo;
import static com.hvisions.materialsmsd.utils.Utils.updateExtendInfo;

/**
 * <p>Title: BomServiceImp</p >
 * <p>Description: bom实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/26</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class BomServiceImpl implements BomService {


    private final BomRepository bomRepository;
    private final MaterialRepository materialRepository;
    private final BomItemRepository bomItemRepository;
    private final SubstituteItemRepository substituteItemRepository;
    private final UnitRepository unitRepository;
    private final BomMaterialRepository bomMaterialRepository;
    private final BomItemService bomItemService;
    private final SubstituteItemService substituteItemService;
    private final int MIN_BOM_COUNT = 0;
    //新建bom
    private final int NEWLY = 0;
    //bom生效状态
    private final int TAKEEFFECT = 1;
    //归档
    private final int File = 2;

    @Resource(name = "bom_extend")
    BaseExtendService bomExtendService;
    @Resource(name = "bom_item_extend")
    BaseExtendService bomItemExtendService;
    @Resource(name = "substitute_item_extend")
    BaseExtendService substituteItemExtendService;

    @Autowired
    public BomServiceImpl(BomRepository bomRepository,
                          MaterialRepository materialRepository,
                          BomItemRepository bomItemRepository,
                          SubstituteItemRepository substituteItemRepository,
                          UnitRepository unitRepository,
                          BomMaterialRepository bomMaterialRepository,
                          BomItemService bomItemService,
                          SubstituteItemService substituteItemService) {

        this.bomRepository = bomRepository;
        this.materialRepository = materialRepository;
        this.bomItemRepository = bomItemRepository;
        this.substituteItemRepository = substituteItemRepository;
        this.unitRepository = unitRepository;
        this.bomMaterialRepository = bomMaterialRepository;
        this.bomItemService = bomItemService;
        this.substituteItemService = substituteItemService;


    }

    @Override
    public void saveOrUpdate(BomDTO bomDTO) {
        HvBmBom hvBmBom = DtoMapper.convert(bomDTO, HvBmBom.class);
        HvBmBom hvBmBomlOld = bomRepository.findFirstByBomCodeAndBomVersions(hvBmBom.getBomCode(), hvBmBom.getBomVersions());
        HvBmUnit hvBmUnit = unitRepository.getBySymbol(bomDTO.getSymbol());
        if (hvBmUnit == null) {
            throw new BaseKnownException(UNIT_SYMBOL_NOT_NULL_PLEASE_FILL_IN_CORRECTLYU);
        }
        createOrUpdateExtend(bomDTO, hvBmBom, hvBmBomlOld, hvBmUnit);
    }


    /**
     * 添加bom信息
     *
     * @param bomDTO 传入Dto对象
     * @return bom id
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createBom(BaseBomDTO bomDTO) {
        if (bomDTO.getBomCount() != null) {
            if (bomDTO.getBomCount().intValue() <= MIN_BOM_COUNT) {
                throw new BaseKnownException(BomExceptionEnum.BOM_NOT_LESSTHEN_ZERO);
            }
        }
        //1.逻辑判断 unitId是否存在  bom，mid是否已存在关联关系
        assessUom(bomDTO);
        //2.初始化BomState 新增为0
        bomDTO.setBomStatus(NEWLY);
        //3.保存Bom
        HvBmBom hvBmBom = bomRepository.save(DtoMapper.convert(bomDTO, HvBmBom.class));
        //4.判断是否有添加扩展属性 不为空添加
        addExtendInfo(bomDTO.getExtend(), hvBmBom.getId(), bomExtendService);
        //5.返回新增BomId
        return hvBmBom.getId();
    }


    /**
     * 更新bom信息
     *
     * @param bomDTO 传入bomDto
     * @return bomId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateBom(BaseBomDTO bomDTO) {
        //判断当前更改的bom状态是否为新增，如果不是报错
        HvBmBom bom = bomRepository.getOne(bomDTO.getId());
        int status = bom.getBomStatus();
        if (status != NEWLY) {
            throw new BaseKnownException(BomExceptionEnum.NOT_NEW_BOM_CANNOT_BE_UPDATE);
        }
        //判断计量单位
        assessUom(bomDTO);
        HvBmBom hvBmBom = bomRepository.save(DtoMapper.convert(bomDTO, HvBmBom.class));
        //5.判断是否有扩展字段加入 如果有修改保存
        updateExtendInfo(bomDTO.getExtend(), hvBmBom.getId(), bomExtendService);
        //6.返回bomId
        return hvBmBom.getId();
    }

    /**
     * 通过materials id 查询BOM信息
     *
     * @param materialsId 物料主数据id
     * @return bomDTO
     */

    @Override
    public List<BomDTO> getBomByMaterialsId(Integer materialsId) {
        //判断materialId是否存在
        boolean midExists = bomMaterialRepository.existsByMaterialId(materialsId);
        if (!midExists) {
            throw new BaseKnownException(BOM_MATERIAL_NOT_EXISTS);
        }
        //根据materialId获取关联关系
        HvBmBomMaterial hvBmBomMaterial = bomMaterialRepository.getHvBomMaterialByMaterialId(materialsId);
        //获取与material关联的bomID
        int bomId = hvBmBomMaterial.getBomId();
        //获取Bom信息
        List<HvBmBom> objects = new ArrayList<>();
        Optional<HvBmBom> byId = bomRepository.findById(bomId);
        if (byId.isPresent()) {
            objects.add(byId.get());
            List<ExtendInfo> extendInfo = bomExtendService.getAll();
            List<BomDTO> bomDTO = DtoMapper.convertList(objects, BomDTO.class);
            joinDTOWithExtend(bomDTO, extendInfo);
            return bomDTO;
        }

        return DtoMapper.convertList(objects, BomDTO.class);
    }

    @Override
    public int getBomIdByMaterialId(Integer materialId) {
        boolean midExists = bomMaterialRepository.existsByMaterialId(materialId);
        if (!midExists) {
            throw new BaseKnownException(BOM_MATERIAL_NOT_EXISTS);
        }
        return bomMaterialRepository.getHvBomMaterialByMaterialId(materialId).getBomId();
    }


    /**
     * 删除bom信息
     *
     * @param id bomId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBom(int id) {
        HvBmBom hvBmBom = bomRepository.getOne(id);
        if (hvBmBom.getBomStatus() != NEWLY) {
            throw new BaseKnownException(BomExceptionEnum.NOT_NEW_BOM_CANNOT_BE_UPDATE);
        }
        bomRepository.deleteById(id);
        bomExtendService.deleteExtendInfo(id);
        bomMaterialRepository.deleteAllByBomId(id);
        List<HvBmBomItem> hvBmBomItems = bomItemRepository.getByBomId(id);
        bomItemRepository.deleteAllByBomId(id);
        //传入ID列表一次删除
        List<Integer> bomItemId = hvBmBomItems.stream().map(SysBase::getId).collect(Collectors.toList());
        substituteItemRepository.deleteAllByBomItemIdIn(bomItemId);

    }


    /**
     * 更改state状态
     *
     * @param id bomId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setStatusTakeEffect(int id) {
        HvBmBom bmBom = bomRepository.getAllById(id);
        //只有新增的才能更改
        if (bmBom.getBomStatus() != NEWLY) {
            throw new BaseKnownException(NOW_NEWLY_CANNOT_BE_MODIFIED);
        }
        //获取同名code 状态
        List<HvBmBom> boms = bomRepository.getAllBomByBomCode(bmBom.getBomCode());

        for (HvBmBom bom : boms) {
            //状态为1的改为2
            if (bom.getBomStatus() == TAKEEFFECT) {
                bom.setBomStatus(File);
                //获取状态1的bomId
                Integer bomTakeEffect = bom.getId();
                //查询这个bom与materials的关联关系
                HvBmBomMaterial bomMaterial = bomMaterialRepository.getHvBomMaterialByBomId(bomTakeEffect);
                if (bomMaterial != null) {
                    //获取关联的物料ID
                    Integer bomMaterialId = bomMaterial.getMaterialId();
                    bomMaterialRepository.deleteById(bomMaterial.getId());
                    //如果物料ID不等于空
                    if (bomMaterialId != null) {
                        HvBmBomMaterial hvBmBomMaterial = new HvBmBomMaterial();
                        hvBmBomMaterial.setBomId(id);
                        hvBmBomMaterial.setMaterialId(bomMaterialId);
                        bomMaterialRepository.save(hvBmBomMaterial);
                    }
                }
                bomRepository.save(bom);
            }
        }
        //更改状态
        bmBom.setBomStatus(TAKEEFFECT);
        bomRepository.save(bmBom);

    }

    /**
     * bom归档
     *
     * @param id bomId
     */
    @Override
    public void discardedBom(int id) {
        HvBmBom bmBom = bomRepository.getAllById(id);
        if (bmBom.getBomStatus() == TAKEEFFECT) {
            bmBom.setBomStatus(File);
        } else {
            throw new BaseKnownException(MaterialExceptionEnum.BOM_STATE_NOT_TAKEEFFECT);
        }
        bomRepository.save(bmBom);
    }

    /**
     * 根据编码 名称 类型 模糊查询
     */
    @Override
    public Page<BomDTO> findByBomCodeOrBomNameOrBomVersionsOrBomStatus(BomQueryDTO bomQueryDTO) {
        Page<HvBmBom> hvBmBoms;
        HvBmBom bmBom = DtoMapper.convert(bomQueryDTO, HvBmBom.class);
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("bomName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("bomCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("bomVersions", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("bomStatus", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());

        Example<HvBmBom> example = Example.of(bmBom, exampleMatcher);
        hvBmBoms = bomRepository.findAll(example, bomQueryDTO.getRequest());
        Page<BomDTO> dtos = DtoMapper.convertPage(hvBmBoms, BomDTO.class);
        List<ExtendInfo> extendInfos = bomExtendService.getExtend(hvBmBoms.stream().map(SysBase::getId).collect(Collectors.toList()));
        //内存中拼接设备基础信息和扩展信息
        joinDTOWithExtend(dtos, extendInfos);
        //获取计量单位ID列表。查询计量单位信息
        List<Integer> unitIds = dtos.stream().map(BomDTO::getUnitId).collect(Collectors.toList());
        List<HvBmUnit> hvBmUnits = unitRepository.getAllByIdIn(unitIds);
        //获取bom 物料关联关系ID列表 查询关联关系信息
        List<Integer> bomMaterialsIdList = dtos.stream().map(SysBaseDTO::getId).collect(Collectors.toList());
        List<HvBmBomMaterial> bmBomMaterials = bomMaterialRepository.getAllByBomIdIn(bomMaterialsIdList);
        for (BomDTO bomDTO : dtos) {
            //查找计量单位数据 如果存在 执行SET方法 如果不存在 跳过
            Optional<HvBmUnit> hvBmUnit = hvBmUnits.stream().filter(t -> t.getId().equals(bomDTO.getUnitId())).findFirst();
            hvBmUnit.ifPresent(hvBmUnit1 -> bomDTO.setSymbol(hvBmUnit1.getSymbol()));
            Optional<HvBmBomMaterial> hvBmBomMaterial = bmBomMaterials.stream().filter(t -> t.getBomId().equals(bomDTO.getId())).findFirst();
            hvBmBomMaterial.ifPresent(hbo -> bomDTO.setMaterialsId(hbo.getMaterialId()));
            if (hvBmBomMaterial.isPresent()) {
                Optional<HvBmMaterial> material = materialRepository.findById(hvBmBomMaterial.get().getMaterialId());
                if (material.isPresent()) {
                    bomDTO.setMaterialName(material.get().getMaterialName());
                    bomDTO.setMaterialCode(material.get().getMaterialCode());
                    bomDTO.setEigenvalue(material.get().getEigenvalue());
                }
            }
        }
        return dtos;
    }

    /**
     * 导出bom信息
     *
     * @return excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    @ApiIgnore
    public ResultVO<ExcelExportDto> exportBomAndBomItemAndSubstituteItem() throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(exportBomAll().getBody());
        excelExportDto.setFileName(BomConst.BOM_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * 导出选中的BOM信息（根据ID列表导出）
     *
     * @param idList bomId列表
     * @return excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResultVO<ExcelExportDto> exportBomAllByIdList(List<Integer> idList) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(exportBomByIdList(idList).getBody());
        excelExportDto.setFileName(BomConst.BOM_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);

    }


    /**
     * 导出bom信息（支持超链接）
     *
     * @return excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    @ApiIgnore
    public ResponseEntity<byte[]> exportBomAll() throws IOException, IllegalAccessException {
        //获取要导入的bom信息
        List<ExportBomDTO> bomDTOS = DtoMapper.convertList(bomRepository.findAll(), ExportBomDTO.class);
        List<ExtendInfo> extendInfos = bomExtendService.getAll();

        //item
        joinExportDTOWithExtend(bomDTOS, extendInfos);
        List<BomItemIncreaseDTO> bomItemIncreaseDTOS = DtoMapper.convertList(bomItemRepository.findAll(), BomItemIncreaseDTO.class);
        List<ExtendInfo> extendInfos1 = bomItemExtendService.getAll();

        //sub
        joinBomItemsIncreaseDTOWithExtend(bomItemIncreaseDTOS, extendInfos1);
        List<SubstituteItemDTO> substituteItemDTOS = DtoMapper.convertList(substituteItemRepository.findAll(), SubstituteItemDTO.class);


        HSSFWorkbook sheets = setHSSFWorkbook(substituteItemDTOS, bomDTOS, bomItemIncreaseDTOS);
        return ExcelUtil.generateHttpExcelFile(sheets, BomConst.BOM_EXPORT_FILE_NAME);
    }


    /**
     * 根据ID列表导出BOM信息 （支持超链接）
     *
     * @param idList bomId列表
     * @return excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResponseEntity<byte[]> exportBomByIdList(List<Integer> idList) throws IOException, IllegalAccessException {
        //根据ID列表查出BOM,Item sub信息
        if (idList.size() < 1) {
            throw new BaseKnownException(MaterialExceptionEnum.CHOOSE_BOM);
        }
        List<HvBmBom> allByIdIn = bomRepository.getAllByIdIn(idList);
        List<ExportBomDTO> bomDTOS = DtoMapper.convertList(allByIdIn, ExportBomDTO.class);
        List<ExtendInfo> extendInfos = bomExtendService.getAll();
        joinExportDTOWithExtend(bomDTOS, extendInfos);
        //根据BOM Item Sub 获取扩展字段信息
        List<BomItemIncreaseDTO> bomItemIncreaseDTOS = DtoMapper
                .convertList(bomItemRepository.getAllByBomIdIn(idList), BomItemIncreaseDTO.class);
        List<ExtendInfo> extendInfos1 = bomItemExtendService.getAll();
        joinBomItemsIncreaseDTOWithExtend(bomItemIncreaseDTOS, extendInfos1);
        List<Integer> bomItemId = bomItemIncreaseDTOS.stream().map(SysBaseDTO::getId).collect(Collectors.toList());
        List<SubstituteItemDTO> substituteItemDTOS = DtoMapper
                .convertList(substituteItemRepository.getAllByBomItemIdIn(bomItemId), SubstituteItemDTO.class);

        HSSFWorkbook hssfWorkbook = this.setHSSFWorkbook(substituteItemDTOS, bomDTOS, bomItemIncreaseDTOS);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, BomConst.BOM_EXPORT_FILE_NAME);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    @ApiIgnore
    public ResponseEntity<byte[]> getBomImportTemplate() throws IOException, IllegalAccessException {
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        List<BomDTO> bomDTOS = new ArrayList<>();
        List<BomItemIncreaseDTO> bomItemIncreaseDTOS = new ArrayList<>();
        List<SubstituteItemDTO> substituteItemDTOS = new ArrayList<>();
        //添加Sheet页
        ExcelUtil.addSheetToWorkBook(bomDTOS, BomConst.BOM_EXPORT_SHEET_NAME, ExportBomDTO.class, bomExtendService.getExtendColumnInfo(), hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(bomItemIncreaseDTOS, BomConst.BOM_ITEM_EXPORT_SHEET_NAME, BomItemIncreaseDTO.class, bomItemExtendService.getExtendColumnInfo(), hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(substituteItemDTOS, BomConst.SUBSTITUTE_ITEM_EXPORT_SHEET_NAME, SubstituteItemDTO.class, substituteItemExtendService.getExtendColumnInfo(), hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, BomConst.BOM_EXPORT_FILE_NAME);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(getBomImportTemplate().getBody());
        excelExportDto.setFileName(BomConst.BOM_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<ImportResult> importBom(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        List<ImportResult> importResults = new ArrayList<>();
        importResults.add(ExcelUtil.importEntity(file, 0, BomDTO.class, this, bomExtendService.getExtendColumnInfo()));
        importResults.add(ExcelUtil.importEntity(file, 1, BomItemIncreaseDTO.class, bomItemService, bomItemExtendService.getExtendColumnInfo()));
        importResults.add(ExcelUtil.importEntity(file, 2, SubstituteItemDTO.class, substituteItemService,
                substituteItemExtendService.getExtendColumnInfo()));
        return importResults;
    }

    /**
     * 通过bomCode获取bom版本
     *
     * @param bomCode bomCode
     * @return 版本列表
     */
    @Override
    public List<String> getBomVersionsByBomCode(String bomCode) {
        return bomRepository.getAllBomByBomCode(bomCode).stream().map(HvBmBom::getBomVersions).collect(Collectors.toList());
    }

    /**
     * 获取生效bom
     *
     * @return 生效bom信息列表
     */
    @Override
    public List<BomDTO> getTakeEffectBom() {
        return DtoMapper.convertList(bomRepository.getByBomStatus(TAKEEFFECT), BomDTO.class);
    }

    /**
     * 获取基础bom信息
     *
     * @param bomCode    bom编码
     * @param bomVersion bom版本
     * @return 基础bom信息
     */
    @Override
    public BaseBomDTO getBaseBomDTOByCodeAndVersion(String bomCode, String bomVersion) {
        BaseBomDTO baseBomDTO =
                DtoMapper.convert(bomRepository.
                        findByBomCodeAndBomVersions(bomCode, bomVersion), BaseBomDTO.class);
        HvBmUnit hvBmUnit = unitRepository.getById(baseBomDTO.getUnitId());
        baseBomDTO.setSymbol(hvBmUnit.getSymbol());
        return baseBomDTO;
    }


    /**
     * 根据BomId查询所关联的BOm bomItem,substituteItem信息
     *
     * @param id bom id
     * @return bomAllDto bom下所有信息
     */
    @Override
    public BomAllDTO getBomBomItemSubstituteItemByBomId(Integer id) {
        //  获取bom信息， 转换DTO
        HvBmBom hvBmBom = bomRepository.getOne(id);
        BomAllDTO bomAllDTO = DtoMapper.convert(hvBmBom, BomAllDTO.class);
        // 如果BOM已经绑定了物料 查询的时候 插入MaterialID
        boolean existsByBomId = bomMaterialRepository.existsByBomId(id);
        if (existsByBomId) {
            HvBmBomMaterial hvBomMaterialByBomId = bomMaterialRepository.getHvBomMaterialByBomId(id);
            bomAllDTO.setMaterialsId(hvBomMaterialByBomId.getMaterialId());
            HvBmMaterial material = materialRepository.getOne(hvBomMaterialByBomId.getMaterialId());
            bomAllDTO.setMaterialCode(material.getMaterialCode());
            bomAllDTO.setEigenvalue(material.getEigenvalue());
            bomAllDTO.setMaterialName(material.getMaterialName());
        }
        //加入扩展属性
        Map<String, Object> extend = bomExtendService.getExtend(id);
        if (extend != null) {
            bomAllDTO.setExtend(extend);
        }
        bomAllDTO.setSymbol(unitRepository.getById(hvBmBom.getUnitId()).getSymbol());
        //获取bomItem信息 转换Dto 插入数据
        List<HvBmBomItem> hvBmBomItems = bomItemRepository.getByBomId(id);
        List<BomItemsAllDTO> bomItemsAllDTOS = DtoMapper.convertList(hvBmBomItems, BomItemsAllDTO.class);
        List<ExtendInfo> extendInfos = bomItemExtendService.getAll();
        //加入扩展属性
        joinBomItemsAllDTOWithExtend(bomItemsAllDTOS, extendInfos);
        bomAllDTO.setBomItemDTOS(bomItemsAllDTOS);
        //在bomItem加入bomCode bomVersions 信息
        for (BomItemsAllDTO bomItemIncreaseDTO : bomItemsAllDTOS) {
            HvBmMaterial material = materialRepository.getOne(bomItemIncreaseDTO.getMaterialsId());
            bomItemIncreaseDTO.setMaterialCode(material.getMaterialCode());
            MaterialDTO convert = DtoMapper.convert(material, MaterialDTO.class);
            convert.setUomName(unitRepository.getById(material.getUom()).getSymbol());
            bomItemIncreaseDTO.setMaterialDTO(convert);
            HvBmBom bom = bomRepository.getOne(bomItemIncreaseDTO.getBomId());
            bomItemIncreaseDTO.setBomCode(bom.getBomCode());
            bomItemIncreaseDTO.setBomVersions(bom.getBomVersions());
            bomItemIncreaseDTO.setMaterialName(material.getMaterialName());
        }
        //循环插入替代物料信息
        for (BomItemsAllDTO bomItemsAllDTO : bomItemsAllDTOS) {
            List<HvBmSubstituteItem> hvBmSubstituteItems = substituteItemRepository
                    .getAllByBomItemId(bomItemsAllDTO.getId());
            //在SDTO插入materialCode BomItemCode 信息
            List<SubstituteItemDTO> substituteItemDTOS = DtoMapper.convertList(hvBmSubstituteItems, SubstituteItemDTO.class);
            for (SubstituteItemDTO substituteItemDTO : substituteItemDTOS) {
                HvBmMaterial material = materialRepository.getOne(substituteItemDTO.getMaterialsId());
                substituteItemDTO.setMaterialCode(material.getMaterialCode());
                HvBmBomItem bomItem = bomItemRepository.getOne(substituteItemDTO.getBomItemId());
                substituteItemDTO.setBomItemCode(bomItem.getBomItemCode());
                substituteItemDTO.setMaterialName(material.getMaterialName());
                substituteItemDTO.setMaterialUnitName(unitRepository.getById(material.getUom()).getSymbol());
            }
            List<ExtendInfo> extendInfos1 = substituteItemExtendService.getAll();
            //加入扩展属性
            joinSubstituteItemDTOWithExtend(substituteItemDTOS, extendInfos1);
            bomItemsAllDTO.setSubstituteItemDTOS(substituteItemDTOS);

        }
        return bomAllDTO;


    }


    /**
     * bom复制功能
     *
     * @param id          bomId
     * @param bomVersions 版本
     * @return copy后的bomId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int copyBom(int id, String bomVersions) {
        //判断传入ID大于0
        if (id > NEWLY) {
            HvBmBom bmBom = bomRepository.getAllById(id);
            if (bmBom.getBomVersions().equals(bomVersions)) {
                throw new BaseKnownException(BOM_VERSIONS_EXISTS);
            }
            //判断bom是否存在
            if (bmBom != null) {
                HvBmBom bmBomNew = new HvBmBom();
                //转化成DTO
                BeanUtils.copyProperties(bmBom, bmBomNew);


                //初始化Id 状态
                bmBomNew.setId(NEWLY);
                //初始化状态
                bmBomNew.setBomStatus(NEWLY);
                //传入新的版本
                bmBomNew.setBomVersions(bomVersions);
                boolean unitIdExists = unitRepository.existsById(bmBomNew.getUnitId());
                if (!unitIdExists) {
                    throw new BaseKnownException(UNIT_ID_NOT_EXISTS);
                }
                //save方法存入复制的bom
                int newId = bomRepository.save(bmBomNew).getId();
                Map<String, Object> oldExtendInfo = bomExtendService.getExtend(id);
                addExtendInfo(oldExtendInfo, newId, bomExtendService);
                //获取新插入bom数据ID
                //通过bomId获取 bomItem
                List<HvBmBomItem> bmBomItem = bomItemRepository.findAllByBomId(id);
                if (bmBomItem != null) {
                    for (HvBmBomItem hvBmBomItem : bmBomItem) {
                        HvBmBomItem hvBmBomItem1 = new HvBmBomItem();
                        Map<String, Object> bomItemExtend = bomItemExtendService.getExtend(hvBmBomItem.getId());
                        BeanUtils.copyProperties(hvBmBomItem, hvBmBomItem1);
                        hvBmBomItem1.setId(NEWLY);
                        hvBmBomItem1.setBomId(newId);
                        int bomItemNewId = bomItemRepository.save(hvBmBomItem1).getId();
                        Utils.addExtendInfo(bomItemExtend, bomItemNewId, bomItemExtendService);
                        //通过之前sid获取下边所有数据
                        List<HvBmSubstituteItem> hvBmSubstituteItems = substituteItemRepository.getAllByBomItemId(hvBmBomItem.getId());
                        for (HvBmSubstituteItem substituteItem : hvBmSubstituteItems) {
                            if (hvBmSubstituteItems.size() > 0) {
                                HvBmSubstituteItem item = new HvBmSubstituteItem();
                                BeanUtils.copyProperties(substituteItem, item);
                                Map<String, Object> subItemExtend = substituteItemExtendService.getExtend(substituteItem.getId());
                                item.setId(NEWLY);
                                item.setBomItemId(bomItemNewId);
                                int subNewId = substituteItemRepository.save(item).getId();
                                Utils.addExtendInfo(subItemExtend, subNewId, substituteItemExtendService);

                            }
                        }
                    }
                }
                return newId;
            } else {
                throw new BaseKnownException(BOM_NOT_EXISTS);
            }
        } else {
            throw new BaseKnownException(BOM_CANNOT_BE_ZERO);
        }
    }

    /**
     * 新增或更新扩展属性
     *
     * @param bomDTO      bomdto
     * @param hvBmBom     bom实体类
     * @param hvBmBomlOld 要更新的ID
     * @param hvBmUnit    计量单位对象
     */
    private void createOrUpdateExtend(BomDTO bomDTO, HvBmBom hvBmBom, HvBmBom hvBmBomlOld, HvBmUnit hvBmUnit) {
        hvBmBom.setUnitId(hvBmUnit.getId());
        if (hvBmBomlOld != null) {
            hvBmBom.setId(hvBmBomlOld.getId());
            updateBom(DtoMapper.convert(hvBmBom, BomDTO.class));
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(hvBmBom.getId());
            extendInfo.setValues(bomDTO.getExtend());
            bomExtendService.updateExtendInfo(extendInfo);
        } else {
            int entityId = createBom(DtoMapper.convert(hvBmBom, BomDTO.class));
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(entityId);
            extendInfo.setValues(bomDTO.getExtend());
            bomExtendService.addExtendInfo(extendInfo);
        }
    }


    private void assessUom(BaseBomDTO bomDTO) {
        if (bomDTO.getUnitId() != null) {
            boolean unitIdExists = unitRepository.existsById(bomDTO.getUnitId());
            if (!unitIdExists) {
                throw new BaseKnownException(UNIT_ID_NOT_EXISTS);
            }
        }
    }

    private void SetMaterialAndBomItemMessage(List<SubstituteItemDTO> substituteItemDTOS, List<HvBmUnit> units) {
        for (SubstituteItemDTO substituteItemDTO : substituteItemDTOS) {

            //根据每个substituteItem的mateialID查询materialCode 插入DTO
            HvBmMaterial material = materialRepository.getOne(substituteItemDTO.getMaterialsId());

            substituteItemDTO.setMaterialCode(material.getMaterialCode());

            //查出来哦的bomitem信息
            HvBmBomItem hvBmBomItem = bomItemRepository.getOne(substituteItemDTO.getBomItemId());
            substituteItemDTO.setBomItemCode(hvBmBomItem.getBomItemCode());

            //查询bomItem的物料编码
            HvBmMaterial hvBmMaterial = materialRepository.getOne(hvBmBomItem.getMaterialsId());
            substituteItemDTO.setBomItemMaterialCode(hvBmMaterial.getMaterialCode());
            substituteItemDTO.setMaterialName(hvBmMaterial.getMaterialName());
            substituteItemDTO.setEigenvalue(hvBmMaterial.getEigenvalue());
            Optional<HvBmUnit> hvBmUnit = units.stream().filter(t -> t.getId().equals(hvBmMaterial.getUom())).findFirst();
            hvBmUnit.ifPresent(t -> substituteItemDTO.setMaterialUnitName(t.getSymbol()));
            //查询对应的bom
            HvBmBom hvBmBom = bomRepository.getOne(hvBmBomItem.getBomId());
            //添加数据
            substituteItemDTO.setBomCode(hvBmBom.getBomCode());
            substituteItemDTO.setBomVersions(hvBmBom.getBomVersions());


        }
    }

    private void SetBomMessage(List<BomItemIncreaseDTO> bomItemIncreaseDTOS, List<HvBmUnit> units) {
        for (BomItemIncreaseDTO bomItemIncreaseDTO : bomItemIncreaseDTOS) {

            //根据每个bomItem的mateialID查询materialCode 插入DTO
            HvBmMaterial material = materialRepository.getOne(bomItemIncreaseDTO.getMaterialsId());
            bomItemIncreaseDTO.setMaterialName(material.getMaterialName());
            bomItemIncreaseDTO.setMaterialCode(material.getMaterialCode());
            bomItemIncreaseDTO.setEigenvalue(material.getEigenvalue());
            Optional<HvBmUnit> hvBmUnit = units.stream().filter(t -> t.getId().equals(material.getUom())).findFirst();
            hvBmUnit.ifPresent(t -> bomItemIncreaseDTO.setMaterialUnitName(t.getSymbol()));

            //根据每个bomItem的bomID查询BomCode ，BomVersions 插入DTO
            HvBmBom bom = bomRepository.getOne(bomItemIncreaseDTO.getBomId());
            bomItemIncreaseDTO.setBomCode(bom.getBomCode());
            bomItemIncreaseDTO.setBomVersions(bom.getBomVersions());


        }
    }

    /**
     * bom导出 创建HssfWorkBook
     *
     * @param substituteItemDTOS  替代料对象
     * @param bomDTOS             bom对象列表
     * @param bomItemIncreaseDTOS item对象列表
     * @return ssfWorkBook
     * @throws IllegalAccessException
     */
    private HSSFWorkbook setHSSFWorkbook(List<SubstituteItemDTO> substituteItemDTOS, List<ExportBomDTO> bomDTOS,
                                         List<BomItemIncreaseDTO> bomItemIncreaseDTOS) throws IllegalAccessException {
        List<ExtendInfo> extendInfos2 = substituteItemExtendService.getAll();
        joinSubstituteItemDTOWithExtend(substituteItemDTOS, extendInfos2);
        //传入计量单位符号信息
        List<HvBmUnit> hvBmUnits = unitRepository.findAll();
        for (ExportBomDTO bomDTO : bomDTOS
        ) {
            Optional<HvBmUnit> unit = hvBmUnits.stream().filter(t -> t.getId().equals(bomDTO.getUnitId())).findFirst();
            unit.ifPresent(hvBmUnit -> bomDTO.setSymbol(hvBmUnit.getSymbol()));

        }
        //传入bomCode,versions 信息

        SetBomMessage(bomItemIncreaseDTOS, hvBmUnits);
        SetMaterialAndBomItemMessage(substituteItemDTOS, hvBmUnits);
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        //每页导入信息
        ExcelUtil.addSheetToWorkBook(bomDTOS, BomConst.BOM_EXPORT_SHEET_NAME, ExportBomDTO.class, bomExtendService.getExtendColumnInfo(), hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(bomItemIncreaseDTOS, BomConst.BOM_ITEM_EXPORT_SHEET_NAME, BomItemIncreaseDTO.class, bomItemExtendService.getExtendColumnInfo(), hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(substituteItemDTOS, BomConst.SUBSTITUTE_ITEM_EXPORT_SHEET_NAME, SubstituteItemDTO.class, substituteItemExtendService.getExtendColumnInfo(), hssfWorkbook);

        return hssfWorkbook;
    }

    /**
     * dto拼接到
     *
     * @param bomDtos     dto列表
     * @param extendInfos 扩展信息列表
     */
    private void joinDTOWithExtend(Iterable<BomDTO> bomDtos, Iterable<ExtendInfo> extendInfos) {
        for (BomDTO bomDTO : bomDtos) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (bomDTO.getId() == extendInfo.getEntityId()) {
                    bomDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }


    private void joinExportDTOWithExtend(Iterable<ExportBomDTO> bomDtos, Iterable<ExtendInfo> extendInfos) {
        for (ExportBomDTO bomDTO : bomDtos) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (bomDTO.getId() == extendInfo.getEntityId()) {
                    bomDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }

    /**
     * dto拼接到
     *
     * @param bomDtos     dto列表
     * @param extendInfos 扩展信息列表
     */
    private void joinBomItemsAllDTOWithExtend(Iterable<BomItemsAllDTO> bomDtos, Iterable<ExtendInfo> extendInfos) {
        for (BomItemsAllDTO bomItemsAllDTO : bomDtos) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (bomItemsAllDTO.getId() == extendInfo.getEntityId()) {
                    bomItemsAllDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }

    /**
     * dto拼接到
     *
     * @param bomDtos     dto列表
     * @param extendInfos 扩展信息列表
     */
    private void joinSubstituteItemDTOWithExtend(Iterable<SubstituteItemDTO> bomDtos, Iterable<ExtendInfo> extendInfos) {
        for (SubstituteItemDTO substituteItemDTO : bomDtos) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (substituteItemDTO.getId() == extendInfo.getEntityId()) {
                    substituteItemDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }


    /**
     * dto拼接到
     *
     * @param bomItemDTOS dto列表
     * @param extendInfos 扩展信息列表
     */
    private void joinBomItemsIncreaseDTOWithExtend(Iterable<BomItemIncreaseDTO> bomItemDTOS, Iterable<ExtendInfo> extendInfos) {
        for (BomItemIncreaseDTO bomItemIncreaseDTO : bomItemDTOS) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (bomItemIncreaseDTO.getId() == extendInfo.getEntityId()) {
                    bomItemIncreaseDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }

}
