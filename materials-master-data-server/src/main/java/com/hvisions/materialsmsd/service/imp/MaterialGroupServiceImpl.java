package com.hvisions.materialsmsd.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.service.imp.BaseServiceImpl;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.materials.dto.MaterialGroupDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterialGroup;
import com.hvisions.materialsmsd.materials.enums.MaterialExceptionEnum;
import com.hvisions.materialsmsd.repository.MaterialRepository;
import com.hvisions.materialsmsd.repository.MaterialGroupRepository;
import com.hvisions.materialsmsd.service.MaterialGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: MaterialTypeServiceImp</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/4</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class MaterialGroupServiceImpl extends BaseServiceImpl<HvBmMaterialGroup, Integer> implements MaterialGroupService {
    private final MaterialGroupRepository materialGroupRepository;
    private final MaterialRepository materialRepository;

    @Autowired
    public MaterialGroupServiceImpl(MaterialGroupRepository materialGroupRepository, MaterialRepository materialRepository) {
        super(materialGroupRepository);
        this.materialGroupRepository = materialGroupRepository;
        this.materialRepository = materialRepository;
    }


    @Override
    public void saveOrUpdate(HvBmMaterialGroup hvBmMaterialGroup) {

    }


    /**
     * 根据code desc 查询物料分组信息信息
     *
     * @return 物料分组信息
     */
    @Override
    public Page<MaterialGroupDTO> findMaterialGroupByCodeOrDesc(MaterialGroupDTO materialGroupDTO) {

        //分页信息
        Page<HvBmMaterialGroup> hvBmMaterialTypes;
        //转换DTO
        HvBmMaterialGroup hvBmMaterialGroup = DtoMapper.convert(materialGroupDTO, HvBmMaterialGroup.class);
        //查询
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("groupCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("groupName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        Example<HvBmMaterialGroup> example = Example.of(hvBmMaterialGroup, exampleMatcher);
        //根据materialQueryDTO传入的参数 查询所有 进行匹配
        hvBmMaterialTypes = materialGroupRepository.findAll(example, materialGroupDTO.getRequest());
        Page<MaterialGroupDTO> groups = DtoMapper.convertPage(hvBmMaterialTypes, materialGroupDTO.getRequest(),
                MaterialGroupDTO.class);
        return groups;
    }

    /**
     * 根据ID删除物料类型
     *
     * @param id 物料类型ID
     */
    @Override
    public void deleteById(int id) {
        boolean exists = materialRepository.existsByMaterialGroup(id);
        if (exists) {
            throw new BaseKnownException(MaterialExceptionEnum.MATERIAL_GROUP_IS_USE);
        }
        materialGroupRepository.deleteById(id);
    }

    /**
     * 根据Id列表删除物料分组
     *
     * @param idList Id列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIdIn(List<Integer> idList) {
        materialGroupRepository.deleteByIdIn(idList);
    }


    /**
     * 通过ID查询物料类型信息
     *
     * @param id 物料类型ID
     * @return 物料类型信息
     */
    @Override
    public MaterialGroupDTO getAllMaterialGroupById(int id) {
        return DtoMapper.convert(materialGroupRepository.getOne(id), MaterialGroupDTO.class);
    }
}
