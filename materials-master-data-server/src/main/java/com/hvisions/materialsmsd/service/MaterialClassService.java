package com.hvisions.materialsmsd.service;

import com.hvisions.materialsmsd.materials.classdto.MaterialClassDTO;
import com.hvisions.materialsmsd.materials.classdto.MaterialClassQuery;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: MaterialClassService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface MaterialClassService {

    /**
     * 创建物料属性类型
     *
     * @param classDTO 属性类型
     * @return 主键
     */
    Integer create(MaterialClassDTO classDTO);

    /**
     * 修改物料属性类型
     *
     * @param classDTO 属性类型
     */
    void update(MaterialClassDTO classDTO);

    /**
     * 获取物料属性类型
     *
     * @param id 属性类型主键
     * @return 属性类型
     */
    MaterialClassDTO findById(Integer id);

    /**
     * 获取物料属性类型
     *
     * @param code 属性类型编码
     * @return 属性类型
     */
    MaterialClassDTO findByCode(String code);

    /**
     * 获取物料属性类型分页数据
     *
     * @param query 属性类型编码
     * @return 属性类型分页数据
     */
    Page<MaterialClassDTO> findPage(MaterialClassQuery query);

    /**
     * 删除物料属性类型
     *
     * @param id 属性类型
     */
    void deleteById(Integer id);

    /**
     * 根据物料id查询物料属性列表
     *
     * @param id 物料di
     * @return 物料属性类型列表
     */
    List<MaterialClassDTO> findByMaterialId(Integer id);

    /**
     * 向物料添加物料属性类型
     *
     * @param materialId 物料di
     * @param classId     属性类型id
     */
    void addClassToMaterial(Integer materialId, Integer classId);

    /**
     * 删除物料的物料属性类型
     *
     * @param materialId 物料di
     * @param classId     属性类型id
     */
    void removeClassToMaterial(Integer materialId, Integer classId);
}