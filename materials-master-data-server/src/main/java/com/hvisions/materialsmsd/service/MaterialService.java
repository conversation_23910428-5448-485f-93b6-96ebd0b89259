package com.hvisions.materialsmsd.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.materialsmsd.bom.dto.BomMaterialDTO;
import com.hvisions.materialsmsd.materials.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: MaterialService</p >
 * <p>Description: 物料扩展属性</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/20</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface MaterialService extends EntitySaver<MaterialDTO> {

    /**
     * 获取所有物料信息
     *
     * @return 物料信息
     */
    List<BaseMaterialDTO> getAllMaterials();

    /**
     * 添加物料信息及扩展属性
     *
     * @param materialDTO 传入Dto对象
     * @return 物料信息ID
     */
    int createMaterial(BaseMaterialDTO materialDTO);

    /**
     * 修改物料信息及扩展属性
     *
     * @param materialDTO 传入Dto对象
     * @return materialType id
     */

    int updateMaterial(BaseMaterialDTO materialDTO);

    /**
     * 根据ID查询material
     *
     * @param id 物料ID
     * @return 物料信息
     */
    MaterialDTO getMaterialById(Integer id);


    /**
     * 根据物料编码和特征值查询物料ID
     *
     * @param materialCode 物料编码
     * @param eigenvalue   特征值
     * @return 物料ID
     */
    Integer getMaterialIdByMaterialCode(String materialCode, String eigenvalue);

    /**
     * 根据物料编码和特征值查询物料
     *
     * @param materialCode 物料编码
     * @param eigenvalue   特征值
     * @return 物料信息
     */
    MaterialDTO getMaterialByMaterialCode(String materialCode, String eigenvalue);


    /**
     * 根据code name 模糊查询
     *
     * @param materialQueryDTO 查询条件对象
     * @return 分页信息
     */
    Page<MaterialDTO> findByCodeOrName(MaterialQueryDTO materialQueryDTO);

    /**
     * 分页查询
     *
     * @param materialQueryDTO 查询条件对象
     * @return 分页信息
     */
    Page<MaterialDTO> findByQuery(MaterialQueryDTO materialQueryDTO);

    /**
     * 根据code name 模糊查询
     *
     * @param materialNameCodeDTO 查询条件对象
     * @return 分页信息
     */
    Page<MaterialDTO> getAllByCodeOrName(MaterialNameCodeDTO materialNameCodeDTO);

    /**
     * 导出所有物料信息
     *
     * @return 物料信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    ResultVO<ExcelExportDto> exportMaterials() throws IOException, IllegalAccessException;


    /**
     * 导出所有物料信息
     *
     * @return 物料信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResponseEntity<byte[]> export() throws IOException, IllegalAccessException;

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    ResultVO<ExcelExportDto> getMaterialsImportTemplate() throws IOException, IllegalAccessException;

    /**
     * 导入物料信息
     *
     * @param file bom信息文档
     * @return 返回信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importMaterials(MultipartFile file) throws IllegalAccessException, ParseException, IOException;

    /**
     * 根据ID删除物料信息
     *
     * @param id 物料ID
     */
    void deleteMaterialById(Integer id);

    /**
     * 绑定bom与Material关联关系
     *
     * @param bomMaterialDTO dto
     * @return id
     */
    Integer createBomMaterial(BomMaterialDTO bomMaterialDTO);

    /**
     * 根据ID列表查询material
     *
     * @param list id列表
     * @return materials列表
     */
    List<MaterialDTO> getMaterialsByIdList(List<Integer> list);

    /**
     * 根据物料编码模糊查询 物料特征值物料编码 以及拼接字符串
     *
     * @param materialCodeQueryDTO 物料编码和物料特征值以及拼接字符串dto
     * @return 物料编码特征值信息列表
     */
    List<MaterialDTO> getHvBmMaterialByMaterialCodeLike(MaterialCodeQueryDTO materialCodeQueryDTO);

    /**
     * 删除物料BOM关联关系
     *
     * @param materialId 物料ID
     */
    void deleteBomMaterialByMaterialId(int materialId);


    /**
     * 获取所有成品 半成品物料信息
     *
     * @param materialQueryDTO 查询条件
     * @return 物料信息
     */
    Page<MaterialDTO> getMaterial(QueryDTO materialQueryDTO);

    /**
     * 获取所有成品 半成品物料信息
     *
     * @return 物料信息
     */
    List<MaterialDTO> getAllProductAndByProduct();


    /**
     * 根据物料类型查询物料
     *
     * @param typeId 物料类型id
     * @return 物料信息
     */
    List<MaterialDTO> getMaterialByTypeId(int typeId);

    /**
     * 根据批次号查询对应的物料信息
     *
     * @param batchNum 批次号
     * @return 物料信息
     */
    MaterialDTO findMaterialByBatch(String batchNum);

    /**
     * 更新转换设置
     *
     * @param parseSettingDTO 批次号
     */
    void updateSetting(ParseSettingDTO parseSettingDTO);


    /**
     * 查询转换设置
     *
     * @return 转换设置"
     */
    ParseSettingDTO getSetting();

    /**
     * 启动时检查是否存在没有配置rootTypeId的物料。如果有则补充相关的信息
     */
    void updateMaterialRootType();

    ResultVO<Integer> materialPushDataSaveAndUpdateAndDelete(MDMPushMaterialDTO mdmPushMaterialDTO);

    ResultVO<Integer> rowMaterialPushDataSaveAndUpdateAndDelete(MDMPushRowMaterialDTO mdmPushMaterialDTO);
}
