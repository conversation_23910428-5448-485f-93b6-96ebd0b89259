package com.hvisions.materialsmsd.service;

import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.service.BaseService;
import com.hvisions.materialsmsd.materials.dto.MaterialGroupDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterialGroup;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: MaterialGroupService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/4</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface MaterialGroupService extends BaseService<HvBmMaterialGroup, Integer>, EntitySaver<HvBmMaterialGroup> {
    /**
     * 通过ID查询物料分组信息
     *
     * @param id 物料分组ID
     * @return 物料分组信息
     */
    MaterialGroupDTO getAllMaterialGroupById(int id);

    /**
     * 根据code desc 查询物料分组信息
     *
     * @param materialGroupDTO 分组描述DTO
     * @return 物料类型信息
     */
    Page<MaterialGroupDTO> findMaterialGroupByCodeOrDesc(MaterialGroupDTO materialGroupDTO);

    /**
     * 根据ID删除物料分组
     *
     * @param id 物料分组ID
     */
    void deleteById(int id);

    /**
     * 根据Id列表删除物料分组
     *
     * @param idList Id列表
     */
    void deleteByIdIn(List<Integer> idList);

}
