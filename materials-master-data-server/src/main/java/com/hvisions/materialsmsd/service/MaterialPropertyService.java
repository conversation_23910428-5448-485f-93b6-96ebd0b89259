package com.hvisions.materialsmsd.service;

import com.hvisions.materialsmsd.materials.classdto.AddMaterialPropertyDTO;
import com.hvisions.materialsmsd.materials.classdto.MaterialClassPropertyDTO;
import com.hvisions.materialsmsd.materials.classdto.MaterialPropertyDTO;

import java.util.List;

/**
 * <p>Title: MaterialPropertyService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface MaterialPropertyService {

    /**
     * 修改物料属性
     *
     * @param propertyDTOs 属性
     */
    void update(List<MaterialPropertyDTO> propertyDTOs);

    /**
     * 获取物料属性
     *
     * @param id 物料id
     * @return 物料属性列表
     */
    List<MaterialPropertyDTO> findByMaterialId(Integer id);


    /**
     * 添加物料属性
     *
     * @param propertyDTO 物料属性
     */
    void addPropertyToMaterials(AddMaterialPropertyDTO propertyDTO);

    /**
     * 根据Id删除物料属性
     * @param id 物料属性id
     */
    void deletePropertyById(int id);
}