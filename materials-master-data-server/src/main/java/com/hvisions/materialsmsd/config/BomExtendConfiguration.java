package com.hvisions.materialsmsd.config;

import com.hvisions.common.dto.ExtendInfoParam;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.SqlFactoryUtil;
import com.hvisions.materialsmsd.consts.BomConst;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: BomExtendConfiguration</p >
 * <p>Description: 扩展</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Configuration
public class BomExtendConfiguration {
    /**
     * @return 获取扩展服务工厂对象
     */
    @Bean
    SqlFactoryUtil getSqlFactory() {
        return new SqlFactoryUtil();
    }

    @Bean(value = "bom_extend")
    BaseExtendService getBomExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(BomConst.BOM_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(BomConst.BOM_EXTEND_ID);
        extendInfoParam.setExtendTableName(BomConst.BOM_EXTEND_TABLE_NAME);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }


    @Bean(value = "bom_item_extend")
    BaseExtendService getBomItemExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(BomConst.BOM_ITEM_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(BomConst.BOM_ITEM_EXTEND_ID);
        extendInfoParam.setExtendTableName(BomConst.BOM_ITEM_EXTEND_TABLE_NAME);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }


    @Bean(value = "substitute_item_extend")
    BaseExtendService getSubstituteItemExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(BomConst.SUBSTITUTE_ITEM_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(BomConst.SUBSTITUTE_ITEM_EXTEND_ID);
        extendInfoParam.setExtendTableName(BomConst.SUBSTITUTE_ITEM_EXTEND_TABLE_NAME);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }
}
