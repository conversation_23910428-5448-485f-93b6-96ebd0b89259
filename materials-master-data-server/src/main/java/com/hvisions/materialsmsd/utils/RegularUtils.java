package com.hvisions.materialsmsd.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>Title: RegularUtils</p >
 * <p>Description: 正则匹配解析</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/13</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public class RegularUtils {
    /**
     * 匹配是否符合规则.解析提取字符串
     *
     * @param batchNum         传入字符串
     * @param materialBatchNum 物料批次号
     * @return 提取出字符串列表
     */
    public static Map<String, String> matching(String materialBatchNum, String batchNum) {
        Map<String, String> map = new HashMap<>();
        //编译此正则表达式regExp，返回regExp被编译后的pattern
        Pattern pattern = Pattern.compile(materialBatchNum);
        //获得一个Matcher对象 通过此对象 对字符串进行操作
        Matcher matcher = pattern.matcher(batchNum);
        //解析字符串
        if (matcher.find()) {
            try {
                map.put("code", matcher.group("code"));
            } catch (IllegalArgumentException ignored) {
            }
            try {
                map.put("id", matcher.group("id"));
            } catch (IllegalArgumentException ignored) {
            }
            try {
                map.put("eigenvalue", matcher.group("eigenvalue"));
            } catch (IllegalArgumentException ignored) {
            }
        }
        return map;
    }


}
