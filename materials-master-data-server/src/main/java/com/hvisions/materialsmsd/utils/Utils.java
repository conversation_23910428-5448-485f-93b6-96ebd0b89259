package com.hvisions.materialsmsd.utils;

import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.service.BaseExtendService;

import java.util.Map;

/**
 * <p>Title: Utils</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-08-16</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public class Utils {


    /**
     * 添加扩展属性
     *
     * @param map               扩展属性
     * @param id                主键ID
     * @param baseExtendService extendservice
     */
    public static void addExtendInfo(Map<String, Object> map, int id, BaseExtendService baseExtendService) {
        if (map != null) {
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setValues(map);
            extendInfo.setEntityId(id);
            baseExtendService.addExtendInfo(extendInfo);
        }
    }

    /**
     * 更新扩展属性
     *
     * @param map               扩展属性
     * @param id                主键ID
     * @param baseExtendService extendservice
     */
    public static void updateExtendInfo(Map<String, Object> map, int id, BaseExtendService baseExtendService) {
        if (map != null) {
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setValues(map);
            extendInfo.setEntityId(id);
            baseExtendService.updateExtendInfo(extendInfo);
        }
    }
}