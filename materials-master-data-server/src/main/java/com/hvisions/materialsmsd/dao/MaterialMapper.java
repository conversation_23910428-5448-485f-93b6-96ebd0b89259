package com.hvisions.materialsmsd.dao;

import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.materials.dto.MaterialQueryDTO;
import com.hvisions.materialsmsd.materials.dto.QueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>Title: MaterialMapper</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/16</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Mapper
public interface MaterialMapper {


    List<MaterialDTO> getMaterialByQuery(@Param("dto") MaterialQueryDTO materialQueryDTO);

    /**
     * 查询物料列表
     *
     * @param queryDTO 查询条件
     * @return 物料列表
     */
    List<MaterialDTO> getMaterials(@Param(value = "query") QueryDTO queryDTO);
}