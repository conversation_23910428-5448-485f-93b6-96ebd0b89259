package com.hvisions.materialsmsd;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <p>Title: MaterialApplication</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/27</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.hvisions.materialsmsd.dao")
@EnableFeignClients(basePackages = {"com.hvisions.log.capture.client"})
@ComponentScan(value = {"com.hvisions.materialsmsd", "com.hvisions.log.capture.client"})
@EnableAsync
public class MaterialApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(MaterialApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(MaterialApplication.class);
    }


}
