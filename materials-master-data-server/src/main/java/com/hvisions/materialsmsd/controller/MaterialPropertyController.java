package com.hvisions.materialsmsd.controller;

import com.hvisions.materialsmsd.materials.classdto.AddMaterialPropertyDTO;
import com.hvisions.materialsmsd.materials.classdto.MaterialPropertyDTO;
import com.hvisions.materialsmsd.service.MaterialPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: MaterialPropertyController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/8</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/material_property")
@Api(description = "")
public class MaterialPropertyController {

    @Autowired
    MaterialPropertyService materialPropertyService;

    /**
     * 修改物料属性
     *
     * @param propertyDTOs 属性
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "修改物料属性")
    public void update(@Valid @RequestBody List<MaterialPropertyDTO> propertyDTOs) {
        materialPropertyService.update(propertyDTOs);
    }

    /**
     * 获取物料属性
     *
     * @param id 物料id
     * @return 物料属性列表
     */
    @GetMapping(value = "/findByMaterialId/{id}")
    @ApiOperation(value = "根据物料ID获取物料属性")
    public List<MaterialPropertyDTO> findByMaterialId(@PathVariable Integer id) {
        return materialPropertyService.findByMaterialId(id);
    }

    /**
     * 添加物料属性
     *
     * @param propertyDTO 物料属性
     */
    @ApiOperation(value = "添加物料属性")
    @PostMapping(value = "/addPropertyToMaterials")
    public void addPropertyToMaterials(@RequestBody AddMaterialPropertyDTO propertyDTO) {
        materialPropertyService.addPropertyToMaterials(propertyDTO);
    }

    /**
     * 根据Id删除物料属性
     *
     * @param id 物料属性ID
     */
    @DeleteMapping(value = "/deletePropertyById/{id}")
    @ApiOperation(value = "根据物料属性Id删除物料属性")
    public void deletePropertyById(@PathVariable int id) {
        materialPropertyService.deletePropertyById(id);
    }
}