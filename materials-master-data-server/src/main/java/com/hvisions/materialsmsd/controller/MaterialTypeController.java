package com.hvisions.materialsmsd.controller;

import com.hvisions.materialsmsd.materials.dto.MaterialTypeDTO;
import com.hvisions.materialsmsd.materials.dto.MaterialTypeQueryDTO;
import com.hvisions.materialsmsd.service.MaterialTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: MaterialTypeController</p >
 * <p>Description: 物料类型维护控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-06</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/materialType")
@Slf4j
@Api(description = "物料类型")
public class MaterialTypeController {


    private final MaterialTypeService materialTypeService;

    @Autowired
    public MaterialTypeController(MaterialTypeService materialTypeService) {
        this.materialTypeService = materialTypeService;
    }


    /**
     * 创建物料类型
     *
     * @param materialTypeDTO 物料类型对象
     * @return 物料类型对象
     */
    @ApiOperation(value = "创建物料类型")
    @PostMapping(value = "/createMaterialType")
    public MaterialTypeDTO createMaterialType(@Valid @RequestBody MaterialTypeDTO materialTypeDTO) {
        return materialTypeService.createMaterialType(materialTypeDTO);
    }

    /**
     * 更新物料类型
     *
     * @param materialTypeDTO 物料类型对象
     * @return 物料类型对象
     */
    @ApiOperation(value = "更新物料类型")
    @PutMapping(value = "/updateMaterialType")
    public MaterialTypeDTO updateMaterialType(@Valid @RequestBody MaterialTypeDTO materialTypeDTO) {
        return materialTypeService.updateMaterialType(materialTypeDTO);
    }

    /**
     * 根据物料类型ID删除物料类型
     *
     * @param id 物料类型ID
     */
    @ApiOperation(value = "根据物料类型ID删除物料类型")
    @DeleteMapping(value = "/deleteTypeById/{id}")
    public void deleteTypeById(@PathVariable int id) {
        materialTypeService.deleteTypeById(id);
    }

    /**
     * 获取根结点物料类型
     *
     * @return 物料类型信息列表
     */
    @GetMapping(value = "/getRootMaterialType")
    @ApiOperation(value = "获取根结点物料类型")
    public List<MaterialTypeDTO> getRootMaterialType() {
        return materialTypeService.getRootMaterialType();
    }


    /**
     * 获取所有物料类型
     *
     * @return 物料类型信息列表
     */
    @GetMapping(value = "/getAllMaterialType")
    @ApiOperation(value = "获取所有物料类型")
    public List<MaterialTypeDTO> getAllMaterialType() {
        return materialTypeService.findAll();
    }

    /**
     * 根据物料类型父级别ID查询
     *
     * @param parentId 父级ID
     * @return 物料类型信息列表
     */
    @GetMapping(value = "/getMaterialTypeByParentId/{parentId}")
    @ApiOperation(value = "根据物料类型父级别ID查询")
    public List<MaterialTypeDTO> getMaterialTypeByParentId(@PathVariable int parentId) {
        return materialTypeService.getMaterialTypeByParentId(parentId);
    }

    /**
     * 分页查询物料类型
     *
     * @param materialTypeQueryDTO 物料类型查询条件
     * @return 物料类型分页信息
     */
    @ApiOperation(value = "分页查询物料类型")
    @PostMapping(value = "/getMaterialTypeByQuery")
    public Page<MaterialTypeDTO> getMaterialTypeByQuery(@RequestBody MaterialTypeQueryDTO materialTypeQueryDTO) {
        return materialTypeService.getMaterialTypeByQuery(materialTypeQueryDTO);
    }


    /**
     * 根据子级节点获取上级id
     *
     * @param materialTypeId 物料类型ID
     * @return id列表
     */
    @ApiOperation(value = "根据子级节点获取上级id")
    @GetMapping(value = "/getParentIdBySonId/{materialTypeId}")
    public List<Integer> getParentIdBySonId(@PathVariable int materialTypeId) {
        return materialTypeService.getParentIdBySonId(materialTypeId);
    }

    /**
     * 获取产品副产品对象信息
     *
     * @return 物料类型列表
     */
    @GetMapping(value = "/getProduct")
    @ApiOperation(value = "获取产品，副产品对象信息")
    @Deprecated
    public List<MaterialTypeDTO> getProduct() {
        return materialTypeService.getProduct();
    }

}