package com.hvisions.materialsmsd.controller;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.materials.dto.BaseMaterialDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterial;
import com.hvisions.materialsmsd.service.BaseMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>Title: DemoEntityController</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/materials")
@Slf4j
@Deprecated
@Api(description = "物料接口(增删改查)")
public class BaseMaterialController {
    private final BaseMaterialService baseMaterialService;


    @Autowired
    public BaseMaterialController(BaseMaterialService baseMaterialService) {
        this.baseMaterialService = baseMaterialService;
    }

    /**
     * 新增
     *
     * @param baseMaterialDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "新增")
    @PostMapping(value = "/createMaterials")

    public int createMaterials(@Valid @RequestBody BaseMaterialDTO baseMaterialDTO) {

        return baseMaterialService.save(DtoMapper.convert(baseMaterialDTO, HvBmMaterial.class)).getId();
    }

    /**
     * 修改
     *
     * @param baseMaterialDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "更新")
    @PutMapping(value = "/saveMaterials")
    public int saveMaterials(@Valid @RequestBody BaseMaterialDTO baseMaterialDTO) {
        return baseMaterialService.save(DtoMapper.convert(baseMaterialDTO, HvBmMaterial.class)).getId();
    }

    /**
     * 删除
     *
     * @param id 实体id
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/deleteMaterials/{id}")
    public void deleteMaterials(@PathVariable int id) {
        baseMaterialService.delete(id);
    }

    /**
     * 查询
     *
     * @return 实体列表
     */
    @ApiOperation(value = "获取所有")
    @GetMapping(value = "/getAllMaterials")
    public List<BaseMaterialDTO> getAllMaterials() {
        return baseMaterialService.findAll().stream()
                .map(t -> DtoMapper.convert(t, BaseMaterialDTO.class))
                .collect(Collectors.toList());
    }

}

























