package com.hvisions.materialsmsd.controller;

import com.hvisions.materialsmsd.materials.classdto.MaterialClassDTO;
import com.hvisions.materialsmsd.materials.classdto.MaterialClassQuery;
import com.hvisions.materialsmsd.service.MaterialClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: MaterialClassController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/8</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RequestMapping(value = "/material_class")
@RestController
@Api
public class MaterialClassController {

    @Autowired
    MaterialClassService materialClassService;

    /**
     * 创建物料属性类型
     *
     * @param classDTO 属性类型
     * @return 主键
     */
    @PostMapping(value = "/create")
    @ApiOperation(value = "创建物料属性类型")
    public Integer create(@Valid @RequestBody MaterialClassDTO classDTO) {
        return materialClassService.create(classDTO);
    }

    /**
     * 修改物料属性类型
     *
     * @param classDTO 属性类型
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "修改物料属性类型")
    public void update(@Valid @RequestBody MaterialClassDTO classDTO) {
        materialClassService.update(classDTO);
    }

    /**
     * 获取物料属性类型
     *
     * @param id 属性类型主键
     * @return 属性类型
     */
    @GetMapping(value = "/findById/{id}")
    @ApiOperation(value = "根据ID获取物料属性类型")
    public MaterialClassDTO findById(@PathVariable Integer id) {
        return materialClassService.findById(id);
    }

    /**
     * 获取物料属性类型
     *
     * @param code 属性类型编码
     * @return 属性类型
     */
    @GetMapping(value = "/findByCode/{code}")
    @ApiOperation(value = "根据Code获取物料属性类型")
    public MaterialClassDTO findByCode(@PathVariable String code) {
        return materialClassService.findByCode(code);
    }

    /**
     * 获取物料属性类型分页数据
     *
     * @param query 属性类型编码
     * @return 属性类型分页数据
     */
    @PostMapping(value = "/findPage")
    @ApiOperation(value = "获取物料属性类型分页数据")
    public Page<MaterialClassDTO> findPage(@RequestBody MaterialClassQuery query) {
        return materialClassService.findPage(query);
    }

    /**
     * 删除物料属性类型
     *
     * @param id 属性类型
     */
    @DeleteMapping(value = "/deleteById/{id}")
    @ApiOperation(value = "删除物料属性类型")
    public void deleteById(@PathVariable Integer id) {
        materialClassService.deleteById(id);
    }

    /**
     * 根据物料id查询物料属性列表
     *
     * @param id 物料di
     * @return 物料属性类型列表
     */
    @GetMapping(value = "/findByMaterialId/{id}")
    @ApiOperation(value = "根据物料id查询物料属性列表")
    public List<MaterialClassDTO> findByMaterialId(@PathVariable Integer id) {
        return materialClassService.findByMaterialId(id);
    }

    /**
     * 向物料添加物料属性类型
     *
     * @param materialId 物料Id
     * @param classId    属性类型id
     */
    @PostMapping(value = "/addClassToMaterial/{materialId}/{classId}")
    @ApiOperation(value = "向物料添加物料属性类型")
    public void addClassToMaterial(@PathVariable Integer materialId, @PathVariable Integer classId) {
        materialClassService.addClassToMaterial(materialId, classId);
    }

    /**
     * 删除物料的物料属性类型
     *
     * @param materialId 物料Id
     * @param classId    属性类型id
     */
    @DeleteMapping(value = "/removeClassToMaterial/{materialId}/{classId}")
    @ApiOperation(value = "删除物料的物料属性类型")
    public void removeClassToMaterial(@PathVariable Integer materialId, @PathVariable Integer classId) {
        materialClassService.removeClassToMaterial(materialId, classId);
    }
}