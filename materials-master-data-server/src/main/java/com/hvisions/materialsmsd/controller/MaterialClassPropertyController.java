package com.hvisions.materialsmsd.controller;


import com.hvisions.materialsmsd.materials.classdto.MaterialClassPropertyDTO;
import com.hvisions.materialsmsd.service.MaterialClassPropertyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * <p>Title: MaterialClassPropertyController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/material_class_property")
@Api(description = "物料属性控制器")
public class MaterialClassPropertyController {

    private final MaterialClassPropertyService materialClassPropertyService;

    @Autowired
    public MaterialClassPropertyController(MaterialClassPropertyService materialClassPropertyService) {
        this.materialClassPropertyService = materialClassPropertyService;
    }


    /**
     * 创建属性
     *
     * @param propertyDTO 属性
     * @return 主键
     */
    @PostMapping(value = "/create")
    @ApiOperation(value = "创建属性")
    public Integer create(@Valid @RequestBody MaterialClassPropertyDTO propertyDTO) {
        return materialClassPropertyService.create(propertyDTO);
    }

    /**
     * 更新属性
     *
     * @param propertyDTO 属性
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "更新属性")
    public void update(@Valid @RequestBody MaterialClassPropertyDTO propertyDTO) {
        materialClassPropertyService.update(propertyDTO);
    }

    /**
     * 删除属性
     *
     * @param id 主键
     */
    @DeleteMapping(value = "/deleteById/{id}")
    @ApiOperation(value = "删除属性")
    public void deleteById(@PathVariable Integer id) {
        materialClassPropertyService.deleteById(id);
    }

    /**
     * 获取属性
     *
     * @param id 物料类型id
     * @return 物料属性类型属性列表
     */
    @GetMapping(value = "/findByMaterialClassId/{id}")
    @ApiOperation(value = "获取属性")
    public List<MaterialClassPropertyDTO> findByMaterialClassId(@PathVariable Integer id) {
        return materialClassPropertyService.findByMaterialClassId(id);
    }
}