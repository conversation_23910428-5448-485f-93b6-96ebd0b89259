package com.hvisions.materialsmsd.controller;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.LogAnnotation;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.materialsmsd.bom.dto.BaseBomDTO;
import com.hvisions.materialsmsd.bom.dto.BomAllDTO;
import com.hvisions.materialsmsd.bom.dto.BomDTO;
import com.hvisions.materialsmsd.bom.dto.BomQueryDTO;
import com.hvisions.materialsmsd.service.BomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: BomController</p >
 * <p>Description: bom控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/27</p >
 * l
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@Slf4j
@RequestMapping(value = "/bom")
@Api(description = "Bom控制器")
@LogAnnotation

public class BomController {

    private final BomService bomService;

    @Resource(name = "bom_extend")
    BaseExtendService bomExtendService;

    @Autowired
    public BomController(BomService bomService) {
        this.bomService = bomService;
    }

    /**
     * 添加BOM信息
     *
     * @param bomDTO 传入DTO对象
     * @return 新增bomId
     */
    @PostMapping("/createBom")
    @ApiOperation(value = "添加bom")
    public int createBom(@Valid @RequestBody BaseBomDTO bomDTO) {
        return bomService.createBom(bomDTO);
    }

    /**
     * 更新bom信息
     *
     * @param bomDTO 传入DTO对象
     * @return bom id
     */
    @PutMapping("/updateBom")
    @ApiOperation(value = "更新bom")
    public int updateBom(@Valid @RequestBody BaseBomDTO bomDTO) {
        return bomService.updateBom(bomDTO);
    }


    /**
     * 添加bom扩展属性
     *
     * @param extendColumnInfo 扩展属性信息
     */
    @PostMapping("/createBomColumn")
    @ApiOperation(value = "添加bom扩展属性")
    public void createBomColumn(@Valid @RequestBody ExtendColumnInfo extendColumnInfo) {
        bomExtendService.addExtend(extendColumnInfo);
    }


    /**
     * 根据名称,状态,版本,编码,模糊查询
     *
     * @param bomQueryDTO 传入的查询条件
     * @return 查询信息
     */
    @PostMapping("/findByBomCodeOrBomNameOrBomVersionsOrBomStatus")
    @ApiOperation(value = "/根据名称状态版本编码模糊查询")
    public Page<BomDTO> findByBomCodeOrBomNameOrBomVersionsOrBomStatus(@RequestBody BomQueryDTO bomQueryDTO) {
        return bomService.findByBomCodeOrBomNameOrBomVersionsOrBomStatus(bomQueryDTO);
    }


    /**
     * 获取生效bom
     *
     * @return 生效bom信息列表
     */
    @GetMapping(value = "/getTakeEffectBom")
    @ApiOperation(value = "获取生效bom")
    public List<BomDTO> getTakeEffectBom() {
        return bomService.getTakeEffectBom();
    }

    /**
     * 删除bom信息
     *
     * @param id bomID
     */

    @DeleteMapping("/deleteBomById/{id}")
    @ApiOperation(value = "删除bom信息")
    @Transactional(rollbackFor = Exception.class)
    public void deleteBomById(@PathVariable int id) {
        bomService.deleteBom(id);
    }

    /**
     * 删除bom扩展属性
     *
     * @param columnName 扩展属性名称
     */
    @DeleteMapping("/deleteBomColumn/{columnName}")
    @ApiOperation(value = "删除扩展属性")
    @Transactional(rollbackFor = Exception.class)
    public void deleteBomColumn(@PathVariable String columnName) {
        bomExtendService.dropExtend(columnName);
    }

    /**
     * 通过materialsId查询bom信息
     *
     * @param materialsId 物料id
     * @return bom列表
     */
    @GetMapping("/getAllByMaterialsId/{materialsId}")
    @ApiOperation(value = "通过materialsId查询bom信息")
    public List<BomDTO> getAllByMaterialsId(@PathVariable Integer materialsId) {
        return bomService.getBomByMaterialsId(materialsId);
    }

    /**
     * 通过Id查询bom信息
     *
     * @param id bomId
     * @return bom详细信息
     */
    @GetMapping("/getBomBomItemSubstituteItemById/{id}")
    @ApiOperation(value = "通过Id查询bom信息")
    @LogAnnotation
    public BomAllDTO getBomBomItemSubstituteItemByBomId(@PathVariable Integer id) {
        return bomService.getBomBomItemSubstituteItemByBomId(id);
    }

    /**
     * 根据bom编码版本获取基础bom信息
     *
     * @param bomCode    bom编码
     * @param bomVersion bom版本
     * @return 基础bom信息
     */
    @GetMapping("/getBaseBomDTOByCodeAndVersion")
    @ApiOperation(value = "根据bom编码版本获取基础bom信息")
    public BaseBomDTO getBaseBomDTOByCodeAndVersion(@RequestParam String bomCode, @RequestParam String bomVersion) {
        return bomService.getBaseBomDTOByCodeAndVersion(bomCode, bomVersion);
    }


    /**
     * 更改status状态
     *
     * @param id bomId
     */
    @PutMapping(value = "/setStatusTakeEffect/{id}")
    @ApiOperation(value = "生效bom")
    public void setStatusTakeEffect(@PathVariable("id") int id) {
        bomService.setStatusTakeEffect(id);
    }

    /**
     * 更改status状态
     *
     * @param id bomId
     */
    @PutMapping(value = "/discardedBom/{id}")
    @ApiOperation(value = "归档bom")
    public void discardedBom(@PathVariable("id") int id) {
        bomService.discardedBom(id);
    }

    /**
     * bom复制
     *
     * @param id          bomId
     * @param bomVersions 版本
     * @return copy的bomId
     */
    @PostMapping(value = "/copyBom/{id}/{bomVersions}")
    @ApiOperation(value = "bom复制功能")
    @Transactional(rollbackFor = Exception.class)
    public int copyBom(@PathVariable int id, @PathVariable String bomVersions) {
        return bomService.copyBom(id, bomVersions);
    }

    /**
     * 通过物料id获取BomId
     *
     * @param materialId 物料id
     * @return bomId
     */
    @GetMapping(value = "/getBomIdByMaterialId/{materialId}")
    @ApiOperation(value = "通过物料ID获取BomId")
    public int getBomIdByMaterialId(@PathVariable int materialId) {
        return bomService.getBomIdByMaterialId(materialId);
    }

    /**
     * 通过bomCode获取bom版本
     *
     * @param bomCode bomCode
     * @return 版本列表
     */
    @GetMapping(value = "/getBomVersionsByBomCode/{bomCode}")
    @ApiOperation(value = "通过bomCode获取bom版本")
    public List<String> getBomVersionsByBomCode(@PathVariable String bomCode) {
        return bomService.getBomVersionsByBomCode(bomCode);
    }


    /**
     * 获取所有Bom扩展字段信息
     *
     * @return Bom扩展字段信息
     */
    @GetMapping(value = "/getAllBomExtend")
    @ApiOperation(value = "获取所有BOM扩展字段信息")
    public List<ExtendColumnInfo> getAllBomExtend() {
        return bomExtendService.getExtendColumnInfo();
    }

    /**
     * 导出所有BOM信息
     *
     * @return excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    @ApiResultIgnore
    @GetMapping(value = "/exportBomAndBomItemAndSubstituteItem")
    @ApiOperation(value = "导出所有Bom信息 ")
    public ResultVO<ExcelExportDto> exportBomAndBomItemAndSubstituteItem() throws IOException, IllegalAccessException {
        return bomService.exportBomAndBomItemAndSubstituteItem();
    }


    /**
     * 导出选中的BOM信息（根据ID列表导出）
     *
     * @param idList bomId列表
     * @return excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    @ApiResultIgnore
    @PostMapping(value = "/exportBomAllByIdList")
    @ApiOperation(value = "导出选中的BOM信息（根据ID列表导出）")
    public ResultVO<ExcelExportDto> exportBomAllByIdList(@RequestBody List<Integer> idList) throws IOException, IllegalAccessException {
        return bomService.exportBomAllByIdList(idList);
    }


    /**
     * 导出所有BOM信息
     *
     * @param idList bomId列表
     * @return excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    @ApiResultIgnore
    @PostMapping(value = "/exportBomByIdList")
    @ApiOperation(value = " 导出选中的BOM信息（根据ID列表导出）（支持超链接）")

    public ResponseEntity<byte[]> exportBomByIdList(@RequestBody List<Integer> idList) throws IOException, IllegalAccessException {
        return bomService.exportBomByIdList(idList);
    }

    /**
     * 导出所有BOM信息
     *
     * @return excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    @ApiResultIgnore
    @GetMapping(value = "/exportBomAll")
    @ApiOperation(value = "导出所有Bom信息(支持超链接) ")

    public ResponseEntity<byte[]> exportBomAll() throws IOException, IllegalAccessException {
        return bomService.exportBomAll();
    }

    /**
     * 获取导入模板（支持超链接）
     *
     * @return excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getBomImportTemplate")
    @ApiOperation(value = "获取导入模板（支持超链接）")
    public ResponseEntity<byte[]> getBomImportTemplate() throws IOException, IllegalAccessException {
        return bomService.getBomImportTemplate();
    }

    /**
     * 获取导入模板
     *
     * @return excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getImportTemplate")
    @ApiOperation(value = "获取导入模板")
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        return bomService.getImportTemplate();
    }

    /**
     * 导入所有bom信息
     *
     * @param file bom信息文档
     * @return 导入结果信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importBom")
    @ApiOperation(value = "导入bom信息，如果code存在则更新，code不存在则新增")
    public List<ImportResult> importBom(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return bomService.importBom(file);
    }

}
