package com.hvisions.materialsmsd.controller;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.materials.dto.MaterialGroupDTO;
import com.hvisions.materialsmsd.entity.HvBmMaterialGroup;
import com.hvisions.materialsmsd.service.MaterialGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title: MaterialGroupController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/4</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/materialGroup")
@Slf4j
@Api(description = "物料分组(增删改查)")
public class MaterialGroupController {

    private final MaterialGroupService materialGroupService;

    @Autowired
    public MaterialGroupController(MaterialGroupService materialGroupService) {
        this.materialGroupService = materialGroupService;
    }


    /**
     * 新增物料分组
     *
     * @param materialGroupDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "新增物料分组")
    @PostMapping(value = "/createMaterialGroup")
    public int createMaterialGroup(@Valid @RequestBody MaterialGroupDTO materialGroupDTO) {

        return materialGroupService.save(DtoMapper.convert(materialGroupDTO, HvBmMaterialGroup.class)).getId();
    }

    /**
     * 修改物料分组
     *
     * @param materialGroupDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "更新")
    @PutMapping(value = "/updateMaterialGroup")
    public int updateMaterialGroup(@Valid @RequestBody MaterialGroupDTO materialGroupDTO) {
        return materialGroupService.save(DtoMapper.convert(materialGroupDTO, HvBmMaterialGroup.class)).getId();
    }

    /**
     * 删除物料分组
     *
     * @param id 实体id
     */
    @ApiOperation(value = "删除物料分组")
    @DeleteMapping(value = "/deleteMaterialGroupById/{id}")
    public void deleteMaterialGroupById(@PathVariable int id) {
        materialGroupService.deleteById(id);
    }

    /**
     * 查询物料分组
     *
     * @return 实体列表
     */
    @ApiOperation(value = "获取所有物料分组")
    @GetMapping(value = "/getAllMaterialGroup")
    public List<MaterialGroupDTO> getAllMaterialGroup() {
        return materialGroupService.findAll().stream()
                .map(t -> DtoMapper.convert(t, MaterialGroupDTO.class))
                .collect(Collectors.toList());
    }

    /**
     * 根据code desc 查询物料分组信息
     *
     * @param materialGroupDTO 物料分组信息DTO
     * @return 物料分组信息
     */
    @PostMapping(value = "/findMaterialGroupByCodeOrDesc")
    @ApiOperation("根据code desc 查询物料分组信息")
    public Page<MaterialGroupDTO> findMaterialGroupByCodeOrDesc(@RequestBody MaterialGroupDTO materialGroupDTO) {
        return materialGroupService.findMaterialGroupByCodeOrDesc(materialGroupDTO);
    }

    /**
     * 通过ID查询物料分组信息
     *
     * @param id 物料分组ID
     * @return 物料分组信息
     */
    @GetMapping(value = "/getMaterialGroupById/{id}")
    @ApiOperation(value = "查询物料分组信息通过id")
    public MaterialGroupDTO getMaterialGroupById(@PathVariable int id) {
        return materialGroupService.getAllMaterialGroupById(id);

    }

    /**
     * 根据物料分组ID列表删除物料分组信息
     *
     * @param idList 物料分组Id列表
     */
    @DeleteMapping(value = "/deleteByIdIn")
    @ApiOperation(value = "根据物料分组ID列表删除物料分组信息")
    public void deleteByIdIn(@RequestBody List<Integer> idList) {
        materialGroupService.deleteByIdIn(idList);
    }

}
