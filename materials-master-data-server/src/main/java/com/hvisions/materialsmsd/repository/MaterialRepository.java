package com.hvisions.materialsmsd.repository;

import com.hvisions.materialsmsd.entity.HvBmMaterial;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: DemoEntityRepository</p>
 * <p>Description: 系统行政部门仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaterialRepository extends JpaRepository<HvBmMaterial, Integer> {


    /**
     * 根据名称查找对象
     *
     * @param materialCode 名称
     * @return 实体名
     */
    HvBmMaterial findFirstByMaterialCode(String materialCode);

    /**
     * 根据material编码获取数据
     *
     * @param materialCode 物料编码
     * @param eigenvalue   特征值
     * @return 返回物料数据
     */
    HvBmMaterial getByMaterialCodeAndEigenvalue(String materialCode, String eigenvalue);

    /**
     * 根据ID列表查询物料
     *
     * @param id id
     * @return 物料列表信息
     */
    List<HvBmMaterial> getHvBmMaterialByIdIn(List<Integer> id);

    /**
     * 模糊查询
     *
     * @param materialCode 物料编码
     * @return 物料信息列表
     */
    List<HvBmMaterial> getHvBmMaterialByMaterialCode(String materialCode);

    /**
     * 判断物料分组在物料中是否存在
     *
     * @param materialGroup 物料分组ID
     * @return boolean
     */
    boolean existsByMaterialGroup(int materialGroup);

    /**
     * 判断 单位是否已被使用
     *
     * @param uomId 单位ID
     * @return boolean
     */
    boolean existsByUom(int uomId);


    /**
     * 判断 物料类型是否已被使用
     *
     * @param typeId 单位ID
     * @return boolean
     */
    boolean existsByMaterialType(int typeId);

    /**
     * 获取所有成品半成品信息
     *
     * @param product   成品
     * @param byproduct 半成品
     * @param pageable  分页条件
     * @return 分页信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "select m  from HvBmMaterial m where m.rootMaterialType in (?1,?2) ")
    Page<HvBmMaterial> getAllByMaterialType(int product, int byproduct, Pageable pageable);


    /**
     * 获取所有成品半成品信息
     *
     * @param product   成品
     * @param byproduct 半成品
     * @return 分页信息
     */
    @Query(value = "select m  from HvBmMaterial m where m.rootMaterialType in (?1,?2) ")
    List<HvBmMaterial> getListByMaterialType(int product, int byproduct);


    /**
     * 根据物料类型查询物料信息
     *
     * @param typeId 物料类型id
     * @return 物料iD
     */
    List<HvBmMaterial> getAllByMaterialType(int typeId);

    /**
     * 获取所有成品半成品信息
     *
     * @param product   成品
     * @param byproduct 半成品
     * @param name      物料名称
     * @param pageable  分页条件
     * @return 分页信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "select m  from HvBmMaterial m where m.rootMaterialType in (?1,?2) and m.materialName like %?3%")
    Page<HvBmMaterial> getAllByMaterialTypeByName(int product, int byproduct, String name, Pageable pageable);

    /**
     * 获取所有成品半成品信息
     *
     * @param product   成品
     * @param byproduct 半成品
     * @param code      物料编码
     * @param pageable  分页条件
     * @return 分页信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "select m  from HvBmMaterial m where m.rootMaterialType in (?1,?2) and m.materialCode like %?3%")
    Page<HvBmMaterial> getAllByMaterialTypeByCode(int product, int byproduct, String code, Pageable pageable);


    /**
     * 获取所有成品半成品信息
     *
     * @param product   成品
     * @param byproduct 半成品
     * @param name      物料名称
     * @param code      物料编码
     * @param pageable  分页条件
     * @return 分页信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "select m  from HvBmMaterial m where m.rootMaterialType in (?1,?2) and m.materialName like %?3% and m" +
            ".materialCode like %?4% ")
    Page<HvBmMaterial> getAllByMaterialTypeByStringCodeAndName(int product, int byproduct, String name,
                                                               String code, Pageable pageable);

    /**
     * 获取所有成品半成品信息
     *
     * @param product   成品
     * @param byproduct 半成品
     * @param name      物料名称
     * @param code      物料编码
     * @param pageable  分页条件
     * @return 分页信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "select m  from HvBmMaterial m where m.rootMaterialType in (?1,?2)  and (m.materialName like %?3% or m" +
            ".materialCode like %?4%) ")
    Page<HvBmMaterial> getAllByMaterialTypeByStringCodeOrName(int product, int byproduct, String name,
                                                              String code, Pageable pageable);

    /**
     * 查询物料
     *
     * @param code     物料名称
     * @param name     物料名称
     * @param pageable 分页条件
     * @return 分页信息
     */
    Page<HvBmMaterial> getAllByMaterialCodeContainsOrMaterialNameContains(String code, String name, Pageable pageable);

    /**
     * 查找所有根物料类型为null的物料
     *
     * @return 物料列表
     */
    List<HvBmMaterial> findAllByRootMaterialTypeNull();

    /**
     * 根据物料id列表删除
     *
     * @param materialIdList 物料id列表
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteByIdIn(List<Integer> materialIdList);

}
