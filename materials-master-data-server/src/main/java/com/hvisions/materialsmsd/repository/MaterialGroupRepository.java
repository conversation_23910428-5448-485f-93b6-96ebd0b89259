package com.hvisions.materialsmsd.repository;

import com.hvisions.materialsmsd.entity.HvBmMaterialGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: MaterialTypeRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/4</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaterialGroupRepository extends JpaRepository<HvBmMaterialGroup, Integer> {
    /**
     * 根据物料分组或者描述查询
     *
     * @param groupCode 物料类型编码
     * @param groupDesc 物料类型描述
     * @return 物料类型信息
     */
    HvBmMaterialGroup findAllByGroupCodeAndGroupName(String groupCode, String groupDesc);

    /**
     * 根据物料分组编码查询物料分组信息
     *
     * @param groupCode 物料分组编码
     * @return 物料分组信息
     */
    HvBmMaterialGroup getByGroupCode(String groupCode);

    /**
     * 根据物料分组ID列表删除物料分组信息
     *
     * @param idList 物料分组Id列表
     */
    void deleteByIdIn(List<Integer> idList);

}
