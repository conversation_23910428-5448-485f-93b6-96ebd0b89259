package com.hvisions.materialsmsd.repository;

import com.hvisions.materialsmsd.entity.HvBmBom;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: BomRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/26</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface BomRepository extends JpaRepository<HvBmBom, Integer> {
    /**
     * 根据bomCode获取信息
     *
     * @param bomCode     bomCode
     * @param bomVersions bom版本
     * @return bom信息
     */
    HvBmBom findByBomCodeAndBomVersions(String bomCode, String bomVersions);

    /**
     * 判断bomCode是否存在
     *
     * @param bomCode bomCode
     * @return true or false
     */
    boolean existsByBomCode(String bomCode);

    /**
     * 判断物料单位是否存在
     *
     * @param unitId 物料单位ID
     * @return true or false
     */
    boolean existsByUnitId(int unitId);

    /**
     * 根据Id查询
     *
     * @param id 主键
     * @return bom信息
     */
    HvBmBom getAllById(int id);

    /**
     * 根据bomCode查询
     *
     * @param bomCode 主键
     * @return bom信息列表
     */
    List<HvBmBom> getAllBomByBomCode(String bomCode);

    /**
     * 根据Id列表查询
     *
     * @param idList 主键
     * @return bom信息列表
     */
    List<HvBmBom> getAllByIdIn(List<Integer> idList);

    /**
     * 根据bom状态查询
     *
     * @param state bom状态
     * @return bom信息列表
     */
    List<HvBmBom> getByBomStatus(int state);

    /**
     * 根据名称查找对象
     *
     * @param bomCode     名称
     * @param bomVersions 版本
     * @return 实体名
     */
    HvBmBom findFirstByBomCodeAndBomVersions(String bomCode, String bomVersions);


}
