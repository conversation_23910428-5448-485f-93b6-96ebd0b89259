package com.hvisions.materialsmsd.repository;

import com.hvisions.materialsmsd.entity.HvBmParseSetting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: ParseSetttingRepository</p>
 * <p>Description: 物料编码设置规则仓储</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/14</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface ParseSettingRepository extends JpaRepository<HvBmParseSetting, Integer> {

    /**
     * 查找第一条数据
     * @return 配置数据
     */
    @Query("select l from HvBmParseSetting l")
    HvBmParseSetting findTop();
}









