package com.hvisions.materialsmsd.repository;

import com.hvisions.materialsmsd.entity.HvBmMaterialProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: MaterialPropertyRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface MaterialPropertyRepository extends JpaRepository<HvBmMaterialProperty, Integer> {

    /**
     * 根据编码和属性类型编码查询
     *
     * @param code      属性编码
     * @param classCode 类型编码
     * @return 属性列表
     */
    List<HvBmMaterialProperty> findAllByCodeAndClassCode(String code, String classCode);
}