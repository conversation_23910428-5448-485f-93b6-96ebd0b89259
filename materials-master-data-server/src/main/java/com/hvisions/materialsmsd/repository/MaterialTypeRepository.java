package com.hvisions.materialsmsd.repository;

import com.hvisions.materialsmsd.entity.HvBmMaterialType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: MaterialTypeRepository</p >
 * <p>Description: 物料类型仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-06</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface MaterialTypeRepository extends JpaRepository<HvBmMaterialType, Integer> {


    /**
     * 根据父级ID 查询物料类型
     *
     * @param parentId 父级ID
     * @return 物料类型列表
     */
    List<HvBmMaterialType> getAllByParentId(int parentId);

    /**
     * 根据id列表查询物料类型
     *
     * @param idIn id列表
     * @return 物料类型列表
     */
    List<HvBmMaterialType> getAllByIdIn(List<Integer> idIn);


    /**
     * 根据类型编码查询
     *
     * @param code 物料类型编码
     * @return 物料类型
     */
    HvBmMaterialType getAllByMaterialTypeCode(String code);

}