package com.hvisions.materialsmsd.repository;

import com.hvisions.materialsmsd.entity.HvBmSubstituteItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: SubstituteItemRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/29</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface SubstituteItemRepository extends JpaRepository<HvBmSubstituteItem, Integer> {


    /**
     * 根据bomItemId查询信息
     *
     * @param bomItemId bomItemId
     * @return 信息列表
     */
    List<HvBmSubstituteItem> getAllByBomItemId(int bomItemId);

    /**
     * 根据bomItemId列表查询信息
     *
     * @param bomItemIdIn bomItemId列表
     * @return 替代料信息列表
     */
    List<HvBmSubstituteItem> getAllByBomItemIdIn(List<Integer> bomItemIdIn);

    /***
     * 判断materialId是否存在
     * @param materialsId materialId
     * @return true or false
     */
    boolean existsByMaterialsId(Integer materialsId);

    /**
     * 根据bomItemID删除替代物料信息
     *
     * @param bomItemId bomitemID
     */
    void deleteAllByBomItemIdIn(List<Integer> bomItemId);

    /**
     * 根据SubstituteItemCode 与BomItemId查询
     *
     * @param substituteItemCode 替代物料编码
     * @param bomItemId          bomItemID
     * @return 替代物料信息
     */
    HvBmSubstituteItem getBySubstituteItemCodeAndBomItemId(String substituteItemCode, Integer bomItemId);

    /**
     * 根据SubstituteItemCode查询替代物料
     *
     * @param substituteItemCode 替代物料编码
     * @return 替代物料信息
     */
    HvBmSubstituteItem getBySubstituteItemCode(String substituteItemCode);


}
