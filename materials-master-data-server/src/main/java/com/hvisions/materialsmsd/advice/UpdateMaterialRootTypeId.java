package com.hvisions.materialsmsd.advice;

import com.hvisions.materialsmsd.service.MaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <p>Title: UpdateMaterialRootTypeId</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/5/8</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class UpdateMaterialRootTypeId implements CommandLineRunner {

    @Autowired
    MaterialService materialService;

    /**
     * Callback used to run the bean.
     *
     * @param args incoming main method arguments
     * @throws Exception on error
     */
    @Override
    public void run(String... args) throws Exception {
        materialService.updateMaterialRootType();
    }
}









