/*
 Navicat Premium Data Transfer

 Source Server         : test
 Source Server Type    : MySQL
 Source Server Version : 80012
 Source Host           : ************:3307
 Source Schema         : materials

 Target Server Type    : MySQL
 Target Server Version : 80012
 File Encoding         : 65001

 Date: 17/05/2019 10:46:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hv_bm_bom
-- ----------------------------
CREATE TABLE `hv_bm_bom`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `bom_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'bom编码',
  `bom_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'bom名称',
  `bom_desc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'bom描述',
  `bom_versions` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'bom版本',
  `bom_status` int(11) NOT NULL COMMENT 'bom状态\n0-新建 \n1-生效\n2-归档',
  `bom_count` decimal(10, 4) NULL DEFAULT NULL COMMENT 'bom数量',
  `unit_id` int(11) NOT NULL COMMENT '计量单位',
  `creator_id` int(11) NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `updater_id` int(11) NULL DEFAULT NULL COMMENT '修改人',
  `sys_num` int(11) NULL DEFAULT NULL COMMENT '系统代码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code_versions`(`bom_code`, `bom_versions`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 295 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_bom_extend
-- ----------------------------
CREATE TABLE `hv_bm_bom_extend`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `bom_id` int(11) NOT NULL COMMENT 'bomid',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 133 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_bom_item
-- ----------------------------
CREATE TABLE `hv_bm_bom_item`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `bom_id` int(11) NOT NULL COMMENT 'bom主键',
  `bom_item_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '编码',
  `bom_item_type` int(11) NOT NULL COMMENT '1-input 投入\n2-output 产出\n3-optional 可选\n4-intermediate 半成品',
  `bom_item_count` decimal(10, 4) NULL DEFAULT NULL COMMENT '数量',
  `bom_item_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `materials_id` int(11) NOT NULL COMMENT 'materials主键',
  `updater_id` int(11) NULL DEFAULT NULL COMMENT '修改人',
  `creator_id` int(11) NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `sys_num` int(11) NULL DEFAULT NULL COMMENT '系统代码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `bom_id_UNIQUE`(`bom_id`, `materials_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 330 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_bom_item_extend
-- ----------------------------
CREATE TABLE `hv_bm_bom_item_extend`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bom_item_id` int(11) NOT NULL,
  `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'null,Remark,备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 232 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'bomitem扩展表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_bom_material
-- ----------------------------
CREATE TABLE `hv_bm_bom_material`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bom_id` int(11) NULL DEFAULT NULL COMMENT 'bom 主键',
  `material_id` int(11) NULL DEFAULT NULL COMMENT 'material主键',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 53 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_material
-- ----------------------------
CREATE TABLE `hv_bm_material`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料名称',
  `material_desc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料描述',
  `material_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物料编码',
  `material_type` int(11) NOT NULL COMMENT '物料类型',
  `creator_id` int(11) NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `updater_id` int(11) NULL DEFAULT NULL COMMENT '修改人',
  `sys_num` int(11) NULL DEFAULT NULL COMMENT '系统代码',
  `uom` int(11) NULL DEFAULT NULL COMMENT '单位ID',
  `eigenvalue` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '特征值',
  `serial_number_profile` tinyint(1) NULL DEFAULT NULL COMMENT '是否追溯',
  `material_group` int(11) NULL DEFAULT NULL COMMENT '物料分类',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `material_eigenvalue`(`material_code`, `eigenvalue`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 239 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_material_extend
-- ----------------------------
CREATE TABLE `hv_bm_material_extend`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `material_id` int(11) NULL DEFAULT NULL,
  `Address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'null,Address,产地',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 210 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物料扩展表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_material_type
-- ----------------------------
CREATE TABLE `hv_bm_material_type`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `type_desc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `hv_bm_material_type_type_code_uindex`(`type_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_parse_setting
-- ----------------------------
CREATE TABLE `hv_bm_parse_setting`  (
  `code_start` int(11) NULL DEFAULT NULL COMMENT '物料编码开始位',
  `code_end` int(11) NULL DEFAULT NULL COMMENT '物料编码结束',
  `eigen_start` int(11) NULL DEFAULT NULL COMMENT '特征值开始',
  `eigen_end` int(11) NULL DEFAULT NULL COMMENT '特征值结束',
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_substitute_item
-- ----------------------------
CREATE TABLE `hv_bm_substitute_item`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `substitute_item_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'substituteltem_编码',
  `substitute_item_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'substituteltem_名称',
  `materials_id` int(11) NOT NULL COMMENT 'materials主键',
  `bom_item_id` int(11) NOT NULL,
  `ratio` decimal(10, 4) NOT NULL COMMENT '替代比例',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  `creator_id` int(11) NULL DEFAULT NULL,
  `updater_id` int(11) NULL DEFAULT NULL,
  `sys_num` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code_bomItemId`(`substitute_item_code`, `bom_item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 64 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_substitute_item_extend
-- ----------------------------
CREATE TABLE `hv_bm_substitute_item_extend`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `substitute_item_id` int(11) NOT NULL COMMENT '替代物id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_bm_unit
-- ----------------------------
CREATE TABLE `hv_bm_unit`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '计量单位主键',
  `symbol` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '国际符号',
  `description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '中文符号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `en_symbol_UNIQUE`(`symbol`) USING BTREE,
  UNIQUE INDEX `zh_cn_symbol_UNIQUE`(`description`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 50 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
