#默认公共配置
spring:
  servlet:
    multipart:
      max-file-size: 100MB
  jmx:
    enabled: false
  profiles:
    active: dev
  http:
    encoding:
      force: true
    charset: UTF-8
    enabled: true
  tomcat:
    uri-encoding: UTF-8
  aop:
    proxy-target-class: true
  cache:
    type: redis
  redis:
    host: ************
    port: 6379
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: admin
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    #是否输入Jpa生成的sql语句,生产环境可以关闭
    show-sql: false
    #数据库生成策略，如果打开会根据entity对象生成数据库。尽量不要使用
    hibernate:
      ddl-auto: update
  #服务注册名
  application:
    name: materials-master-data
  #国际化配置
  messages:
    basename: i18n/messages
    cache-seconds: -1
    encoding: utf-8
  cloud:
    refresh:
      #为了解决springboot与spring cloud数据库初始化检查添加的配置项的循环依赖问题所添加
      refreshable: none
ribbon:
  ConnectTimeout: 50000 # 连接超时时间(ms)
  ReadTimeout: 50000 # 通信超时时间(ms)
hystrix:
  command:
    default:
    execution:
    isolation:
    thread:
    timeoutInMilliseconds: 60000 # 设置hystrix的超时时间为6000ms
mybatis:
  configuration:
    map-underscore-to-camel-case: true
  mapperLocations: classpath:mapper/*.xml
h-visions:
  log:
    enable: false
  materialBatchNum: "(?<code>^[a-zA-z]{8})|(?<eigenvalue>[0-9]{2})"
  service-name: 物料服务
  data-audit:
    enable: false
#开启所有的健康监控信息
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
#info标签：可以在springboot admin的Insights界面Detail中进行展示,也可以再eureka界面点击实例名称查看
info:
  build:
    artifact: '@project.artifactId@'
    version: '@project.version@'
    server-name: ${h-visions.service-name}
server:
  port: 9050
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8763/eureka/
  instance:
    prefer-ip-address: true
    instance-id: materialsmsd-dev:${server.port}
