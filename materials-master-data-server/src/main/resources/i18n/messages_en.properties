# \u901A\u7528\u5F02\u5E38\u4FE1\u606F
SUCCESS=SUCCESS
SERVER_ERROR=SERVER_ERROR
JSON_PARSE_ERROR=JSON_PARSE_ERROR
ILLEGAL_STRING=IL<PERSON>GAL_STRING
NULL_RESULT=NULL_RESULT
VIOLATE_INTEGRITY=VIOLATE_INTEGRITY
IMPORT_FILE_NO_SUPPORT=IMPORT_FILE_NO_SUPPORT
IMPORT_SHEET_IS_NULL=IMPORT_SHEET_IS_NULL
ENTITY_PROPERTY_NOT_SUPPORT=ENTITY_PROPERTY_NOT_SUPPORT
SAVE_SHOULD_NO_IDENTITY=SAVE_SHOULD_NO_IDENTITY
UPDATE_SHOULD_HAVE_IDENTITY=UPDATE_SHOULD_HAVE_IDENTITY
CONST_VIOLATE=const_violate
NO_SUCH_ELEMENT=NO_SUCH_ELEMENT
DATA_INTEGRITY_VIOLATION=DATA_INTEGRITY_VIOLATION
COLUMN_EXISTS_VALUE=COLUMN_EXISTS_VALUE
#------------------------------------------------
DEMO_EXCEPTION_ENUM=DEMO_EXCEPTION_ENUM
MATERIALS_CANNOT_BE_ZERO=MATERIALS_CANNOT_BE_ZERO
MATERIALS_NOT_EXISTS=MATERIALS_NOT_EXISTS
BOM_NOT_EXISTS=BOM_NOT_EXISTS
BOM_CANNOT_BE_ZERO=BOM_CANNOT_BE_ZERO
BOM_ITEM_CANNOT_BE_ZERO=BOM_ITEM_CANNOT_BE_ZERO
BOM_ITEM_NOT_EXISTS=BOM_ITEM_NOT_EXISTS
UNIT_ID_NOT_EXISTS=UNIT_ID_NOT_EXISTS
CONSTRAINT_VIOLATION=CONSTRAINT_VIOLATION
REPETITIVE_CORRELATIVE_MATERIAL=REPETITIVE_CORRELATIVE_MATERIAL
BOM_MATERIAL_NOT_EXISTS=BOM_MATERIAL_NOT_EXISTS
SYMBOL_EXISTS=SYMBOL_EXISTS
DESC_EXISTS=DESC_EXISTS
NOW_NEWLY_CANNOT_BE_MODIFIED=NOW_NEWLY_CANNOT_BE_MODIFIED
MATERIAL_BOM_RELEVANCY=MATERIAL_BOM_RELEVANCY
MATERIAL_BOM_ITEM_RELEVANCY=MATERIAL_BOM_ITEM_RELEVANCY
MATERIAL_SUBSTITUTE_ITEM_RELEVANCY=MATERIAL_SUBSTITUTE_ITEM_RELEVANCY
BOM_ITEM_CODE_EXISTS=BOM_ITEM_CODE_EXISTS
MATERIAL_CODE_EIGENVALUE_ERROR=MATERIAL_CODE_EIGENVALUE_ERROR
MATERIALS_NOT_NULL=MATERIALS_NOT_NULL
SUBSTITUTE_ITEM_COUNT_OUT_OF_RANGE=SUBSTITUTE_ITEM_COUNT_OUT_OF_RANGE
MATERIAL_GROUP_IS_USE=MATERIAL_GROUP_IS_USE
UOM_IS_USE=UOM_IS_USE
BOM_ID_NOT_NULL=BOM_ID_NOT_NULL
BOM_ITEM_MATERIAL_CODE_EXISTS=BOM_ITEM_MATERIAL_CODE_EXISTS
BOM_STATUS_ARCHIVED=BOM_STATUS_ARCHIVED
DESCRIPTION_NOT_NULL=DESCRIPTION_NOT_NULL
MATERIAL_TYPE_ERROR=MATERIAL_TYPE_ERROR
MATERIAL_TYPE_NAME_ERROR=MATERIAL_TYPE_NAME_ERROR
REPEATED_ENTRY_INTO_FORCE_BOMCODE=REPEATED_ENTRY_INTO_FORCE_BOMCODE
UOM_ERROR=UOM_ERROR
UOM_NAME_ERROR=UOM_NAME_ERROR
UNIT_SYMBOL_NOT_NULL=UNIT_SYMBOL_NOT_NULL
UNIT_SYMBOL_NOT_NULL_PLEASE_FILL_IN_CORRECTLYU=UNIT_SYMBOL_NOT_NULL_PLEASE_FILL_IN_CORRECTLYU
BOM_MATERIAL_IS_EXISTS=BOM_MATERIAL_IS_EXISTS
BOM_CODE_OR_VERSION_NOT_NULL=BOM_CODE_OR_VERSION_NOT_NULL
REGEX_ERROR_MATERIAL_NOT_FIND=REGEX_ERROR_MATERIAL_NOT_FIND
MATERIAL_HAVE_BOM=MATERIAL_HAVE_BOM
MATERIAL_PARSE_NOT_SET=MATERIAL_PARSE_NOT_SET
PARSE_SETTING_ERROR=PARSE_SETTING_ERROR
NOT_BOM=NOT_BOM
BOM_NOT_LESSTHEN_ZERO=BOM_NOT_LESSTHEN_ZERO
NOT_NEW_BOM_CANNOT_BE_UPDATE=NOT_NEW_BOM_CANNOT_BE_UPDATE
BOM_NOT_FOUND=bon_not_find
FIXED_TYPE_CANNOT_BE_DELETED=FIXED_TYPE_CANNOT_BE_DELETED
MATERIAL_TYPE_NOT_NULL=MATERIAL_TYPE_NOT_NULL
MATERIAL_TYPE_IS_USE=MATERIAL_TYPE_IS_USE
MATERIAL_TYPE_HAVE_DATA=MATERIAL_TYPE_HAVE_DATA
BOM_VERSIONS_EXISTS=BOM_VERSIONS_EXISTS
NOT_NEW_BOM_CANNOT_BE_DELETE=NOT_NEW_BOM_CANNOT_BE_DELETE
CHOOSE_BOM=CHOOSE_BOM
BOM_STATE_NOT_TAKEEFFECT=BOM_STATE_NOT_TAKEEFFECT