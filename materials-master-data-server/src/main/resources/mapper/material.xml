<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.materialsmsd.dao.MaterialMapper">
    <resultMap id="material" type="com.hvisions.materialsmsd.materials.dto.MaterialDTO">
        <result property="uomName" column="description"/>
        <result property="materialGroupCode" column="group_code"/>
        <result property="materialGroupName" column="group_name"/>
        <result property="bomVersion" column="bom_versions"/>
    </resultMap>

    <select id="getMaterialByQuery" resultMap="material"
            parameterType="com.hvisions.materialsmsd.materials.dto.MaterialQueryDTO">
        select
        m.*,
        t.material_type_code,
        t.material_type_name,
        g.group_code,
        g.group_name,
        b.bom_code,
        b.bom_name,
        b.bom_versions,
        u.symbol,
        u.description
        from
        hv_bm_material m
        left join hv_bm_material_extend e on m.id = e.material_id
        left join hv_bm_material_type t on m.material_type = t.id
        left join hv_bm_material_group g on m.material_group = g.id
        left join hv_bm_bom_material bm on m.id = bm.material_id
        left join hv_bm_bom b on bm.bom_id = b.id
        left join hv_bm_unit u on m.uom = u.id
        <where>
            <if test="dto.materialCode != null">
                and m.material_code like concat('%',#{dto.materialCode},'%')
            </if>
            <if test="dto.materialName != null">
                and m.material_name like concat('%',#{dto.materialName},'%')
            </if>
            <if test="dto.materialType != null">
                and m.material_type = #{dto.materialType}
            </if>
            <if test="dto.materialGroup != null">
                and m.material_group = #{dto.materialGroup}
            </if>
            <if test="dto.keyWord != null">
                and m.material_code like concat('%',#{dto.keyWord},'%') or m.material_name like
                concat('%',#{dto.keyWord},'%') or m.eigenvalue like concat('%',#{dto.keyWord},'%')
            </if>
            <if test="dto.extend !=null and dto.extend.size() >0">
                <foreach collection="dto.extend.entrySet()" item="item" index="index">
                    and ${index} = #{item}
                </foreach>
            </if>
        </where>
    </select>

    <resultMap id="rm" type="com.hvisions.materialsmsd.materials.dto.MaterialDTO"></resultMap>

    <select resultMap="rm" id="getMaterials" parameterType="com.hvisions.materialsmsd.materials.dto.QueryDTO">
        SELECT
        t1.id,
        t1.create_time,
        t1.update_time,
        t1.creator_id,
        t1.updater_id,
        t1.material_code,
        t1.material_desc,
        t1.material_name,
        t1.eigenvalue,
        t1.material_group,
        t1.material_type,
        t1.uom,
        t1.serial_number_profile,
        t2.description uomName,
        t3.material_type_code,
        t3.material_type_name,
        t4.group_code materialGroupCode,
        t4.group_name materialGroupName,
        t6.id bomId,
        t6.bom_code,
        t6.bom_desc,
        t6.bom_count,
        t6.bom_name,
        t6.bom_versions bomVersion
        FROM
        hv_bm_material t1
        LEFT JOIN hv_bm_unit t2 ON t1.uom = t2.id
        LEFT JOIN hv_bm_material_type t3 ON t1.material_type = t3.id
        LEFT JOIN hv_bm_material_group t4 ON t1.material_group = t4.id
        LEFT JOIN hv_bm_bom_material t5 ON t1.id = t5.material_id
        LEFT JOIN hv_bm_bom t6 ON t6.id = t5.bom_id
        <where>
            <if test="query.keyWord != null and query.keyWord != ''">
                AND ( t1.material_code like concat('%',#{query.keyWord},'%')
                OR t1.material_name like concat('%',#{query.keyWord},'%')
                OR t1.eigenvalue like concat('%',#{query.keyWord},'%'))
            </if>
            <if test="query.materialCode !=null and query.materialCode != ''">
                AND t1.material_code like concat('%',#{query.materialCode},'%')
            </if>
            <if test="query.materialName !=null and query.materialName != ''">
                AND t1.material_name like concat('%',#{query.materialName},'%')
            </if>
            <if test="query.materialType !=null ">
                AND t3.id = #{query.materialType}
            </if>
            <if test="query.materialGroup !=null ">
                AND t4.id = #{query.materialGroup}
            </if>
            <if test="query.hasBom !=null and query.hasBom == true">
                AND t6.id IS NOT NULL
            </if>
            <if test="query.hasBom !=null and query.hasBom == false">
                AND t6.id IS NULL
            </if>
            <if test="query.materialTypes !=null and query.materialTypes.size > 0">
                <foreach collection="query.materialTypes" index="index" item="item" open="and t3.material_type_code in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.materialIdList !=null and query.materialIdList.size > 0">
                <foreach collection="query.materialIdList" index="index" item="item" open="and t1.id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.keyWord != null">
                and t1.material_code like concat('%',#{query.keyWord},'%') or t1.material_name like
                concat('%',#{query.keyWord},'%') or t1.eigenvalue like concat('%',#{query.keyWord},'%')
            </if>
        </where>
    </select>
</mapper>