package com.hvisions.rawmaterial.enums.unified;

/**
 * 任务类型枚举
 * 定义原料生产系统中所有的任务类型
 * 根据您提供的完整任务类型列表重新整理
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public enum TaskTypeEnum {

    // ==================== 高粱、稻壳通用任务类型 ====================
    /**
     * 入仓任务
     */
    INBOUND("INBOUND", "入仓任务"),

    /**
     * 筒仓实时状态信息(含一期、3*17、1*7、225仓)
     */
    SILO_REALTIME_STATUS("SILO_REALTIME_STATUS", "筒仓实时状态信息(含一期、3*17、1*7、225仓)"),

    /**
     * 筒仓清仓记录
     */
    SILO_CLEARANCE("SILO_CLEARANCE", "筒仓清仓记录"),

    /**
     * 筒仓物料切换记录
     */
    SILO_MATERIAL_SWITCH("SILO_MATERIAL_SWITCH", "筒仓物料切换记录"),

    /**
     * 筒仓倒仓任务信息
     */
    SILO_TRANSFER("SILO_TRANSFER", "筒仓倒仓任务信息"),

    /**
     * 筒仓通风记录
     */
    VENTILATION("VENTILATION", "通风记录"),

    // ==================== 高粱业务任务类型 ====================

    /**
     * 高粱二期筒仓收货接口（入仓）
     *//*
    SORGHUM_SILO_INBOUND("SORGHUM_SILO_INBOUND", "高粱二期筒仓收货接口（入仓）"),

    *//**
     * 高粱筒仓实时状态信息(含一期、3*17、1*7、225仓)
     *//*
    SORGHUM_SILO_REALTIME_STATUS("SORGHUM_SILO_REALTIME_STATUS", "高粱筒仓实时状态信息(含一期、3*17、1*7、225仓)"),

    *//**
     * 筒仓清仓记录
     *//*
    SORGHUM_SILO_CLEARANCE("SORGHUM_SILO_CLEARANCE", "高粱筒仓清仓记录"),

    *//**
     * 高粱筒仓物料切换记录
     *//*
    SORGHUM_SILO_MATERIAL_SWITCH("SORGHUM_SILO_MATERIAL_SWITCH", "高粱筒仓物料切换记录"),

    *//**
     * 高粱倒仓任务信息
     *//*
    SORGHUM_SILO_TRANSFER("SORGHUM_SILO_TRANSFER", "高粱倒仓任务信息"),

    *//**
     * 高粱通风记录
     *//*
    SORGHUM_VENTILATION("SORGHUM_VENTILATION", "高粱通风记录"),*/

    /**
     * 高粱二期转一期的转运任务信息
     */
    SORGHUM_TRANSFER("SORGHUM_TRANSFER", "高粱二期转一期的转运任务信息"),

    /**
     * 高粱入仓任务信息（未经二期筒仓转运）
     */
    SORGHUM_DIRECT_INBOUND("SORGHUM_DIRECT_INBOUND", "高粱入仓任务信息（未经二期筒仓转运）"),

    /**
     * 高粱转粮任务信息（一期对接）
     */
    SORGHUM_GRAIN_TRANSFER("SORGHUM_GRAIN_TRANSFER", "高粱转粮任务信息（一期对接）"),

    /**
     * 高粱粉碎任务信息（一期对接）
     */
    SORGHUM_CRUSHING_TASK("SORGHUM_CRUSHING_TASK", "高粱粉碎任务信息（一期对接）"),

    /**
     * 高粱粉转运任务信息（一期对接）
     */
    SORGHUM_POWDER_TRANSFER("SORGHUM_POWDER_TRANSFER", "高粱粉转运任务信息（一期对接）"),

    /**
     * 高粱粉分发任务信息（一期对接）
     */
    SORGHUM_POWDER_DISTRIBUTION("SORGHUM_POWDER_DISTRIBUTION", "高粱粉分发任务信息（一期对接）"),

    /**
     * 高粱粉转运任务批次发放进度
     */
    SORGHUM_POWDER_TRANSFER_BATCH_PROGRESS("SORGHUM_POWDER_TRANSFER_BATCH_PROGRESS", "高粱粉转运任务批次发放进度"),

    /**
     * 高粱二期筒仓进出快检数据对接（淀粉、水分、蛋白质）
     */
    SORGHUM_QUALITY_CHECK("SORGHUM_QUALITY_CHECK", "高粱二期筒仓进出快检数据对接（淀粉、水分、蛋白质）"),

    // ==================== 稻壳业务任务类型 ====================

    /**
     * 筒仓清仓记录
     */
    /*RICE_HUSK_SILO_CLEARANCE("RICE_HUSK_SILO_CLEARANCE", "筒仓清仓记录"),

    *//**
     * 筒仓物料切换记录
     *//*
    RICE_HUSK_SILO_MATERIAL_SWITCH("RICE_HUSK_SILO_MATERIAL_SWITCH", "筒仓物料切换记录"),

    *//**
     * 通风记录
     *//*
    RICE_HUSK_VENTILATION("RICE_HUSK_VENTILATION", "通风记录"),

    *//**
     * 倒仓任务信息
     *//*
    RICE_HUSK_SILO_TRANSFER("RICE_HUSK_SILO_TRANSFER", "倒仓任务信息"),

    *//**
     * 筒仓实时状态信息(含一期、3*17、1*7、225仓)
     *//*
    RICE_HUSK_SILO_REALTIME_STATUS("RICE_HUSK_SILO_REALTIME_STATUS", "筒仓实时状态信息(含一期、3*17、1*7、225仓)"),*/

    /**
     * 稻壳二期转一期的转运任务信息
     */
    RICE_HUSK_TRANSFER("RICE_HUSK_TRANSFER", "稻壳二期转一期的转运任务信息"),

    /**
     * 入仓任务信息（未经二期筒仓转运）
     */
    RICE_HUSK_DIRECT_INBOUND("RICE_HUSK_DIRECT_INBOUND", "入仓任务信息（未经二期筒仓转运）"),

    /**
     * 稻壳发放任务（一期对接）
     */
    RICE_HUSK_RELEASE("RICE_HUSK_RELEASE", "稻壳发放任务（一期对接）"),

    /**
     * 蒸糠生产任务（一期对接）
     */
    RICE_HUSK_STEAMING_PRODUCTION("RICE_HUSK_STEAMING_PRODUCTION", "蒸糠生产任务（一期对接）"),

    /**
     * 熟糠传输任务（一期对接）
     */
    RICE_HUSK_COOKED_BRAN_TRANSFER("RICE_HUSK_COOKED_BRAN_TRANSFER", "熟糠传输任务（一期对接）"),

    /**
     * 熟糠发放任务（一期对接）
     */
    RICE_HUSK_COOKED_BRAN_RELEASE("RICE_HUSK_COOKED_BRAN_RELEASE", "熟糠发放任务（一期对接）"),

    /**
     * 蒸糠机启停时间
     */
    RICE_HUSK_STEAMING_MACHINE_OPERATION("RICE_HUSK_STEAMING_MACHINE_OPERATION", "蒸糠机启停时间");

    /**
     * 任务类型代码
     */
    private final String code;

    /**
     * 任务类型描述
     */
    private final String description;

    TaskTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 任务类型代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TaskTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TaskTypeEnum taskType : values()) {
            if (taskType.getCode().equals(code)) {
                return taskType;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     *
     * @param description 任务类型描述
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TaskTypeEnum getByDescription(String description) {
        if (description == null) {
            return null;
        }
        for (TaskTypeEnum taskType : values()) {
            if (taskType.getDescription().equals(description)) {
                return taskType;
            }
        }
        return null;
    }

    /**
     * 检查是否为高粱相关任务
     *
     * @return true-是高粱相关任务，false-不是
     */
    public boolean isSorghumRelated() {
        return this.name().startsWith("SORGHUM_");
    }

    /**
     * 检查是否为稻壳相关任务
     *
     * @return true-是稻壳相关任务，false-不是
     */
    public boolean isRiceHuskRelated() {
        return this.name().startsWith("RICE_HUSK_");
    }

    /**
     * 检查是否为筒仓操作相关任务
     *
     * @return true-是筒仓操作相关任务，false-不是
     */
    public boolean isSiloOperationRelated() {
        return this.name().contains("SILO_") || this.name().contains("VENTILATION");
    }

    /**
     * 检查是否为一期对接任务
     *
     * @return true-是一期对接任务，false-不是
     */
    public boolean isPhase1InterfaceTask() {
        return this.name().contains("_PHASE1");
    }

    /**
     * 检查是否为二期转一期任务
     *
     * @return true-是二期转一期任务，false-不是
     */
    public boolean isPhase2ToPhase1Task() {
        return this.name().contains("PHASE2_TO_PHASE1");
    }

    /**
     * 检查是否为实时状态任务
     *
     * @return true-是实时状态任务，false-不是
     */
    public boolean isRealtimeStatusTask() {
        return this.name().contains("REALTIME_STATUS") || this.name().contains("MACHINE_OPERATION");
    }

    /**
     * 检查是否为快检相关任务
     *
     * @return true-是快检相关任务，false-不是
     */
    public boolean isQualityCheckTask() {
        return this.name().contains("QUALITY_CHECK");
    }

    /**
     * 检查是否为清仓相关任务
     *
     * @return true-是清仓相关任务，false-不是
     */
    public boolean isClearanceTask() {
        return this.name().contains("CLEARANCE");
    }

    /**
     * 检查是否为物料切换相关任务
     *
     * @return true-是物料切换相关任务，false-不是
     */
    public boolean isMaterialSwitchTask() {
        return this.name().contains("MATERIAL_SWITCH");
    }

    /**
     * 检查是否为转运相关任务
     *
     * @return true-是转运相关任务，false-不是
     */
    public boolean isTransferTask() {
        return this.name().contains("TRANSFER");
    }

    /**
     * 检查是否为通风相关任务
     *
     * @return true-是通风相关任务，false-不是
     */
    public boolean isVentilationTask() {
        return this.name().contains("VENTILATION");
    }

    /**
     * 检查是否为入仓相关任务
     *
     * @return true-是入仓相关任务，false-不是
     */
    public boolean isInboundTask() {
        return this.name().contains("INBOUND");
    }

    /**
     * 检查是否为蒸糠相关任务
     *
     * @return true-是蒸糠相关任务，false-不是
     */
    public boolean isSteamingTask() {
        return this.name().contains("STEAMING");
    }

    @Override
    public String toString() {
        return code + " - " + description;
    }
}
