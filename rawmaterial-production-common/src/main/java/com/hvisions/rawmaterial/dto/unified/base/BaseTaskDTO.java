package com.hvisions.rawmaterial.dto.unified.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 基础任务DTO - 包含所有任务共同的字段
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@ApiModel(description = "基础任务DTO")
public class BaseTaskDTO {

    // ==================== 核心必填字段 ====================
    
    @ApiModelProperty(value = "任务号", required = true)
    @NotNull(message = "任务号不能为空")
    private String taskNo;

    @ApiModelProperty(value = "业务系统，详见BusinessSystemEnum枚举：ZHONGLIANG-中粮，BULER-布勒，JIESAI-捷赛", required = true)
    @NotNull(message = "业务系统不能为空")
    private String businessSystem;

    @ApiModelProperty(value = "任务类型，详见TaskTypeEnum枚举。", required = true)
    @NotNull(message = "任务类型不能为空")
    private String taskType;

    @ApiModelProperty(value = "数据类型：TASK-任务，REALTIME-实时数据")
    private String dataType;

    // ==================== 物料信息 ====================
    
    @ApiModelProperty(value = "物料类型：SORGHUM-高粱，RICE_HUSK-稻壳")
    private String materialType;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "更换前物料类型")
    private String beforeMaterialType;

    @ApiModelProperty(value = "更换后物料类型")
    private String afterMaterialType;

    // ==================== 任务状态和时间 ====================
    
    @ApiModelProperty(value = "任务状态 待处理-PENDING，处理中-PROCESSING，已完成-COMPLETED，失败-FAILED，已取消-CANCELLED，超时-TIMEOUT, 进仓中-INBOUNDING，出仓中-OUTBOUNDING，未启动-NOT_STARTED")
    private String status;

    @ApiModelProperty(value = "任务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "任务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    // ==================== 筒仓信息 ====================
    
    @ApiModelProperty(value = "筒仓号（单筒仓操作时使用）")
    private String siloNo;

    @ApiModelProperty(value = "筒仓名称（单筒仓操作时使用）")
    private String siloName;

    @ApiModelProperty(value = "发出筒仓号")
    private String sourceSiloNo;

    @ApiModelProperty(value = "发出筒仓名称")
    private String sourceSiloName;

    @ApiModelProperty(value = "接收筒仓号")
    private String targetSiloNo;

    @ApiModelProperty(value = "接收筒仓名称")
    private String targetSiloName;

    // ==================== 重量信息 ====================
    
    @ApiModelProperty(value = "实际重量/收货重量(KG)")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "计划重量(KG)")
    private BigDecimal plannedWeight;

    @ApiModelProperty(value = "发出重量(KG)")
    private BigDecimal sourceWeight;

    @ApiModelProperty(value = "接收重量(KG)")
    private BigDecimal targetWeight;

    @ApiModelProperty(value = "库存重量(KG)")
    private BigDecimal inventoryWeight;

    @ApiModelProperty(value = "清仓前重量(KG)")
    private BigDecimal beforeClearanceWeight;

    //plannedReleaseWeight
    @ApiModelProperty(value = "计划发放重量(KG)")
    private BigDecimal plannedReleaseWeight;

    // ==================== 设备和位置信息 ====================
    
    @ApiModelProperty(value = "设备编号")
    private String equipmentNo;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "中心")
    private String center;

    @ApiModelProperty(value = "路线")
    private String route;

    @ApiModelProperty(value = "通风时间（小时）")
    private Integer ventilationDuration;

    // ==================== 扩展信息 ====================

    //qualityCheckLocation
    @ApiModelProperty(value = "快检地点：IN-进，OUT-出")
    private String qualityCheckLocation;

    //batchProgress
    @ApiModelProperty(value = "批次发放进度")
    private String batchProgress;

    @ApiModelProperty(value = "唯一标识用于去重（建议传递，可为空）")
    private String uniqueId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    //淀粉含量
    @ApiModelProperty(value = "淀粉含量")
    private BigDecimal starchContent;

    //水分含量
    @ApiModelProperty(value = "水分含量")
    private BigDecimal moistureContent;

    //含糖率
    @ApiModelProperty(value = "蛋白质含量")
    private BigDecimal proteinContent;
}
