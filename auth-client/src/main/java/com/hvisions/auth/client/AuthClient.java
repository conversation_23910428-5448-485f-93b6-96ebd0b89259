package com.hvisions.auth.client;

import com.hvisions.auth.dto.user.UserDTO;
import com.hvisions.auth.dto.user.UserInfoDto;
import com.hvisions.common.vo.ResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: AuthClient</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "auth", fallbackFactory = AuthFallBackClient.class)
public interface AuthClient {


    /**
     * 验证用户账户密码
     *
     * @param account  登录账号
     * @param password 登录密码
     * @return 用户ID
     */
    @GetMapping("/baseUser/validateUser")
    ResultVO<UserDTO> validateUser(@RequestParam("account") String account, @RequestParam("password") String password);


    /**
     * 根据用户id列表，获取用户简单信息
     *
     * @param idList 用户id列表
     * @return 用户信息
     */
    @PostMapping(value = "/baseUser/getUserInfoByIds")
    ResultVO<List<UserInfoDto>> getUserInfoByIds(@RequestBody List<Integer> idList);

    /**
     * 所有所有用户基础信息
     *
     * @return 用户基础信息列表
     */
    @GetMapping(value = "/baseUser/getAllUserInfo")
    ResultVO<List<UserInfoDto>> getAllUserInfo();

    /**
     * 根据部门id查询用户数据
     *
     * @param departmentId 查询对象
     * @return 用户数据列表
     */
    @GetMapping(value = "/user/getUserListByDepartmentId/{departmentId}")
    ResultVO<List<UserDTO>> getUserListByDepartmentId(@PathVariable("departmentId") int departmentId);

}
